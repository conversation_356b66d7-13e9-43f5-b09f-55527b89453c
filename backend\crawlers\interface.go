package crawlers

import (
	"context"
	"time"
)

// 漏洞信息结构
type VulnInfo struct {
	UniqueKey   string   // 唯一标识，如CVE编号或其他ID
	Title       string   // 漏洞名称
	Description string   // 漏洞描述
	Severity    string   // 危害等级
	CVE         string   // CVE编号
	CWE         string   // CWE编号，可能有多个，用逗号分隔
	VulnType    string   // 漏洞类型，可能有多个，用逗号分隔
	Score       string   // CVSS评分
	Disclosure  string   // 披露日期
	References  []string // 参考链接
	From        string   // 来源URL
	Tags        []string // 标签
	Remediation string   // 修复建议
}

// 采集源信息
type Provider struct {
	Name        string // 采集源名称
	DisplayName string // 显示名称
	Link        string // 采集源链接
}

// 检查漏洞是否存在的函数类型
type CheckVulnExistsFunc func(vulnID string) bool

// 采集器接口
type Grabber interface {
	// 获取采集源信息
	ProviderInfo() *Provider

	// 获取最新漏洞信息
	// itemLimit: 限制采集的数据条数
	// startDate: 开始日期，格式为YYYY-MM-DD，为空表示不限制
	// endDate: 结束日期，格式为YYYY-MM-DD，为空表示不限制
	GetUpdate(ctx context.Context, itemLimit int, startDate string, endDate string) ([]*VulnInfo, error)

	// 获取最新漏洞信息，支持检查漏洞是否已存在
	// itemLimit: 限制采集的数据条数
	// startDate: 开始日期，格式为YYYY-MM-DD，为空表示不限制
	// endDate: 结束日期，格式为YYYY-MM-DD，为空表示不限制
	// checkExists: 检查漏洞是否已存在的函数，如果为nil则不检查
	GetUpdateWithCheck(ctx context.Context, itemLimit int, startDate string, endDate string, checkExists CheckVulnExistsFunc) ([]*VulnInfo, error)

	// 判断漏洞是否值得关注
	IsValuable(info *VulnInfo) bool
}

// 转换漏洞严重程度
const (
	SeverityLow      = "低危"
	SeverityMedium   = "中危"
	SeverityHigh     = "高危"
	SeverityCritical = "严重"
)

// 获取当前时间的格式化字符串
func GetCurrentDate() string {
	return time.Now().Format("2006-01-02")
}
