<template>
  <div class="ioc-intelligence-push-management">
    <div class="header">
      <h2>IOC情报推送管理</h2>
      <p class="module-description">情报推送模块 - 管理IOC情报的推送，支持单个和批量推送，推送记录管理</p>
      <div class="actions">
        <el-button type="primary" @click="showBatchPushDialog = true">
          <el-icon><Promotion /></el-icon>
          批量推送
        </el-button>
        <el-button @click="refreshPushRecords">
          <el-icon><Refresh /></el-icon>
          刷新记录
        </el-button>
      </div>
    </div>

    <!-- 推送记录搜索栏 -->
    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="IOC值">
          <el-input v-model="searchForm.ioc" placeholder="请输入IOC值" clearable />
        </el-form-item>
        <el-form-item label="推送状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>
        <el-form-item label="推送通道">
          <el-select v-model="searchForm.channel_type" placeholder="请选择通道类型" clearable>
            <el-option label="微信机器人" value="wechat_bot" />
            <el-option label="钉钉机器人" value="dingding" />
            <el-option label="Webhook" value="webhook" />
            <el-option label="飞书机器人" value="lark" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchPushRecords">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 推送记录表格 -->
    <div class="table-container">
      <el-table
        :data="pushRecords"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="iocIntelligence.ioc" label="IOC值" width="150" />
        <el-table-column prop="iocIntelligence.iocType" label="IOC类型" width="100" />
        <el-table-column prop="iocIntelligence.riskLevel" label="风险等级" width="100">
          <template #default="scope">
            <el-tag :type="getRiskLevelType(scope.row.iocIntelligence?.riskLevel)">
              {{ scope.row.iocIntelligence?.riskLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="channelName" label="推送通道" width="150" />
        <el-table-column prop="channelType" label="通道类型" width="120" />
        <el-table-column prop="status" label="推送状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
              {{ scope.row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="errorMessage" label="错误信息" width="200" show-overflow-tooltip />
        <el-table-column prop="pushedAt" label="推送时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.pushedAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="retryPush(scope.row)"
              :disabled="scope.row.status === 'success'"
            >
              重新推送
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deletePushRecord(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 批量推送对话框 -->
    <el-dialog
      v-model="showBatchPushDialog"
      title="批量推送IOC情报"
      width="600px"
      :before-close="handleCloseBatchPushDialog"
    >
      <el-form :model="batchPushForm" label-width="120px">
        <el-form-item label="推送方式">
          <el-radio-group v-model="batchPushForm.pushType">
            <el-radio label="channel">指定通道</el-radio>
            <el-radio label="policy">指定策略</el-radio>
            <el-radio label="default">默认推送</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="推送通道" v-if="batchPushForm.pushType === 'channel'">
          <el-select v-model="batchPushForm.channelId" placeholder="请选择推送通道">
            <el-option
              v-for="channel in pushChannels"
              :key="channel.id"
              :label="channel.name"
              :value="channel.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="推送策略" v-if="batchPushForm.pushType === 'policy'">
          <el-select v-model="batchPushForm.policyId" placeholder="请选择推送策略">
            <el-option
              v-for="policy in pushPolicies"
              :key="policy.id"
              :label="policy.name"
              :value="policy.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="IOC情报">
          <el-select
            v-model="batchPushForm.iocIds"
            multiple
            placeholder="请选择要推送的IOC情报"
            style="width: 100%"
          >
            <el-option
              v-for="ioc in availableIOCs"
              :key="ioc.id"
              :label="`${ioc.ioc} (${ioc.riskLevel})`"
              :value="ioc.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showBatchPushDialog = false">取消</el-button>
          <el-button type="primary" @click="executeBatchPush" :loading="batchPushLoading">
            确认推送
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Promotion } from '@element-plus/icons-vue'
import api from './api'

// 响应式数据
const loading = ref(false)
const pushRecords = ref([])
const selectedRows = ref([])
const showBatchPushDialog = ref(false)
const batchPushLoading = ref(false)
const pushChannels = ref([])
const pushPolicies = ref([])
const availableIOCs = ref([])

// 搜索表单
const searchForm = reactive({
  ioc: '',
  status: '',
  channel_type: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 批量推送表单
const batchPushForm = reactive({
  pushType: 'default',
  channelId: null,
  policyId: null,
  iocIds: []
})

// 获取推送记录列表
const fetchPushRecords = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      ...searchForm
    }
    const response = await api.get('/ioc/intelligence-push/records', { params })
    pushRecords.value = response.data.data
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取推送记录失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 获取推送通道列表
const fetchPushChannels = async () => {
  try {
    const response = await api.get('/push/channels')
    pushChannels.value = response.data.data || []
  } catch (error) {
    console.error('获取推送通道失败:', error)
  }
}

// 获取推送策略列表
const fetchPushPolicies = async () => {
  try {
    const response = await api.get('/push/policies')
    pushPolicies.value = response.data.data || []
  } catch (error) {
    console.error('获取推送策略失败:', error)
  }
}

// 获取可用的IOC情报列表
const fetchAvailableIOCs = async () => {
  try {
    const response = await api.get('/ioc/intelligence-production/intelligence', {
      params: { page: 1, page_size: 100 }
    })
    availableIOCs.value = response.data.data || []
  } catch (error) {
    console.error('获取IOC情报列表失败:', error)
  }
}

// 工具函数
const getRiskLevelType = (riskLevel) => {
  const typeMap = {
    '严重': 'danger',
    '高危': 'warning',
    '中危': 'primary',
    '低危': 'info',
    '信息': 'success'
  }
  return typeMap[riskLevel] || 'info'
}

const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp * 1000).toLocaleString()
}

// 事件处理
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchPushRecords()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchPushRecords()
}

const searchPushRecords = () => {
  pagination.page = 1
  fetchPushRecords()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    ioc: '',
    status: '',
    channel_type: ''
  })
  searchPushRecords()
}

const refreshPushRecords = () => {
  fetchPushRecords()
}

// 重新推送
const retryPush = async (record) => {
  try {
    await api.post(`/ioc/intelligence-push/single/${record.iocIntelligenceId}`)
    ElMessage.success('重新推送成功')
    fetchPushRecords()
  } catch (error) {
    ElMessage.error('重新推送失败: ' + error.message)
  }
}

// 删除推送记录
const deletePushRecord = async (record) => {
  try {
    await ElMessageBox.confirm('确认删除此推送记录？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await api.delete(`/ioc/intelligence-push/records/${record.id}`)
    ElMessage.success('删除成功')
    fetchPushRecords()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

// 批量推送相关
const handleCloseBatchPushDialog = () => {
  showBatchPushDialog.value = false
  Object.assign(batchPushForm, {
    pushType: 'default',
    channelId: null,
    policyId: null,
    iocIds: []
  })
}

const executeBatchPush = async () => {
  if (batchPushForm.iocIds.length === 0) {
    ElMessage.warning('请选择要推送的IOC情报')
    return
  }

  batchPushLoading.value = true
  try {
    const data = {
      ioc_ids: batchPushForm.iocIds
    }

    if (batchPushForm.pushType === 'channel') {
      data.channel_id = batchPushForm.channelId
    } else if (batchPushForm.pushType === 'policy') {
      data.policy_id = batchPushForm.policyId
    }

    await api.batchPushIOCIntelligence(data)
    ElMessage.success('批量推送成功')
    showBatchPushDialog.value = false
    fetchPushRecords()
  } catch (error) {
    ElMessage.error('批量推送失败: ' + error.message)
  } finally {
    batchPushLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchPushRecords()
  fetchPushChannels()
  fetchPushPolicies()
  fetchAvailableIOCs()
})
</script>

<style scoped>
.ioc-intelligence-push-management {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}

.header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.module-description {
  margin: 0 0 16px 0;
  color: #909399;
  font-size: 14px;
}

.actions {
  display: flex;
  gap: 10px;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}

.search-form {
  margin: 0;
}

.table-container {
  background: white;
  border-radius: 4px;
  overflow: hidden;
}

.pagination-container {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
