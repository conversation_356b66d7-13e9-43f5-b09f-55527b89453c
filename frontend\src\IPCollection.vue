<template>
  <div class="ip-collection-container">
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="生成源">
          <el-select
            v-model="filterForm.source"
            placeholder="选择生成源"
            clearable
            filterable
          >
            <el-option label="威胁情报平台" value="threatbook" />
            <el-option label="奇安信" value="qianxin" />
            <el-option label="阿里云" value="aliyun" />
            <el-option label="微步在线" value="threatbook" />
            <el-option label="绿盟" value="nsfocus" />
            <el-option label="启明星辰" value="venustech" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select 
            v-model="filterForm.status" 
            placeholder="全部" 
            clearable
          >
            <el-option label="运行中" value="running" />
            <el-option label="已停止" value="stopped" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>
        <el-form-item label="采集时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon> 搜索
          </el-button>
          <el-button @click="resetFilter">
            <el-icon><RefreshLeft /></el-icon> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="hover" class="table-card">
      <div class="table-header">
        <div class="table-title">情报生成任务</div>
        <div class="table-actions">
          <el-button type="primary" @click="refreshList">
            <el-icon><Refresh /></el-icon> 刷新
          </el-button>
          <el-button type="success" v-if="isAdmin" @click="handleCreate">
            <el-icon><Plus /></el-icon> 新建生成
          </el-button>
          <el-button 
            type="danger" 
            v-if="isAdmin" 
            @click="handleBatchDelete"
            :disabled="multipleSelection.length === 0"
          >
            <el-icon><Delete /></el-icon> 批量删除
          </el-button>
        </div>
      </div>

      <el-table
        :data="ipCollections"
        border
        style="width: 100%"
        v-loading="loading"
        row-key="id"
        stripe
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          textAlign: 'center',
          fontWeight: '500',
          writingMode: 'horizontal-tb',
          whiteSpace: 'nowrap'
        }"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="任务名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="source" label="生成源" width="150">
          <template #default="scope">
            <el-tag size="small" effect="plain">{{ scope.row.source }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              effect="dark"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="collectedCount" label="生成数量" width="120" sortable />
        <el-table-column prop="lastRunTime" label="最近运行时间" width="180" sortable>
          <template #default="scope">
            {{ formatDate(scope.row.lastRunTime, 'YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="createdBy" label="创建人" width="120" show-overflow-tooltip />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <div class="operation-buttons">
              <el-tooltip content="查看详情" placement="top">
                <el-button size="small" type="primary" circle @click="handleDetail(scope.row)">
                  <el-icon><InfoFilled /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip v-if="isAdmin" content="编辑" placement="top">
                <el-button size="small" type="warning" circle @click="handleEdit(scope.row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip v-if="isAdmin && scope.row.status !== 'running'" content="运行" placement="top">
                <el-button size="small" type="success" circle @click="handleRun(scope.row)">
                  <el-icon><VideoPlay /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip v-if="isAdmin && scope.row.status === 'running'" content="停止" placement="top">
                <el-button size="small" type="danger" circle @click="handleStop(scope.row)">
                  <el-icon><VideoPause /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip v-if="isAdmin" content="删除" placement="top">
                <el-button size="small" type="danger" circle @click="handleDelete(scope.row)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>

    <!-- 创建/编辑采集任务对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑生成任务' : '新建生成任务'"
      width="50%"
      destroy-on-close
    >
      <el-form :model="collectionForm" :rules="formRules" ref="collectionFormRef" label-width="100px">
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="collectionForm.name" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="生成源" prop="source">
          <el-select v-model="collectionForm.source" placeholder="请选择生成源" style="width: 100%">
            <el-option label="威胁情报平台" value="threatbook" />
            <el-option label="奇安信" value="qianxin" />
            <el-option label="阿里云" value="aliyun" />
            <el-option label="微步在线" value="threatbook" />
            <el-option label="绿盟" value="nsfocus" />
            <el-option label="启明星辰" value="venustech" />
          </el-select>
        </el-form-item>
        <el-form-item label="API密钥" prop="apiKey">
          <el-input v-model="collectionForm.apiKey" placeholder="请输入API密钥" show-password />
        </el-form-item>
        <el-form-item label="生成周期" prop="schedule">
          <el-select v-model="collectionForm.schedule" placeholder="请选择生成周期" style="width: 100%">
            <el-option label="每小时" value="hourly" />
            <el-option label="每天" value="daily" />
            <el-option label="每周" value="weekly" />
            <el-option label="每月" value="monthly" />
            <el-option label="手动触发" value="manual" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="collectionForm.description" type="textarea" rows="3" placeholder="请输入任务描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 采集任务详情抽屉 -->
    <el-drawer
      v-model="detailDrawerVisible"
      title="采集任务详情"
      size="60%"
      direction="rtl"
      destroy-on-close
    >
      <div v-if="currentCollection" class="collection-detail">
        <div class="custom-descriptions">
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">任务名称</div>
            <div class="custom-descriptions-content">{{ currentCollection.name }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">采集源</div>
            <div class="custom-descriptions-content">
              <el-tag size="small" effect="plain">{{ currentCollection.source }}</el-tag>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">状态</div>
            <div class="custom-descriptions-content">
              <el-tag :type="getStatusType(currentCollection.status)" effect="dark">
                {{ getStatusText(currentCollection.status) }}
              </el-tag>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">生成数量</div>
            <div class="custom-descriptions-content">{{ currentCollection.collectedCount }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">生成周期</div>
            <div class="custom-descriptions-content">{{ getScheduleText(currentCollection.schedule) }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">最近运行</div>
            <div class="custom-descriptions-content">
              {{ formatDate(currentCollection.lastRunTime, 'YYYY-MM-DD HH:mm:ss') }}
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">创建人</div>
            <div class="custom-descriptions-content">{{ currentCollection.createdBy }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">创建时间</div>
            <div class="custom-descriptions-content">
              {{ formatDate(currentCollection.createdAt, 'YYYY-MM-DD HH:mm:ss') }}
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">描述</div>
            <div class="custom-descriptions-content">{{ currentCollection.description || '无' }}</div>
          </div>
        </div>

        <div class="collection-logs">
          <div class="section-title">运行日志</div>
          <el-timeline>
            <el-timeline-item
              v-for="(log, index) in currentCollection.logs"
              :key="index"
              :timestamp="formatDate(log.timestamp, 'YYYY-MM-DD HH:mm:ss')"
              :type="getLogType(log.type)"
            >
              {{ log.message }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, RefreshLeft, Refresh, Plus, Delete, Edit, InfoFilled, 
  VideoPlay, VideoPause 
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 模拟数据，实际开发中应从API获取
const ipCollections = ref([
  {
    id: 1,
    name: '威胁情报平台IP生成',
    source: '威胁情报平台',
    status: 'running',
    collectedCount: 1256,
    lastRunTime: '2023-06-15 10:30:25',
    createdAt: '2023-05-01 14:30:25',
    createdBy: 'admin',
    schedule: 'daily',
    description: '从威胁情报平台生成恶意IP数据',
    logs: [
      { timestamp: '2023-06-15 10:30:25', type: 'info', message: '开始生成任务' },
      { timestamp: '2023-06-15 10:35:12', type: 'success', message: '成功生成56条新IP情报' },
      { timestamp: '2023-06-15 10:40:05', type: 'info', message: '任务继续执行中' }
    ]
  },
  {
    id: 2,
    name: '奇安信情报生成',
    source: '奇安信',
    status: 'completed',
    collectedCount: 876,
    lastRunTime: '2023-06-14 22:15:42',
    createdAt: '2023-05-10 09:15:42',
    createdBy: 'admin',
    schedule: 'weekly',
    description: '从奇安信平台生成高危IP数据',
    logs: [
      { timestamp: '2023-06-14 22:00:00', type: 'info', message: '开始生成任务' },
      { timestamp: '2023-06-14 22:10:25', type: 'warning', message: '部分API请求超时，已重试' },
      { timestamp: '2023-06-14 22:15:42', type: 'success', message: '生成完成，共获取到28条新IP情报' }
    ]
  },
  {
    id: 3,
    name: '阿里云情报生成',
    source: '阿里云',
    status: 'failed',
    collectedCount: 0,
    lastRunTime: '2023-06-13 18:22:10',
    createdAt: '2023-05-20 18:22:10',
    createdBy: 'operator',
    schedule: 'manual',
    description: '从阿里云安全中心采集恶意IP数据',
    logs: [
      { timestamp: '2023-06-13 18:20:00', type: 'info', message: '开始采集任务' },
      { timestamp: '2023-06-13 18:21:30', type: 'warning', message: 'API认证失败' },
      { timestamp: '2023-06-13 18:22:10', type: 'error', message: '采集任务失败，请检查API密钥是否有效' }
    ]
  }
])

const router = useRouter()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)
const multipleSelection = ref([])
const detailDrawerVisible = ref(false)
const currentCollection = ref(null)
const dateRange = ref(null)
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)

const filterForm = ref({
  source: '',
  status: '',
  startDate: '',
  endDate: ''
})

const collectionForm = ref({
  name: '',
  source: '',
  apiKey: '',
  schedule: 'daily',
  description: ''
})

const formRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  source: [
    { required: true, message: '请选择采集源', trigger: 'change' }
  ],
  apiKey: [
    { required: true, message: '请输入API密钥', trigger: 'blur' }
  ],
  schedule: [
    { required: true, message: '请选择采集周期', trigger: 'change' }
  ]
}

const collectionFormRef = ref(null)

const isAdmin = computed(() => {
  // 从localStorage获取用户信息或其他方式判断是否为管理员
  return true // 示例，实际应根据用户权限判断
})

// 处理日期范围变化
const handleDateRangeChange = (val) => {
  if (val) {
    filterForm.value.startDate = val[0]
    filterForm.value.endDate = val[1]
  } else {
    filterForm.value.startDate = ''
    filterForm.value.endDate = ''
  }
}

// 根据状态获取对应的标签类型
const getStatusType = (status) => {
  const map = {
    'running': 'primary',
    'completed': 'success',
    'stopped': 'info',
    'failed': 'danger'
  }
  return map[status] || 'info'
}

// 根据状态获取对应的文本
const getStatusText = (status) => {
  const map = {
    'running': '运行中',
    'completed': '已完成',
    'stopped': '已停止',
    'failed': '失败'
  }
  return map[status] || status
}

// 根据日志类型获取对应的类型
const getLogType = (type) => {
  const map = {
    'info': 'primary',
    'success': 'success',
    'warning': 'warning',
    'error': 'danger'
  }
  return map[type] || 'info'
}

// 获取采集周期文本
const getScheduleText = (schedule) => {
  const map = {
    'hourly': '每小时',
    'daily': '每天',
    'weekly': '每周',
    'monthly': '每月',
    'manual': '手动触发'
  }
  return map[schedule] || schedule
}

// 格式化日期
const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return '-'
  return dayjs(date).format(format)
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchIPCollections()
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.value = {
    source: '',
    status: '',
    startDate: '',
    endDate: ''
  }
  dateRange.value = null
  handleSearch()
}

// 刷新列表
const refreshList = () => {
  fetchIPCollections()
}

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

// 处理表格排序变化
const handleSortChange = (column) => {
  // 实现排序逻辑
  fetchIPCollections()
}

// 处理每页显示数量变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchIPCollections()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchIPCollections()
}

// 查看详情
const handleDetail = (row) => {
  currentCollection.value = row
  detailDrawerVisible.value = true
}

// 处理创建
const handleCreate = () => {
  isEdit.value = false
  collectionForm.value = {
    name: '',
    source: '',
    apiKey: '',
    schedule: 'daily',
    description: ''
  }
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (row) => {
  isEdit.value = true
  collectionForm.value = {
    id: row.id,
    name: row.name,
    source: row.source,
    apiKey: '********', // 出于安全考虑，不显示实际API密钥
    schedule: row.schedule,
    description: row.description
  }
  dialogVisible.value = true
}

// 提交表单
const submitForm = () => {
  if (!collectionFormRef.value) return
  
  collectionFormRef.value.validate((valid) => {
    if (valid) {
      submitting.value = true
      
      // 模拟API调用
      setTimeout(() => {
        submitting.value = false
        dialogVisible.value = false
        
        if (isEdit.value) {
          ElMessage.success('编辑成功')
        } else {
          ElMessage.success('创建成功')
        }
        
        // 刷新列表
        fetchIPCollections()
      }, 1000)
    }
  })
}

// 处理运行
const handleRun = (row) => {
  ElMessageBox.confirm(
    `确定要运行"${row.name}"采集任务吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  )
    .then(() => {
      // 实现运行逻辑
      ElMessage.success('任务已开始运行')
      // 更新状态
      row.status = 'running'
    })
    .catch(() => {
      // 用户取消
    })
}

// 处理停止
const handleStop = (row) => {
  ElMessageBox.confirm(
    `确定要停止"${row.name}"采集任务吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(() => {
      // 实现停止逻辑
      ElMessage.success('任务已停止')
      // 更新状态
      row.status = 'stopped'
    })
    .catch(() => {
      // 用户取消
    })
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除"${row.name}"采集任务吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(() => {
      // 实现删除逻辑
      ElMessage.success('删除成功')
      // 从列表中移除
      ipCollections.value = ipCollections.value.filter(item => item.id !== row.id)
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 批量删除
const handleBatchDelete = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${multipleSelection.value.length} 个采集任务吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(() => {
      // 实现批量删除逻辑
      ElMessage.success('批量删除成功')
      // 从列表中移除
      const ids = multipleSelection.value.map(item => item.id)
      ipCollections.value = ipCollections.value.filter(item => !ids.includes(item.id))
      multipleSelection.value = []
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 获取IP采集数据
const fetchIPCollections = () => {
  loading.value = true
  
  // 这里应该是API调用，现在使用setTimeout模拟异步操作
  setTimeout(() => {
    // 实际开发中，这里应该是API调用返回的数据
    // ipCollections.value = response.data.items
    // total.value = response.data.total
    
    loading.value = false
  }, 500)
}

onMounted(() => {
  fetchIPCollections()
})
</script>

<style scoped>
.ip-collection-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 18px;
  font-weight: bold;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.operation-buttons {
  display: flex;
  justify-content: space-around;
}

.custom-descriptions {
  padding: 20px;
}

.custom-descriptions-row {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.custom-descriptions-label {
  font-weight: bold;
  width: 100px;
  color: #606266;
}

.custom-descriptions-content {
  flex: 1;
}

.collection-logs {
  padding: 0 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}
</style> 