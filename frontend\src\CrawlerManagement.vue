<template>
  <div class="crawler-management">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>漏洞采集源管理</span>
        </div>
      </template>
      
      <el-table :data="crawlers" style="width: 100%" v-loading="loading" border stripe size="small">
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="type" label="类型" width="220" align="center">
          <template #default="scope">
            <el-tag 
              :type="getProviderTagType(scope.row.type)" 
              effect="plain"
              class="provider-tag"
            >
              {{ getProviderDisplayName(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="160" show-overflow-tooltip>
          <template #default="scope">
            <div class="description-cell">{{ scope.row.description || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="schedule" label="采集周期" width="100" align="center">
          <template #default="scope">
            <el-tag type="warning" effect="plain" size="small">
              {{ getScheduleText(scope.row.interval || scope.row.schedule, 
                typeof scope.row.config === 'string' ? JSON.parse(scope.row.config) : scope.row.config) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="90" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status ? 'success' : 'danger'" effect="dark" size="small">
              {{ scope.row.status ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastRunAt" label="上次运行时间" width="170" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.lastRunAt" type="info" effect="plain" size="small">
              {{ formatTimestamp(scope.row.lastRunAt) }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="170" align="center">
          <template #default="scope">
            <div class="operation-buttons">
              <el-tooltip content="编辑" placement="top">
                <el-button size="small" type="primary" plain @click="handleEdit(scope.row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="立即采集" placement="top">
                <el-button 
                  size="small" 
                  type="success" 
                  plain
                  circle
                  :disabled="!scope.row.status"
                  @click="handleRun(scope.row)"
                >
                  <el-icon><Download /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="查看日志" placement="top">
                <el-button 
                  size="small" 
                  type="info" 
                  plain
                  circle
                  @click="showLogs(scope.row.id)"
                >
                  <el-icon><Document /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 采集日志对话框 -->
    <el-dialog v-model="logsDialogVisible" title="采集日志" width="80%">
      <div class="dialog-header-actions" style="margin-bottom: 15px; text-align: right;">
        <el-button type="danger" @click="confirmClearLogs" :loading="clearLogsLoading">
          清除日志
        </el-button>
      </div>

      <el-table :data="crawlerLogs" style="width: 100%" v-loading="logsLoading" border stripe>
        <el-table-column prop="id" label="ID" width="60" align="center" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getLogStatusType(scope.row.status)">
              {{ getLogStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="采集数量" width="100" align="center" />
        <el-table-column prop="newCount" label="新增数量" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.newCount > 0" type="success" effect="plain">
              {{ scope.row.newCount }}
            </el-tag>
            <span v-else>{{ scope.row.newCount || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="消息" show-overflow-tooltip />
        <el-table-column prop="startTime" label="开始时间" width="180" align="center">
          <template #default="scope">
            <span>{{ formatTimestamp(scope.row.startTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="endTime" label="结束时间" width="180" align="center">
          <template #default="scope">
            <span v-if="scope.row.endTime">{{ formatTimestamp(scope.row.endTime) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container" style="margin-top: 15px; text-align: right;">
        <el-pagination
          v-model:current-page="logsCurrentPage"
          v-model:page-size="logsPageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="logsTotal"
          @size-change="handleLogsSizeChange"
          @current-change="handleLogsCurrentChange"
        />
      </div>
    </el-dialog>
    
    <!-- 确认清除日志对话框 -->
    <el-dialog
      v-model="clearLogsDialogVisible"
      title="确认清除日志"
      width="30%"
    >
      <span>确定要清除{{ currentCrawlerId ? '该采集器的' : '所有' }}采集日志吗？此操作不可恢复！</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="clearLogsDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="doClearLogs" :loading="clearLogsLoading">
            确认清除
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 确认采集对话框 -->
    <el-dialog
      v-model="runDialogVisible"
      title="确认采集"
      width="30%"
    >
      <span>确定要立即执行采集任务吗？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="runDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="doRunCrawler" :loading="runLoading">
            确认采集
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 创建/编辑采集源对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      title="编辑采集源"
      width="50%"
    >
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="120px"
      >
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择采集源类型" style="width: 100%" disabled>
            <el-option 
              v-for="provider in providers" 
              :key="provider.name" 
              :label="provider.displayName" 
              :value="provider.name"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            rows="3" 
            placeholder="请输入采集源描述" 
          />
        </el-form-item>
        
        <!-- 通用配置选项 -->
        <template v-if="form.type">
          <el-form-item label="采集页数" prop="pageLimit">
            <el-input-number v-model="form.pageLimit" :min="1" :max="10" />
          </el-form-item>
          
          <el-form-item label="漏洞披露日期" prop="disclosureDateRange">
            <el-date-picker
              v-model="form.disclosureDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
            <div class="form-tip">仅采集指定日期范围内披露的漏洞，留空表示不限制</div>
          </el-form-item>
        </template>
        
        <el-form-item label="采集周期" prop="schedule">
          <el-select v-model="form.schedule" placeholder="请选择采集周期" style="width: 100%">
            <el-option 
              v-for="option in scheduleOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="自定义间隔" prop="customInterval" v-if="form.schedule === 'custom'">
          <el-input-number v-model="form.customInterval" :min="1" :max="1440" />
          <span class="form-tip">分钟</span>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="form.status"
            active-text="启用"
            inactive-text="禁用"
          />
          <span class="form-tip">启用后将按照设定的采集周期自动执行采集任务</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Edit, Download, Document, Delete } from '@element-plus/icons-vue'
import api from './api'
import type { Crawler, CrawlerLog } from './api'

// 数据
const crawlers = ref<Crawler[]>([])
const crawlerLogs = ref<CrawlerLog[]>([])
const loading = ref(false)
const logsLoading = ref(false)
const dialogVisible = ref(false)
const logsDialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const providers = ref<{name: string, displayName: string}[]>([]) // 采集器提供者列表

// 采集周期选项
const scheduleOptions = ref([
  { value: 'custom', label: '自定义间隔' },
  { value: 'hourly', label: '每小时' },
  { value: 'daily', label: '每天' },
  { value: 'weekly', label: '每周' },
  { value: 'monthly', label: '每月' },
  { value: 'manual', label: '手动' }
])

// 表单
const formRef = ref<FormInstance>()
const form = reactive({
  id: 0,
  type: 'chaitin', // 默认选择长亭漏洞库
  description: '',
  pageLimit: 1,
  disclosureDateRange: null as [string, string] | null,
  schedule: 'manual', // 默认手动执行（符合V1版本标准）
  customInterval: 60, // 自定义间隔，默认60分钟
  status: true
})

// 表单验证规则
const rules = reactive<FormRules>({
  type: [
    { required: true, message: '请选择采集源类型', trigger: 'change' }
  ],
  pageLimit: [
    { required: true, message: '请设置采集页数', trigger: 'blur' }
  ],
  schedule: [
    { required: true, message: '请选择采集周期', trigger: 'change' }
  ]
})

// 日志分页相关
const logsCurrentPage = ref(1)
const logsPageSize = ref(10)
const logsTotal = ref(0)
const currentCrawlerId = ref<number | null>(null)

// 清除日志相关
const clearLogsDialogVisible = ref(false)
const clearLogsLoading = ref(false)

// 确认采集相关
const runDialogVisible = ref(false)
const runLoading = ref(false)
const currentRunCrawler = ref<Crawler | null>(null)

// 获取采集源列表
const fetchCrawlers = async () => {
  loading.value = true
  try {
    const res = await api.getCrawlers()
    crawlers.value = res.data.data
    
    // 检查是否有需要显示详情的采集器
    const selectedId = localStorage.getItem('selectedCrawlerId')
    if (selectedId) {
      // 清除localStorage中的值，避免下次访问页面仍然显示
      localStorage.removeItem('selectedCrawlerId')
      
      // 找到对应的采集器
      const crawler = crawlers.value.find(c => c.id === parseInt(selectedId) || c.id === selectedId)
      if (crawler) {
        // 显示该采集器的日志
        await showLogs(crawler.id)
      }
    }
  } catch (error) {
    console.error('获取采集源列表失败:', error)
    ElMessage.error('获取采集源列表失败')
  } finally {
    loading.value = false
  }
}

// 获取可用的采集器类型
const fetchCrawlerProviders = async () => {
  try {
    console.log('开始获取采集器类型列表')
    const res = await api.getCrawlerProviders()
    console.log('API响应:', res)
    
    // 将API返回的数据转换为前端需要的格式
    if (res.data && res.data.data && Array.isArray(res.data.data)) {
      console.log('API返回的数据:', res.data.data)
      providers.value = res.data.data.map((item: any) => {
        // 确保所有字段都有值，避免undefined错误
        return {
          name: (item.Name || '').toLowerCase(), // 确保名称是小写的
          displayName: item.DisplayName || '',
          link: item.Link || ''
        }
      })
    } else {
      console.log('API返回数据格式不正确或为空，使用默认值')
      setDefaultProviders()
    }
    
    // 如果API没有返回数据或数据不完整，提供默认值
    if (providers.value.length === 0) {
      setDefaultProviders()
    }
    
    console.log('处理后的采集器类型列表:', providers.value)
  } catch (error) {
    console.error('获取采集器类型列表失败:', error)
    // 不显示错误提示，避免用户困惑
    // ElMessage.error('获取采集器类型列表失败')
    setDefaultProviders()
  }
}

// 设置默认的采集器类型列表
const setDefaultProviders = () => {
  providers.value = [
    { name: 'chaitin', displayName: '长亭漏洞库', link: 'https://stack.chaitin.com/vuldb/index' },
    { name: 'qianxin', displayName: '奇安信威胁情报中心', link: 'https://ti.qianxin.com/vulnerability' },
    { name: 'aliyun', displayName: '阿里云漏洞库', link: '' },
    { name: 'threatbook', displayName: '微步威胁情报', link: 'https://x.threatbook.com' },
    { name: 'seebug', displayName: 'Seebug漏洞平台', link: 'https://www.seebug.org/' },
    { name: 'venustech', displayName: '启明星辰漏洞通告', link: 'https://www.venustech.com.cn/article_type/5/' },
    { name: 'oscs', displayName: 'OSCS情报预警', link: 'https://www.oscs1024.com/' },
    { name: 'nvd', displayName: 'NVD漏洞数据库', link: 'https://nvd.nist.gov/' }
  ]
}

// 获取采集间隔选项
const fetchScheduleOptions = async () => {
  try {
    const response = await fetch('/api/admin/crawler-intervals', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.code === 200 && result.data) {
        scheduleOptions.value = result.data
      }
    }
  } catch (error) {
    console.error('获取采集间隔选项失败:', error)
    // 如果获取失败，使用默认选项
  }
}

// 获取采集日志
const fetchCrawlerLogs = async (crawlerId: number) => {
  logsLoading.value = true
  currentCrawlerId.value = crawlerId
  
  try {
    const res = await api.getCrawlerLogs(crawlerId, logsCurrentPage.value, logsPageSize.value)
    if (res.data && res.data.code === 200) {
      const data = res.data.data
      crawlerLogs.value = data.list || []
      logsTotal.value = data.total || 0
    } else {
      ElMessage.error(res.data?.msg || '获取采集日志失败')
    }
  } catch (error) {
    console.error('获取采集日志失败:', error)
    ElMessage.error('获取采集日志失败')
  } finally {
    logsLoading.value = false
  }
}

// 处理日志分页大小变化
const handleLogsSizeChange = (val: number) => {
  logsPageSize.value = val
  fetchCrawlerLogs(currentCrawlerId.value!)
}

// 处理日志当前页变化
const handleLogsCurrentChange = (val: number) => {
  logsCurrentPage.value = val
  fetchCrawlerLogs(currentCrawlerId.value!)
}

// 显示创建对话框
const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  form.id = 0
  form.type = 'chaitin' // 默认选择长亭漏洞库
  form.description = ''
  form.pageLimit = 1
  form.disclosureDateRange = null
  form.schedule = 'manual' // 默认手动执行（符合V1版本标准）
  form.customInterval = 60 // 默认设置为60分钟，但在手动模式下会被重置为0
  form.status = true
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 显示编辑对话框
const handleEdit = (row: Crawler) => {
  isEdit.value = true
  resetForm()
  form.id = row.id!
  form.type = row.type
  form.description = row.description || ''
  form.status = row.status
  
  // 解析配置
  let config: any = null
  if (row.config) {
    // 确保配置是对象，而不是字符串
    config = typeof row.config === 'string' ? JSON.parse(row.config) : row.config
    
    form.pageLimit = config.pageLimit || 1
    
    // 设置采集周期，使用服务器返回的值
    form.schedule = row.interval || row.schedule || 'daily'
    
    // 仅当采集周期为自定义时，才设置customInterval
    if (form.schedule === 'custom') {
      form.customInterval = config.customInterval || 60
    } else {
      // 对于非自定义模式，设置一个默认值，但不会被使用
      form.customInterval = 60
    }
    
    // 日期范围
    if (config.startDate && config.endDate) {
      form.disclosureDateRange = [config.startDate, config.endDate]
    } else {
      form.disclosureDateRange = null
    }
  }
  
  dialogVisible.value = true
}

// 显示日志对话框
const showLogs = async (crawlerId: number) => {
  logsCurrentPage.value = 1
  logsPageSize.value = 10
  await fetchCrawlerLogs(crawlerId)
  logsDialogVisible.value = true
}

// 确认清除日志
const confirmClearLogs = () => {
  clearLogsDialogVisible.value = true
}

// 执行清除日志
const doClearLogs = async () => {
  clearLogsLoading.value = true
  
  try {
    await api.clearCrawlerLogs(currentCrawlerId.value)
    ElMessage.success('日志清除成功')
    
    // 重新加载日志数据
    logsCurrentPage.value = 1
    await fetchCrawlerLogs(currentCrawlerId.value!)
  } catch (error) {
    console.error('清除日志失败:', error)
    ElMessage.error('清除日志失败')
  } finally {
    clearLogsLoading.value = false
    clearLogsDialogVisible.value = false
  }
}

// 立即采集 (显示确认对话框)
const handleRun = (row: Crawler) => {
  currentRunCrawler.value = row
  runDialogVisible.value = true
}

// 执行采集任务
const doRunCrawler = async () => {
  if (!currentRunCrawler.value) {
    runDialogVisible.value = false
    return
  }
  
  runLoading.value = true
  try {
    await api.runCrawler(currentRunCrawler.value.id!)
    ElMessage.success('采集任务已启动')
    runDialogVisible.value = false
  } catch (error) {
    console.error('启动采集任务失败:', error)
    ElMessage.error('启动采集任务失败')
  } finally {
    runLoading.value = false
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        // 获取类型的显示名称作为采集源名称
        const typeName = getProviderDisplayName(form.type)
        
        // 构建请求数据
        const data: Crawler = {
          name: typeName, // 使用类型的显示名称作为采集源名称
          type: form.type,
          types: [form.type], // 将单个类型包装成数组
          description: form.description,
          status: form.status,
          interval: form.schedule // 使用interval字段而不是schedule
        }
        
        // 设置配置
        data.config = {
          pageLimit: form.pageLimit,
        }
        
        // 只有在自定义间隔模式下才设置customInterval
        if (form.schedule === 'custom') {
          data.config.customInterval = form.customInterval
        }
        
        // 设置漏洞披露日期范围
        if (form.disclosureDateRange && form.disclosureDateRange.length === 2) {
          data.config.startDate = form.disclosureDateRange[0]
          data.config.endDate = form.disclosureDateRange[1]
        }
        
        let res
        if (isEdit.value) {
          // 更新
          res = await api.updateCrawler(form.id, data)
        } else {
          // 创建
          res = await api.createCrawler(data)
        }
        
        if (res.data.code === 0 || res.data.code === 200) {
          ElMessage.success(isEdit.value ? '更新采集源成功' : '创建采集源成功')
          dialogVisible.value = false
          fetchCrawlers() // 刷新列表
        } else {
          ElMessage.error(res.data.msg || '操作失败')
        }
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('提交失败，请稍后重试')
      } finally {
        submitting.value = false
      }
    } else {
      console.log('表单验证失败')
    }
  })
}

// 格式化时间戳
const formatTimestamp = (timestamp: number) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp * 1000)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
}

// 获取日志状态类型
const getLogStatusType = (status: string) => {
  switch (status.toLowerCase()) {
    case 'success':
      return 'success'
    case 'failed':
      return 'danger'
    case 'running':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取日志状态文本
const getLogStatusText = (status: string) => {
  switch (status.toLowerCase()) {
    case 'success':
      return '成功'
    case 'failed':
      return '失败'
    case 'running':
      return '运行中'
    default:
      return status
  }
}

// 获取采集周期文本
const getScheduleText = (schedule: string, config?: any) => {
  if (!schedule) return '未设置';
  
  // 确保config是对象
  let configObj = config;
  if (typeof config === 'string') {
    try {
      configObj = JSON.parse(config);
    } catch (e) {
      // 解析失败，使用原始config
    }
  }
  
  // 根据schedule类型显示相应的文本
  switch (schedule) {
    case 'custom':
      // 对于自定义间隔，显示分钟数
      if (configObj && typeof configObj === 'object' && typeof configObj.customInterval !== 'undefined') {
        return `${configObj.customInterval}分钟`;
      }
      return '自定义间隔';
    case 'hourly':
      return '每小时';
    case 'daily':
      return '每天';
    case 'weekly':
      return '每周';
    case 'monthly':
      return '每月';
    case 'manual':
      return '手动';
    default:
      return schedule;
  }
}

// 获取采集器显示名称
const getProviderDisplayName = (type: string) => {
  // 针对表格显示的简短名称
  switch (type) {
    case 'chaitin':
      return '长亭漏洞库'
    case 'qianxin':
      return '奇安信威胁情报中心'
    case 'aliyun':
      return '阿里云漏洞库'
    case 'threatbook':
      return '微步威胁情报'
    case 'seebug':
      return 'Seebug漏洞平台'
    case 'venustech':
      return '启明星辰漏洞通告'
    case 'oscs':
      return 'OSCS情报预警'
    case 'nvd':
      return 'NVD漏洞数据库'
    default:
      return type
  }
}

// 获取采集器标签类型
const getProviderTagType = (type: string) => {
  switch (type) {
    case 'chaitin':
      return 'success'
    case 'qianxin':
      return 'warning'
    case 'aliyun':
      return 'primary'
    case 'threatbook':
      return 'info'
    case 'seebug':
      return 'danger'
    case 'venustech':
      return 'success'
    case 'oscs':
      return 'warning'
    case 'nvd':
      return 'primary'
    default:
      return 'info'
  }
}

// 判断采集器是否禁用
const isProviderDisabled = (type: string) => {
  // 所有采集器都已实现，不需要禁用
  return false
}

// 获取采集器提示信息
const getProviderNotice = (type: string) => {
  return ''
}

// 生命周期钩子
onMounted(() => {
  fetchCrawlers()
  fetchCrawlerProviders()
  fetchScheduleOptions()
})
</script>

<style scoped>
.crawler-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.operation-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.provider-tag {
  width: 90%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 8px;
  box-sizing: border-box;
  font-size: 12px;
  height: 22px;
  line-height: 22px;
}

.description-cell {
  padding: 0 12px 0 15px;
  text-align: left;
  width: 100%;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table__header) {
  font-size: 12px;
  font-weight: bold;
}

:deep(.el-table__header th) {
  text-align: center;
  padding: 3px 0;
  background-color: #f5f7fa;
  height: 32px;
}

:deep(.el-table__row) {
  height: 38px;
}

:deep(.el-table__cell) {
  padding: 3px 0;
}

/* 类型列居中显示 */
:deep(.el-table .el-table__cell:nth-child(3) .cell) {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 采集周期列居中显示 */
:deep(.el-table .el-table__cell:nth-child(5) .cell) {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 状态列居中显示 */
:deep(.el-table .el-table__cell:nth-child(6) .cell) {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 上次运行时间列居中显示 */
:deep(.el-table .el-table__cell:nth-child(7) .cell) {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 操作列居中显示 */
:deep(.el-table .el-table__cell:nth-child(8) .cell) {
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.el-table .cell.el-tooltip) {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.pagination-container {
  text-align: right;
  margin-top: 15px;
}
</style> 