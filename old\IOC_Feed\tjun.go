// Package IOC_Feed 提供天际友盟IOC API的接口实现和情报查询服务
package IOC_Feed

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"vulnerability_push/internal/config"
	"vulnerability_push/internal/utils"
)

// TJUNClient 天际友盟IOC请求客户端
type TJUNClient struct {
	host      string // API域名，如 api.tj-un.com
	appKey    string // APP KEY
	appSecret string // APP密钥
	timeout   time.Duration
	client    *http.Client
}

// NewTJUNClient 创建新的天际友盟IOC客户端
func NewTJUNClient(host, appKey, appSecret string) *TJUNClient {
	return &TJUNClient{
		host:      host,
		appKey:    appKey,
		appSecret: appSecret,
		timeout:   5 * time.Second, // 默认5秒超时
		client: &http.Client{
			Timeout: 5 * time.Second,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true, // 跳过SSL证书验证
				},
			},
		},
	}
}

// SetTimeout 设置请求超时时间
func (c *TJUNClient) SetTimeout(timeout time.Duration) *TJUNClient {
	c.timeout = timeout
	c.client.Timeout = timeout
	return c
}

// RequestIOCV2Request /v2/requestIOC接口请求结构
type RequestIOCV2Request struct {
	Token string `json:"token"` // 用户身份标识，必填
	Type  string `json:"type"`  // 待查询数据的类型，必填，可选值为ip、domain、email、url、hash
	Value string `json:"value"` // 待查询数据值，必填
}

// RequestIOCV2Response /v2/requestIOC接口响应结构
type RequestIOCV2Response struct {
	ResponseStatus struct {
		Code    int    `json:"code"`    // 响应状态码：1成功，其他为错误
		Message string `json:"message"` // 响应消息
	} `json:"response_status"`
	ResponseData interface{} `json:"response_data"` // 响应数据，具体结构根据查询类型而定
}

// 常量定义
const (
	HMAC_SHA256                    = "HmacSHA256"
	ENCODING                       = "UTF-8"
	USER_AGENT                     = "demo/golang/tjun"
	LF                             = "\n"
	DEFAULT_TIMEOUT                = 5000 // 毫秒
	CA_HEADER_TO_SIGN_PREFIX_SYSTEM = "X-Ca-"

	// HTTP头常量
	HTTP_HEADER_ACCEPT       = "Accept"
	HTTP_HEADER_CONTENT_TYPE = "Content-Type"
	HTTP_HEADER_CONTENT_MD5  = "Content-MD5"
	HTTP_HEADER_DATE         = "Date"
	HTTP_HEADER_USER_AGENT   = "User-Agent"

	// 系统头常量
	X_CA_TIMESTAMP         = "X-Ca-Timestamp"
	X_CA_NONCE            = "X-Ca-Nonce"
	X_CA_KEY              = "X-Ca-Key"
	X_CA_SIGNATURE        = "X-Ca-Signature"
	X_CA_SIGNATURE_HEADERS = "X-Ca-Signature-Headers"

	// 内容类型常量
	CONTENT_TYPE_FORM = "application/x-www-form-urlencoded; charset=UTF-8"
	CONTENT_TYPE_JSON = "application/json; charset=UTF-8"
)

// 签名工具函数

// generateUUID 生成UUID (简化版本)
func generateUUID() string {
	return fmt.Sprintf("%d-%d", time.Now().UnixNano(), time.Now().Unix())
}

// utf8ToISO88591 UTF-8转ISO-8859-1编码 (简化处理)
func utf8ToISO88591(s string) string {
	return s
}

// hmacSHA256 计算HMAC-SHA256签名
func hmacSHA256(data, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

// buildStringToSign 构建待签名字符串
func buildStringToSign(headers map[string]string, urlPath string, formParams map[string]string, method string) string {
	var sb strings.Builder

	// 1. HTTP方法
	sb.WriteString(strings.ToUpper(method))
	sb.WriteString(LF)

	// 2. Accept头
	if accept, ok := headers[HTTP_HEADER_ACCEPT]; ok {
		sb.WriteString(accept)
	}
	sb.WriteString(LF)

	// 3. Content-MD5头
	if contentMD5, ok := headers[HTTP_HEADER_CONTENT_MD5]; ok {
		sb.WriteString(contentMD5)
	}
	sb.WriteString(LF)

	// 4. Content-Type头
	if contentType, ok := headers[HTTP_HEADER_CONTENT_TYPE]; ok {
		sb.WriteString(contentType)
	}
	sb.WriteString(LF)

	// 5. Date头
	if date, ok := headers[HTTP_HEADER_DATE]; ok {
		sb.WriteString(date)
	}
	sb.WriteString(LF)

	// 6. 构建签名头
	sb.WriteString(buildHeaders(headers))

	// 7. 构建资源路径
	sb.WriteString(buildResource(urlPath, formParams))

	return sb.String()
}

// buildHeaders 构建待签名Http头
func buildHeaders(headers map[string]string) string {
	headersToSign := make(map[string]string)
	var signHeadersList []string

	// 收集需要签名的头
	for key, value := range headers {
		if isHeaderToSign(key) {
			headersToSign[key] = value
			signHeadersList = append(signHeadersList, key)
		}
	}

	// 按字典序排序
	sort.Strings(signHeadersList)

	// 设置签名头列表
	if len(signHeadersList) > 0 {
		headers[X_CA_SIGNATURE_HEADERS] = strings.Join(signHeadersList, ",")
	}

	// 构建签名字符串
	var sb strings.Builder
	for _, key := range signHeadersList {
		sb.WriteString(key)
		sb.WriteString(":")
		sb.WriteString(headersToSign[key])
		sb.WriteString(LF)
	}

	return sb.String()
}

// buildResource 构建待签名Path+Query+FormParams
func buildResource(urlPath string, formParams map[string]string) string {
	var sb strings.Builder

	// 解析URL，分离路径和查询参数
	parsedURL, err := url.Parse(urlPath)
	if err != nil {
		sb.WriteString(urlPath)
	} else {
		sb.WriteString(parsedURL.Path)

		// 合并查询参数和表单参数
		allParams := make(map[string]string)

		// 添加URL查询参数
		for key, values := range parsedURL.Query() {
			if len(values) > 0 {
				allParams[key] = values[0]
			} else {
				allParams[key] = ""
			}
		}

		// 添加表单参数
		for key, value := range formParams {
			if _, exists := allParams[key]; !exists {
				allParams[key] = value
			}
		}

		// 按字典序排序参数
		if len(allParams) > 0 {
			sb.WriteString("?")

			var keys []string
			for key := range allParams {
				keys = append(keys, key)
			}
			sort.Strings(keys)

			for i, key := range keys {
				if i > 0 {
					sb.WriteString("&")
				}

				value := allParams[key]
				if value == "" {
					sb.WriteString(key)
				} else {
					sb.WriteString(key)
					sb.WriteString("=")
					sb.WriteString(value)
				}
			}
		}
	}

	return sb.String()
}

// isHeaderToSign 判断Http头是否参与签名
func isHeaderToSign(headerName string) bool {
	if headerName == "" {
		return false
	}

	// 系统头参与签名
	if strings.HasPrefix(headerName, CA_HEADER_TO_SIGN_PREFIX_SYSTEM) {
		return true
	}

	return false
}

// initialBasicHeader 初始化基础Header
func (c *TJUNClient) initialBasicHeader(headers map[string]string, method, requestURL string, formParams map[string]string) map[string]string {
	if headers == nil {
		headers = make(map[string]string)
	}

	// 解析URL获取路径和查询参数
	parsedURL, err := url.Parse(requestURL)
	if err != nil {
		return headers
	}

	var pathAndQuery strings.Builder
	if parsedURL.Path != "" {
		pathAndQuery.WriteString(parsedURL.Path)
	}
	if parsedURL.RawQuery != "" {
		pathAndQuery.WriteString("?")
		pathAndQuery.WriteString(parsedURL.RawQuery)
	}

	// 设置基础头部
	headers[HTTP_HEADER_USER_AGENT] = USER_AGENT
	headers[X_CA_TIMESTAMP] = strconv.FormatInt(time.Now().UnixMilli(), 10)
	headers[X_CA_NONCE] = generateUUID()
	headers[X_CA_KEY] = c.appKey

	// 计算签名 (必须在设置完所有头部后计算)
	signature := hmacSHA256(buildStringToSign(headers, pathAndQuery.String(), formParams, method), c.appSecret)
	headers[X_CA_SIGNATURE] = signature

	return headers
}

// RequestIOCV2 调用/v2/requestIOC接口
func (c *TJUNClient) RequestIOCV2(ctx context.Context, token, iocType, value string) (*RequestIOCV2Response, error) {
	// 参数验证
	if c.appKey == "" {
		return nil, fmt.Errorf("请填写APP KEY")
	}
	if c.appSecret == "" {
		return nil, fmt.Errorf("请填写APP SECRET")
	}
	if c.host == "" {
		return nil, fmt.Errorf("请填写host")
	}
	if token == "" {
		return nil, fmt.Errorf("请填写token")
	}
	if iocType == "" {
		return nil, fmt.Errorf("请填写type")
	}
	if value == "" {
		return nil, fmt.Errorf("请填写value")
	}

	// 构建请求URL
	requestURL := fmt.Sprintf("https://%s/v2/requestIOC", c.host)

	// 构建表单参数
	formParams := map[string]string{
		"token": token,
		"type":  iocType,
		"value": value,
	}

	// 初始化请求头
	headers := map[string]string{
		HTTP_HEADER_ACCEPT:       "application/json",
		HTTP_HEADER_CONTENT_TYPE: CONTENT_TYPE_FORM,
	}

	// 初始化基础头部和签名
	headers = c.initialBasicHeader(headers, "POST", requestURL, formParams)

	return c.executeRequest(ctx, "POST", requestURL, headers, formParams)
}

// RequestIOCReputation 调用/v1/reputation接口
func (c *TJUNClient) RequestIOCReputation(ctx context.Context, token, iocType, value, struct_, nextpage string) (*RequestIOCV2Response, error) {
	// 参数验证
	if c.appKey == "" {
		return nil, fmt.Errorf("请填写APP KEY")
	}
	if c.appSecret == "" {
		return nil, fmt.Errorf("请填写APP SECRET")
	}
	if c.host == "" {
		return nil, fmt.Errorf("请填写host")
	}
	if token == "" {
		return nil, fmt.Errorf("请填写token")
	}
	if iocType == "" {
		return nil, fmt.Errorf("请填写type")
	}
	if value == "" {
		return nil, fmt.Errorf("请填写value")
	}
	if struct_ == "" {
		return nil, fmt.Errorf("请填写struct")
	}

	// 构建请求URL
	requestURL := fmt.Sprintf("https://%s/v1/reputation", c.host)

	// 构建表单参数
	formParams := map[string]string{
		"token":  token,
		"type":   iocType,
		"value":  value,
		"struct": struct_,
	}

	// 添加可选的nextpage参数
	if nextpage != "" {
		formParams["nextpage"] = nextpage
	}

	// 初始化请求头
	headers := map[string]string{
		HTTP_HEADER_ACCEPT:       "application/json",
		HTTP_HEADER_CONTENT_TYPE: CONTENT_TYPE_FORM,
	}

	// 初始化基础头部和签名
	headers = c.initialBasicHeader(headers, "POST", requestURL, formParams)

	return c.executeRequest(ctx, "POST", requestURL, headers, formParams)
}

// executeRequest 执行HTTP请求
func (c *TJUNClient) executeRequest(ctx context.Context, method, requestURL string, headers map[string]string, formParams map[string]string) (*RequestIOCV2Response, error) {
	// 构建表单数据 (application/x-www-form-urlencoded格式)
	formData := url.Values{}
	for key, value := range formParams {
		formData.Set(key, value)
	}

	// 创建请求
	var req *http.Request
	var err error

	if method == "POST" {
		// POST请求，使用表单数据作为请求体
		req, err = http.NewRequestWithContext(ctx, method, requestURL, strings.NewReader(formData.Encode()))
	} else {
		req, err = http.NewRequestWithContext(ctx, method, requestURL, nil)
	}

	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	for key, value := range headers {
		req.Header.Set(key, utf8ToISO88591(value))
	}

	// 调试输出请求信息
	utils.Infof("请求URL: %s\n", requestURL)
	utils.Infof("请求方法: %s\n", method)
	utils.Infof("请求头:\n")
	for key, value := range headers {
		utils.Infof("  %s: %s\n", key, value)
	}
	utils.Infof("请求体: %s\n", formData.Encode())

	// 执行请求，支持重试
	var resp *http.Response
	for i := 0; i < 3; i++ {
		resp, err = c.client.Do(req)
		if err != nil {
			if i == 2 { // 最后一次重试
				return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
			}
			continue
		}

		// 检查状态码
		if resp.StatusCode == 200 {
			break
		} else if resp.StatusCode >= 500 {
			resp.Body.Close()
			if i == 2 { // 最后一次重试
				return nil, fmt.Errorf("服务器错误，状态码: %d", resp.StatusCode)
			}
			continue
		} else {
			// 客户端错误，不重试
			break
		}
	}

	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应数据
	var response RequestIOCV2Response
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %v, 响应内容: %s", err, string(body))
	}

	// 检查业务状态码 (天际友盟API成功状态码为1)
	if response.ResponseStatus.Code != 1 {
		return nil, fmt.Errorf("API返回业务错误: code=%d, message=%s", response.ResponseStatus.Code, response.ResponseStatus.Message)
	}

	return &response, nil
}

// =============================================================================
// 天际友盟情报查询服务
// =============================================================================

// TJUNIntelligenceService 天际友盟情报查询服务
type TJUNIntelligenceService struct {
	client *TJUNClient
	config *config.TJUNConfig
}

// TJUNIntelligenceResult 天际友盟情报查询结果
type TJUNIntelligenceResult struct {
	Success      bool        `json:"success"`
	Data         interface{} `json:"data"`
	ErrorMessage string      `json:"errorMessage,omitempty"`
	QueryTime    int64       `json:"queryTime"`
}

// IOCBatchRequest 批量IOC查询请求
type IOCBatchRequest struct {
	IOCs     []string `json:"iocs"`     // IOC值列表
	IOCType  string   `json:"iocType"`  // IOC类型
	MaxBatch int      `json:"maxBatch"` // 最大批量大小，默认10
}

// IOCBatchResult 批量IOC查询结果
type IOCBatchResult struct {
	Success      bool                           `json:"success"`
	Results      map[string]*TJUNIntelligenceResult `json:"results"`     // IOC -> 查询结果的映射
	ErrorMessage string                         `json:"errorMessage,omitempty"`
	QueryTime    int64                          `json:"queryTime"`
}

// NewTJUNIntelligenceService 创建天际友盟情报查询服务
func NewTJUNIntelligenceService(cfg *config.TJUNConfig) *TJUNIntelligenceService {
	if cfg == nil {
		// 使用默认配置
		cfg = &config.TJUNConfig{
			Enabled:      true,
			Host:         "api.tj-un.com",
			AppKey:       "24634572",
			AppSecret:    "812d078fe8143e529599f9d6689a6046",
			Token:        "9fd3b7f517dc4674b7f001e0b1ed7c61",
			Timeout:      10,
			MaxBatchSize: 10,
			RetryCount:   3,
		}
	}

	client := NewTJUNClient(cfg.Host, cfg.AppKey, cfg.AppSecret)
	client.SetTimeout(time.Duration(cfg.Timeout) * time.Second)

	return &TJUNIntelligenceService{
		client: client,
		config: cfg,
	}
}

// QueryIOCIntelligence 查询单个IOC情报
func (s *TJUNIntelligenceService) QueryIOCIntelligence(ioc, iocType string) *TJUNIntelligenceResult {
	// 调用批量查询，只传入一个IOC
	batchResult := s.QueryIOCIntelligenceBatch([]string{ioc}, iocType, 1)
	if result, exists := batchResult.Results[ioc]; exists {
		return result
	}

	// 如果批量查询失败，返回失败结果
	return &TJUNIntelligenceResult{
		Success:      false,
		ErrorMessage: batchResult.ErrorMessage,
		QueryTime:    batchResult.QueryTime,
	}
}

// QueryIOCIntelligenceBatch 批量查询IOC情报
func (s *TJUNIntelligenceService) QueryIOCIntelligenceBatch(iocs []string, iocType string, maxBatch int) *IOCBatchResult {
	if maxBatch <= 0 {
		maxBatch = s.config.MaxBatchSize // 使用配置中的默认值
	}

	result := &IOCBatchResult{
		QueryTime: time.Now().Unix(),
		Results:   make(map[string]*TJUNIntelligenceResult),
	}

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(s.config.Timeout*2)*time.Second) // 批量查询需要更长时间
	defer cancel()

	// 按批次处理IOC
	for i := 0; i < len(iocs); i += maxBatch {
		end := i + maxBatch
		if end > len(iocs) {
			end = len(iocs)
		}

		batch := iocs[i:end]
		if err := s.processBatch(ctx, batch, iocType, result); err != nil {
			// 如果批次处理失败，为该批次的所有IOC设置错误结果
			for _, ioc := range batch {
				result.Results[ioc] = &TJUNIntelligenceResult{
					Success:      false,
					ErrorMessage: fmt.Sprintf("批量查询失败: %v", err),
					QueryTime:    result.QueryTime,
				}
			}
		}
	}

	// 检查是否有成功的结果
	hasSuccess := false
	for _, res := range result.Results {
		if res.Success {
			hasSuccess = true
			break
		}
	}

	result.Success = hasSuccess
	if !hasSuccess && len(result.Results) > 0 {
		// 如果所有查询都失败，设置总体错误信息
		for _, res := range result.Results {
			if res.ErrorMessage != "" {
				result.ErrorMessage = res.ErrorMessage
				break
			}
		}
	}

	return result
}

// processBatch 处理单个批次的IOC查询
func (s *TJUNIntelligenceService) processBatch(ctx context.Context, batch []string, iocType string, result *IOCBatchResult) error {
	if strings.ToLower(iocType) == "ip" {
		// 对于IP类型，调用reputation接口
		ipArrayBytes, _ := json.Marshal(batch)
		ipArray := string(ipArrayBytes)

		reputationResp, err := s.client.RequestIOCReputation(ctx, s.config.Token, "ip", ipArray, "ip_reputation", "")
		if err != nil {
			return fmt.Errorf("查询IP信誉失败: %v", err)
		}

		// 解析批量结果
		if reputationData, ok := reputationResp.ResponseData.(map[string]interface{}); ok {
			if ipReputations, ok := reputationData["ip_reputation"].([]interface{}); ok {
				// 为每个查询的IP创建结果映射
				ipResultMap := make(map[string]interface{})
				for _, ipRep := range ipReputations {
					if ipInfo, ok := ipRep.(map[string]interface{}); ok {
						if ip, ok := ipInfo["ip"].(string); ok {
							ipResultMap[ip] = ipInfo
						}
					}
				}

				// 为批次中的每个IP设置结果
				for _, ip := range batch {
					if ipData, exists := ipResultMap[ip]; exists {
						result.Results[ip] = &TJUNIntelligenceResult{
							Success:   true,
							Data:      map[string]interface{}{"ip_reputation": []interface{}{ipData}},
							QueryTime: result.QueryTime,
						}
					} else {
						result.Results[ip] = &TJUNIntelligenceResult{
							Success:      false,
							ErrorMessage: "未找到该IP的信誉信息",
							QueryTime:    result.QueryTime,
						}
					}
				}
			} else {
				return fmt.Errorf("解析IP信誉响应数据失败")
			}
		} else {
			return fmt.Errorf("解析响应数据格式失败")
		}
	} else {
		// 对于其他类型，目前仍然单独查询（因为API限制）
		for _, ioc := range batch {
			iocResp, err := s.client.RequestIOCV2(ctx, s.config.Token, iocType, ioc)
			if err != nil {
				result.Results[ioc] = &TJUNIntelligenceResult{
					Success:      false,
					ErrorMessage: fmt.Sprintf("查询IOC情报失败: %v", err),
					QueryTime:    result.QueryTime,
				}
			} else {
				result.Results[ioc] = &TJUNIntelligenceResult{
					Success:   true,
					Data:      iocResp.ResponseData,
					QueryTime: result.QueryTime,
				}
			}
		}
	}

	return nil
}

// SerializeData 序列化数据为JSON字符串
func (r *TJUNIntelligenceResult) SerializeData() (string, error) {
	if r.Data == nil {
		return "", nil
	}

	dataBytes, err := json.Marshal(r.Data)
	if err != nil {
		return "", fmt.Errorf("序列化天际友盟数据失败: %v", err)
	}

	return string(dataBytes), nil
}

// GetQueryStatus 获取查询状态
func (r *TJUNIntelligenceResult) GetQueryStatus() string {
	if r.Success {
		return "success"
	}
	return "failed"
}

// =============================================================================
// 全局服务实例管理
// =============================================================================

// 全局天际友盟情报服务实例
var globalTJUNService *TJUNIntelligenceService

// InitGlobalTJUNService 初始化全局天际友盟情报服务
func InitGlobalTJUNService() error {
	// 从配置文件加载配置
	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		return fmt.Errorf("加载配置文件失败: %v", err)
	}

	if !cfg.TJUN.Enabled {
		return fmt.Errorf("天际友盟服务已禁用")
	}

	globalTJUNService = NewTJUNIntelligenceService(&cfg.TJUN)
	return nil
}

// GetGlobalTJUNService 获取全局天际友盟情报服务
func GetGlobalTJUNService() *TJUNIntelligenceService {
	if globalTJUNService == nil {
		// 如果没有初始化，自动初始化
		InitGlobalTJUNService()
	}
	return globalTJUNService
}

// QueryIOCIntelligenceGlobal 使用全局服务查询IOC情报
func QueryIOCIntelligenceGlobal(ioc, iocType string) *TJUNIntelligenceResult {
	service := GetGlobalTJUNService()
	if service == nil {
		return &TJUNIntelligenceResult{
			Success:      false,
			ErrorMessage: "天际友盟服务不可用",
			QueryTime:    time.Now().Unix(),
		}
	}
	return service.QueryIOCIntelligence(ioc, iocType)
}
