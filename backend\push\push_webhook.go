package push

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

// Webhook推送类型常量
const TypeWebhook = "webhook"

// WebhookConfig Webhook配置
type WebhookConfig struct {
	URL    string `yaml:"url" json:"url"`
	Method string `yaml:"method" json:"method"`
}

// 推送到自定义Webhook
func PushToWebhook(vuln *Vulnerability, channel *PushChannel, record *PushRecord) error {
	// 获取通道配置
	config := channel.GetConfig()
	if config == nil {
		return fmt.Errorf("通道配置为空")
	}
	
	// 检查webhook URL是否存在
	webhookURL, ok := config["url"].(string)
	if !ok || webhookURL == "" {
		return fmt.Errorf("Webhook URL未配置或格式不正确")
	}
	
	// 获取请求方法，默认为POST
	method := "POST"
	if methodVal, ok := config["method"].(string); ok && methodVal != "" {
		method = methodVal
	}
	
	// 构建要发送的漏洞数据
	data := map[string]interface{}{
		"id":             vuln.ID,
		"name":           vuln.Name,
		"vuln_id":        vuln.VulnID,
		"severity":       vuln.Severity,
		"disclosure_date": vuln.DisclosureDate,
		"source":         vuln.Source,
		"description":    vuln.Description,
		"push_reason":    vuln.PushReason,
		"remediation":    vuln.Remediation,
		"references":     vuln.GetReferences(),
		"tags":           vuln.GetTags(),
		"timestamp":      time.Now().Unix(),
		"push_channel": map[string]interface{}{
			"id":   channel.ID,
			"name": channel.Name,
			"type": channel.Type,
		},
	}
	
	// 将数据转换为JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("构建JSON数据失败: %v", err)
	}
	
	// 创建HTTP请求
	req, err := http.NewRequest(method, webhookURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %v", err)
	}
	
	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Vulnerability-Push-System/1.0")
	
	// 设置自定义请求头
	if headers, ok := config["headers"].(map[string]interface{}); ok {
		for key, value := range headers {
			if strValue, ok := value.(string); ok {
				req.Header.Set(key, strValue)
			}
		}
	}
	
	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("推送失败，HTTP状态码: %d", resp.StatusCode)
	}
	
	return nil
}

// 推送IOC情报到自定义Webhook
func PushIOCIntelligenceToWebhook(ioc *IOCIntelligence, channel *PushChannel, record *IOCIntelligencePushRecord) error {
	// 获取通道配置
	config := channel.GetConfig()
	if config == nil {
		return fmt.Errorf("通道配置为空")
	}

	// 检查webhook URL是否存在
	webhookURL, ok := config["url"].(string)
	if !ok || webhookURL == "" {
		return fmt.Errorf("Webhook URL未配置或格式不正确")
	}

	// 获取请求方法，默认为POST
	method := "POST"
	if methodVal, ok := config["method"].(string); ok && methodVal != "" {
		method = methodVal
	}

	// 构建要发送的IOC情报数据
	data := map[string]interface{}{
		"id":             ioc.ID,
		"ioc":            ioc.IOC,
		"ioc_type":       ioc.IOCType,
		"location":       ioc.Location,
		"type":           ioc.Type,
		"risk_level":     ioc.RiskLevel,
		"hit_count":      ioc.HitCount,
		"description":    ioc.Description,
		"source":         ioc.Source,
		"discovery_date": ioc.DiscoveryDate,
		"tags":           ioc.Tags,
		"push_reason":    ioc.PushReason,
		"push_time":      time.Now().Format("2006-01-02 15:04:05"),
		"message_type":   "ioc_intelligence",
	}

	// 序列化数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化数据失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest(method, webhookURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "VulnPush-IOC-Bot/1.0")

	// 如果配置中有自定义头部，添加它们
	if headers, ok := config["headers"].(map[string]interface{}); ok {
		for key, value := range headers {
			if strValue, ok := value.(string); ok {
				req.Header.Set(key, strValue)
			}
		}
	}

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("推送失败，HTTP状态码: %d", resp.StatusCode)
	}

	return nil
}

// 批量推送IOC情报到自定义Webhook
func BatchPushIOCIntelligenceToWebhook(iocIntels []*IOCIntelligence, channel *PushChannel) error {
	// 获取通道配置
	config := channel.GetConfig()
	if config == nil {
		return fmt.Errorf("通道配置为空")
	}

	// 检查webhook URL是否存在
	webhookURL, ok := config["url"].(string)
	if !ok || webhookURL == "" {
		return fmt.Errorf("Webhook URL未配置或格式不正确")
	}

	// 获取请求方法，默认为POST
	method := "POST"
	if methodVal, ok := config["method"].(string); ok && methodVal != "" {
		method = methodVal
	}

	// 构建要发送的批量IOC情报数据
	iocList := make([]map[string]interface{}, 0, len(iocIntels))
	for _, ioc := range iocIntels {
		iocData := map[string]interface{}{
			"id":             ioc.ID,
			"ioc":            ioc.IOC,
			"ioc_type":       ioc.IOCType,
			"location":       ioc.Location,
			"type":           ioc.Type,
			"risk_level":     ioc.RiskLevel,
			"hit_count":      ioc.HitCount,
			"description":    ioc.Description,
			"source":         ioc.Source,
			"discovery_date": ioc.DiscoveryDate,
			"tags":           ioc.Tags,
			"push_reason":    ioc.PushReason,
		}
		iocList = append(iocList, iocData)
	}

	data := map[string]interface{}{
		"message_type":   "batch_ioc_intelligence",
		"total_count":    len(iocIntels),
		"push_time":      time.Now().Format("2006-01-02 15:04:05"),
		"ioc_list":       iocList,
		"summary":        buildBatchIOCPushContent(iocIntels),
	}

	// 序列化数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化数据失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest(method, webhookURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "VulnPush-IOC-Bot/1.0")

	// 如果配置中有自定义头部，添加它们
	if headers, ok := config["headers"].(map[string]interface{}); ok {
		for key, value := range headers {
			if strValue, ok := value.(string); ok {
				req.Header.Set(key, strValue)
			}
		}
	}

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("推送失败，HTTP状态码: %d", resp.StatusCode)
	}

	return nil
}