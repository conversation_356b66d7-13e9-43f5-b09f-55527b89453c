package handlers

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/internal/middleware"
	"vulnerability_push/internal/utils"
)

// RouterManager 路由管理器
type RouterManager struct {
	engine *gin.Engine

	// 中间件
	authMiddleware *middleware.AuthMiddleware

	// 处理器
	userHandler                         *UserHandler
	vulnerabilityHandler                *VulnerabilityHandler
	iocHandler                          *IOCHandler
	dataInterfaceHandler                *DataInterfaceHandler
	threatIntelligenceInterfaceHandler  *ThreatIntelligenceInterfaceHandler
	productionStrategyHandler           *ProductionStrategyHandler
	pushHandler                         *PushHandler
	exportHandler                       *ExportHandler
	crawlerHandler                      *CrawlerHandler
}

// NewRouterManager 创建路由管理器
func NewRouterManager(db *gorm.DB, jwtSecret string, jwtExpireHours int) *RouterManager {
	// 创建认证中间件配置
	authConfig := &middleware.AuthConfig{
		JWTSecret:     jwtSecret,
		JWTExpireHour: int64(jwtExpireHours),
	}

	return &RouterManager{
		authMiddleware:                      middleware.NewAuthMiddleware(authConfig),
		userHandler:                         NewUserHandler(db, jwtSecret, jwtExpireHours),
		vulnerabilityHandler:                NewVulnerabilityHandler(db),
		iocHandler:                          NewIOCHandler(db),
		dataInterfaceHandler:                NewDataInterfaceHandler(db),
		threatIntelligenceInterfaceHandler:  NewThreatIntelligenceInterfaceHandler(db),
		productionStrategyHandler:           NewProductionStrategyHandler(db),
		pushHandler:                         NewPushHandler(db),
		exportHandler:                       NewExportHandler(db),
		crawlerHandler:                      NewCrawlerHandler(db),
	}
}

// SetupRoutes 设置路由
func (rm *RouterManager) SetupRoutes() *gin.Engine {
	// 创建Gin引擎
	rm.engine = gin.New()
	
	// 添加中间件
	rm.setupMiddleware()
	
	// 设置路由组
	rm.setupAPIRoutes()
	
	return rm.engine
}

// setupCompatibilityRoutes 设置兼容性路由（匹配前端期望的路径）
func (rm *RouterManager) setupCompatibilityRoutes() {
	// 认证相关兼容性路由（不需要认证）
	rm.engine.POST("/api/login", rm.userHandler.Login)
	rm.engine.POST("/api/logout", rm.userHandler.Logout)

	// 需要认证的兼容性路由
	authenticated := rm.engine.Group("/api")
	authenticated.Use(rm.authMiddleware.JWTAuth())
	{
		authenticated.GET("/verify", rm.userHandler.VerifyToken)
		authenticated.GET("/user", rm.userHandler.GetCurrentUserInfo)
		authenticated.GET("/vulnerabilities", rm.vulnerabilityHandler.GetVulnerabilities)
		authenticated.GET("/vulnerabilities/:id", rm.vulnerabilityHandler.GetVulnerability)
		authenticated.GET("/vulnerability/stats", rm.vulnerabilityHandler.GetDashboardStats)
		authenticated.GET("/debug/database", rm.vulnerabilityHandler.GetDatabaseInfo)
		authenticated.POST("/vulnerabilities/export", rm.vulnerabilityHandler.ExportVulnerabilities)
		authenticated.POST("/vulnerabilities/export-by-date", rm.vulnerabilityHandler.ExportVulnerabilitiesByDate)
		authenticated.POST("/change-password", rm.userHandler.ChangePassword)
		authenticated.POST("/validate-api-key", rm.userHandler.ValidateAPIKey)
	}

	// 用户管理兼容性路由
	admin := rm.engine.Group("/api/admin")
	admin.Use(rm.authMiddleware.JWTAuth())
	{
		users := admin.Group("/users")
		{
			users.GET("", rm.userHandler.GetUsers)
			users.POST("", rm.userHandler.CreateUser)
			users.GET("/:id", rm.userHandler.GetUser)
			users.PUT("/:id", rm.userHandler.UpdateUser)
			users.DELETE("/:id", rm.userHandler.DeleteUser)
			users.PATCH("/:id/reset-api-key", rm.userHandler.AdminResetAPIKey)
			users.POST("/:id/change-password", rm.userHandler.AdminChangePassword)
		}

		vulnerabilities := admin.Group("/vulnerabilities")
		{
			vulnerabilities.GET("", rm.vulnerabilityHandler.GetVulnerabilities)
			vulnerabilities.POST("", rm.vulnerabilityHandler.CreateVulnerability)
			vulnerabilities.GET("/:id", rm.vulnerabilityHandler.GetVulnerability)
			vulnerabilities.PUT("/:id", rm.vulnerabilityHandler.UpdateVulnerability)
			vulnerabilities.DELETE("/:id", rm.vulnerabilityHandler.DeleteVulnerability)
			vulnerabilities.POST("/:id/push", rm.pushHandler.PushVulnerability)
		}
	}
}

// setupIOCCompatibilityRoutes 设置IOC情报兼容性路由
func (rm *RouterManager) setupIOCCompatibilityRoutes() {
	// IOC情报管理兼容性路由
	admin := rm.engine.Group("/api/admin")
	admin.Use(rm.authMiddleware.JWTAuth())
	{
		iocIntelligence := admin.Group("/ioc-intelligence")
		{
			iocIntelligence.GET("", rm.iocHandler.GetIOCIntelligence)
			iocIntelligence.POST("", rm.iocHandler.CreateIOCIntelligence)
			iocIntelligence.GET("/stats", rm.iocHandler.GetIOCIntelligenceStats)
			iocIntelligence.POST("/export", rm.iocHandler.ExportIOCIntelligence)
			iocIntelligence.GET("/:id", rm.iocHandler.GetIOCIntelligenceByID)
			iocIntelligence.PUT("/:id", rm.iocHandler.UpdateIOCIntelligence)
			iocIntelligence.DELETE("/:id", rm.iocHandler.DeleteIOCIntelligence)
			iocIntelligence.POST("/:id/push", rm.iocHandler.PushIOCIntelligence)
			iocIntelligence.POST("/batch-push", rm.iocHandler.BatchPushIOCIntelligence)
		}

		// IOC情报数据相关路由（兼容性路由，重定向到新的数据采集模块）
		iocData := admin.Group("/ioc-intelligence-data")
		{
			iocData.GET("", rm.iocHandler.GetIOCSourceData)
			iocData.POST("/generate", rm.iocHandler.GenerateIOCSourceData)
			iocData.POST("/batch-delete", rm.iocHandler.BatchDeleteIOCSourceData)
			iocData.DELETE("/clear-uuid-records", rm.iocHandler.ClearUUIDRecords)
		}

		// IOC白名单相关路由
		iocWhitelist := admin.Group("/ioc-whitelist")
		{
			iocWhitelist.GET("", rm.iocHandler.GetIOCWhitelist)
			iocWhitelist.POST("", rm.iocHandler.CreateIOCWhitelist)
			iocWhitelist.GET("/:id", rm.iocHandler.GetIOCWhitelistByID)
			iocWhitelist.PUT("/:id", rm.iocHandler.UpdateIOCWhitelist)
			iocWhitelist.DELETE("/:id", rm.iocHandler.DeleteIOCWhitelist)
			iocWhitelist.POST("/batch-delete", rm.iocHandler.BatchDeleteIOCWhitelist)
			iocWhitelist.GET("/template", rm.iocHandler.DownloadIOCWhitelistTemplate)
			iocWhitelist.POST("/batch-import", rm.iocHandler.BatchImportIOCWhitelist)
			iocWhitelist.GET("/cache/stats", rm.iocHandler.GetIOCWhitelistCacheStats)
			iocWhitelist.POST("/cache/refresh", rm.iocHandler.RefreshIOCWhitelistCache)
		}

		// 数据接口相关路由
		dataInterfaces := admin.Group("/data-interfaces")
		{
			// 接口管理
			dataInterfaces.GET("", rm.dataInterfaceHandler.GetDataInterfaces)
			dataInterfaces.POST("", rm.dataInterfaceHandler.CreateDataInterface)
			dataInterfaces.GET("/stats", rm.dataInterfaceHandler.GetDataInterfaceStats)
			dataInterfaces.GET("/running", rm.dataInterfaceHandler.GetRunningDataInterfaces)
			dataInterfaces.GET("/:id", rm.dataInterfaceHandler.GetDataInterfaceDetail)
			dataInterfaces.PUT("/:id", rm.dataInterfaceHandler.UpdateDataInterface)
			dataInterfaces.DELETE("/:id", rm.dataInterfaceHandler.DeleteDataInterface)
			dataInterfaces.POST("/:id/toggle", rm.dataInterfaceHandler.ToggleDataInterfaceStatus)
			dataInterfaces.POST("/:id/run", rm.dataInterfaceHandler.RunDataInterface)
			dataInterfaces.GET("/:id/logs", rm.dataInterfaceHandler.GetDataInterfaceLogs)
			dataInterfaces.GET("/:id/stats", rm.dataInterfaceHandler.GetDataInterfaceDetailedStats)

			// 接口类型管理
			dataInterfaces.GET("/types", rm.dataInterfaceHandler.GetDataInterfaceTypes)
			dataInterfaces.GET("/types/:type", rm.dataInterfaceHandler.GetDataInterfaceTypeInfo)
			dataInterfaces.POST("/validate-config", rm.dataInterfaceHandler.ValidateDataInterfaceConfig)
		}

		// 威胁情报接口相关路由
		threatIntelInterfaces := admin.Group("/threat-intelligence-interfaces")
		{
			threatIntelInterfaces.GET("", rm.threatIntelligenceInterfaceHandler.GetThreatIntelligenceInterfaces)
			threatIntelInterfaces.POST("", rm.threatIntelligenceInterfaceHandler.CreateThreatIntelligenceInterface)
			threatIntelInterfaces.GET("/:id", rm.threatIntelligenceInterfaceHandler.GetThreatIntelligenceInterface)
			threatIntelInterfaces.PUT("/:id", rm.threatIntelligenceInterfaceHandler.UpdateThreatIntelligenceInterface)
			threatIntelInterfaces.DELETE("/:id", rm.threatIntelligenceInterfaceHandler.DeleteThreatIntelligenceInterface)
			threatIntelInterfaces.POST("/batch-delete", rm.threatIntelligenceInterfaceHandler.BatchDeleteThreatIntelligenceInterfaces)
			threatIntelInterfaces.POST("/:id/test", rm.threatIntelligenceInterfaceHandler.TestThreatIntelligenceInterface)
		}

		// 生产策略相关路由
		productionStrategies := admin.Group("/production-strategies")
		{
			productionStrategies.GET("", rm.productionStrategyHandler.GetProductionStrategies)
			productionStrategies.POST("", rm.productionStrategyHandler.CreateProductionStrategy)
			productionStrategies.PUT("/:id", rm.productionStrategyHandler.UpdateProductionStrategy)
			productionStrategies.DELETE("/:id", rm.productionStrategyHandler.DeleteProductionStrategy)
			productionStrategies.POST("/:id/toggle-status", rm.productionStrategyHandler.ToggleProductionStrategyStatus)
			productionStrategies.POST("/:id/execute", rm.productionStrategyHandler.ExecuteProductionStrategy)
			productionStrategies.GET("/:id/logs", rm.productionStrategyHandler.GetProductionStrategyLogs)
		}

		// 生产策略配置路由（兼容性）
		admin.GET("/production-strategy-config", rm.productionStrategyHandler.GetProductionStrategyConfig)

		// 推送相关路由
		pushChannels := admin.Group("/push/channels")
		{
			pushChannels.GET("", rm.pushHandler.GetPushChannels)
			pushChannels.POST("", rm.pushHandler.CreatePushChannel)
			pushChannels.GET("/:id", rm.pushHandler.GetPushChannel)
			pushChannels.PUT("/:id", rm.pushHandler.UpdatePushChannel)
			pushChannels.DELETE("/:id", rm.pushHandler.DeletePushChannel)
			pushChannels.POST("/:id/test", rm.pushHandler.TestPushChannel)
		}

		pushPolicies := admin.Group("/push/policies")
		{
			pushPolicies.GET("", rm.pushHandler.GetPushPolicies)
			pushPolicies.GET("/:id", rm.pushHandler.GetPushPolicy)
			pushPolicies.PUT("/:id", rm.pushHandler.UpdatePushPolicy)
		}

		pushRecords := admin.Group("/push/records")
		{
			pushRecords.GET("", rm.pushHandler.GetPushRecords)
		}

		// 推送白名单路由
		admin.GET("/push/whitelist", rm.pushHandler.GetPushWhitelist)
		admin.PUT("/push/whitelist", rm.pushHandler.UpdatePushWhitelist)

		// RSS配置路由
		admin.GET("/push/rss/config", rm.pushHandler.GetRssConfig)
		admin.PUT("/push/rss/config", rm.pushHandler.UpdateRssConfig)

		// 导出配置路由
		admin.GET("/export/config", rm.exportHandler.GetExportConfig)
		admin.POST("/export/config", rm.exportHandler.SaveExportConfig)
		admin.GET("/export/files", rm.exportHandler.GetExportFiles)
		admin.GET("/export/files/:filename", rm.exportHandler.DownloadExportFile)
		admin.DELETE("/export/files/:filename", rm.exportHandler.DeleteExportFile)

		// 采集器相关路由（兼容性路由）
		admin.GET("/crawlers", rm.crawlerHandler.GetCrawlers)
		admin.POST("/crawlers", rm.crawlerHandler.CreateCrawler)
		admin.GET("/crawlers/:id", rm.crawlerHandler.GetCrawler)
		admin.PUT("/crawlers/:id", rm.crawlerHandler.UpdateCrawler)
		admin.DELETE("/crawlers/:id", rm.crawlerHandler.DeleteCrawler)
		admin.POST("/crawlers/:id/run", rm.crawlerHandler.RunCrawler)
		admin.GET("/crawlers/:id/logs", rm.crawlerHandler.GetCrawlerLogs)
		admin.GET("/crawler-providers", rm.crawlerHandler.GetCrawlerTypes)
		admin.GET("/crawler-intervals", rm.crawlerHandler.GetCrawlerIntervals)
		admin.GET("/crawler-logs", rm.crawlerHandler.GetAllCrawlerLogs)
		admin.DELETE("/crawler-logs", rm.crawlerHandler.DeleteCrawlerLogs)

		// IOC情报推送记录路由
		admin.GET("/ioc-intelligence/push/records", rm.iocHandler.GetIOCIntelligencePushRecords)
		admin.DELETE("/ioc-intelligence/push/records/:id", rm.iocHandler.DeleteIOCIntelligencePushRecord)
	}
}

// setupNewIOCRoutes 设置新的IOC情报模块化路由
func (rm *RouterManager) setupNewIOCRoutes() {
	// 新的IOC情报模块化路由 - 按三个功能模块组织
	api := rm.engine.Group("/api")
	api.Use(rm.authMiddleware.JWTAuth())
	{
		// 注册IOC相关的新路由结构
		rm.iocHandler.RegisterIOCRoutes(api)
	}
}

// setupMiddleware 设置中间件
func (rm *RouterManager) setupMiddleware() {
	// 恢复中间件
	rm.engine.Use(gin.Recovery())
	
	// 日志中间件 - 使用统一的日志记录器
	rm.engine.Use(gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// 使用统一的日志记录器记录HTTP请求
		logMsg := fmt.Sprintf("HTTP请求 %s %s - 状态:%d 延迟:%s IP:%s UserAgent:%s",
			param.Method,
			param.Path,
			param.StatusCode,
			param.Latency,
			param.ClientIP,
			param.Request.UserAgent(),
		)

		if param.ErrorMessage != "" {
			logMsg += fmt.Sprintf(" 错误:%s", param.ErrorMessage)
		}

		// 根据状态码选择日志级别
		if param.StatusCode >= 500 {
			utils.Errorf(logMsg)
		} else if param.StatusCode >= 400 {
			utils.Warnf(logMsg)
		} else {
			utils.Infof(logMsg)
		}

		// 返回空字符串，因为我们已经通过utils记录了日志
		return ""
	}))
	
	// CORS中间件
	rm.engine.Use(func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, PATCH, DELETE, OPTIONS")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Origin, Authorization, Content-Type, X-API-Key, X-Request-ID")
		c.Writer.Header().Set("Access-Control-Expose-Headers", "Content-Length, Content-Type, Access-Control-Allow-Origin, X-Request-ID")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	})
	
	// 请求ID中间件
	rm.engine.Use(func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}
		
		c.Set("RequestID", requestID)
		c.Header("X-Request-ID", requestID)
		c.Next()
	})
	
	// 超时中间件
	rm.engine.Use(func(c *gin.Context) {
		// TODO: 实现超时中间件
		c.Next()
	})
}

// setupAPIRoutes 设置API路由
func (rm *RouterManager) setupAPIRoutes() {
	// 健康检查
	rm.engine.GET("/health", rm.healthCheck)
	rm.engine.GET("/ping", rm.ping)

	// 兼容性路由（前端期望的路径）
	rm.setupCompatibilityRoutes()

	// IOC情报兼容性路由
	rm.setupIOCCompatibilityRoutes()

	// 新的IOC情报模块化路由
	rm.setupNewIOCRoutes()

	// API版本1
	v1 := rm.engine.Group("/api/v1")
	{
		// 认证相关路由（无需认证）
		auth := v1.Group("/auth")
		{
			auth.POST("/login", rm.userHandler.Login)
			auth.POST("/logout", rm.userHandler.Logout)
		}
		
		// 用户相关路由
		users := v1.Group("/users")
		users.Use(rm.authMiddleware.JWTAuth()) // 启用认证中间件
		{
			users.GET("", rm.userHandler.GetUsers)
			users.POST("", rm.userHandler.CreateUser)
			users.GET("/:id", rm.userHandler.GetUser)
			users.PUT("/:id", rm.userHandler.UpdateUser)
			users.DELETE("/:id", rm.userHandler.DeleteUser)
			
			// 用户个人操作
			users.GET("/profile", rm.userHandler.GetProfile)
			users.PUT("/profile", rm.userHandler.UpdateProfile)
			users.POST("/change-password", rm.userHandler.ChangePassword)
			users.POST("/reset-api-key", rm.userHandler.ResetAPIKey)
		}
		
		// 漏洞相关路由
		vulnerabilities := v1.Group("/vulnerabilities")
		vulnerabilities.Use(rm.authMiddleware.JWTAuth()) // 启用认证中间件
		{
			vulnerabilities.GET("", rm.vulnerabilityHandler.GetVulnerabilities)
			vulnerabilities.POST("", rm.vulnerabilityHandler.CreateVulnerability)
			vulnerabilities.GET("/stats", rm.vulnerabilityHandler.GetVulnerabilityStats)
			vulnerabilities.GET("/export", rm.vulnerabilityHandler.ExportVulnerabilities)
			vulnerabilities.DELETE("/batch", rm.vulnerabilityHandler.BatchDeleteVulnerabilities)
			vulnerabilities.GET("/:id", rm.vulnerabilityHandler.GetVulnerability)
			vulnerabilities.PUT("/:id", rm.vulnerabilityHandler.UpdateVulnerability)
			vulnerabilities.DELETE("/:id", rm.vulnerabilityHandler.DeleteVulnerability)
			vulnerabilities.POST("/:id/push", rm.pushHandler.PushVulnerability)
		}
		
		// 采集器相关路由
		crawlers := v1.Group("/crawlers")
		crawlers.Use(rm.authMiddleware.JWTAuth())
		{
			crawlers.GET("", rm.crawlerHandler.GetCrawlers)
			crawlers.POST("", rm.crawlerHandler.CreateCrawler)
			crawlers.GET("/types", rm.crawlerHandler.GetCrawlerTypes)
			crawlers.GET("/intervals", rm.crawlerHandler.GetCrawlerIntervals)
			crawlers.GET("/:id", rm.crawlerHandler.GetCrawler)
			crawlers.PUT("/:id", rm.crawlerHandler.UpdateCrawler)
			crawlers.DELETE("/:id", rm.crawlerHandler.DeleteCrawler)
			crawlers.POST("/:id/run", rm.crawlerHandler.RunCrawler)
			crawlers.GET("/:id/logs", rm.crawlerHandler.GetCrawlerLogs)
		}
		
		// 推送相关路由
		push := v1.Group("/push")
		push.Use(rm.authMiddleware.JWTAuth())
		{
			// 推送通道
			channels := push.Group("/channels")
			{
				channels.GET("", rm.pushHandler.GetPushChannels)
				channels.POST("", rm.pushHandler.CreatePushChannel)
				channels.GET("/:id", rm.pushHandler.GetPushChannel)
				channels.PUT("/:id", rm.pushHandler.UpdatePushChannel)
				channels.DELETE("/:id", rm.pushHandler.DeletePushChannel)
				channels.POST("/:id/test", rm.pushHandler.TestPushChannel)
			}

			// 推送策略
			policies := push.Group("/policies")
			{
				policies.GET("", rm.pushHandler.GetPushPolicies)
				policies.GET("/:id", rm.pushHandler.GetPushPolicy)
				policies.PUT("/:id", rm.pushHandler.UpdatePushPolicy)
				// policies.POST("", rm.pushHandler.CreatePushPolicy)
				// policies.DELETE("/:id", rm.pushHandler.DeletePushPolicy)
			}

			// 推送记录
			push.GET("/records", rm.pushHandler.GetPushRecords)
			push.POST("/vulnerabilities/:id", rm.pushHandler.PushVulnerability)
		}
		
		// IOC情报相关路由
		ioc := v1.Group("/ioc")
		ioc.Use(rm.authMiddleware.JWTAuth())
		{
			// IOC情报
			intelligence := ioc.Group("/intelligence")
			{
				intelligence.GET("", rm.iocHandler.GetIOCIntelligence)
				intelligence.POST("", rm.iocHandler.CreateIOCIntelligence)
				intelligence.GET("/:id", rm.iocHandler.GetIOCIntelligenceByID)
				intelligence.PUT("/:id", rm.iocHandler.UpdateIOCIntelligence)
				intelligence.DELETE("/:id", rm.iocHandler.DeleteIOCIntelligence)
				intelligence.POST("/:id/push", rm.iocHandler.PushIOCIntelligence)
				intelligence.POST("/batch/push", rm.iocHandler.BatchPushIOCIntelligence)
			}

			// IOC白名单 (暂时注释，后续实现)
			// whitelist := ioc.Group("/whitelist")
			// {
			//     whitelist.GET("", rm.iocHandler.GetIOCWhitelist)
			//     whitelist.POST("", rm.iocHandler.CreateIOCWhitelist)
			//     whitelist.GET("/:id", rm.iocHandler.GetIOCWhitelistByID)
			//     whitelist.PUT("/:id", rm.iocHandler.UpdateIOCWhitelist)
			//     whitelist.DELETE("/:id", rm.iocHandler.DeleteIOCWhitelist)
			// }

			// 数据接口 (暂时注释，后续实现)
			// interfaces := ioc.Group("/interfaces")
			// {
			//     interfaces.GET("", rm.iocHandler.GetDataInterfaces)
			//     interfaces.POST("", rm.iocHandler.CreateDataInterface)
			//     interfaces.GET("/:id", rm.iocHandler.GetDataInterface)
			//     interfaces.PUT("/:id", rm.iocHandler.UpdateDataInterface)
			//     interfaces.DELETE("/:id", rm.iocHandler.DeleteDataInterface)
			//     interfaces.POST("/:id/run", rm.iocHandler.RunDataInterface)
			//     interfaces.GET("/:id/logs", rm.iocHandler.GetDataInterfaceLogs)
			// }
		}
		
	}

	// RSS订阅（公开访问，不需要认证）
	rm.engine.GET("/api/rss", rm.pushHandler.GenerateRSS)

	// 静态文件服务
	rm.engine.Static("/static", "./static")
	rm.engine.Static("/assets", "../frontend/dist/assets")
	rm.engine.StaticFile("/favicon.ico", "../frontend/dist/vite.svg")

	// 前端SPA路由支持
	rm.engine.StaticFile("/", "../frontend/dist/index.html")
	rm.engine.StaticFile("/index.html", "../frontend/dist/index.html")

	// 404处理 - SPA路由回退
	rm.engine.NoRoute(func(c *gin.Context) {
		// 如果是API请求，返回404
		if strings.HasPrefix(c.Request.URL.Path, "/api/") {
			rm.notFound(c)
			return
		}
		// 否则返回前端index.html，让前端路由处理
		c.File("../frontend/dist/index.html")
	})
}

// 处理器方法

// healthCheck 健康检查
func (rm *RouterManager) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
		"version":   "1.0.0",
	})
}

// ping Ping检查
func (rm *RouterManager) ping(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "pong",
	})
}



// notFound 404处理器
func (rm *RouterManager) notFound(c *gin.Context) {
	c.JSON(http.StatusNotFound, gin.H{
		"code":    404,
		"message": "接口不存在",
	})
}

// 工具函数

// generateRequestID 生成请求ID
func generateRequestID() string {
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}
