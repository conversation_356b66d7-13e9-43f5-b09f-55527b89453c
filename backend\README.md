# 漏洞情报管理平台后端

## 项目结构

经过重构后，后端项目采用了清晰的分层架构：

```
backend/
├── main.go                     # 应用入口文件
├── config.yaml                 # 配置文件
├── go.mod                      # Go模块文件
├── go.sum                      # Go依赖文件
├── internal/                   # 内部模块
│   ├── config/                 # 配置管理
│   │   └── config.go
│   ├── database/               # 数据库管理
│   │   ├── connection.go       # 数据库连接
│   │   ├── migration.go        # 数据库迁移
│   │   ├── seeder.go          # 默认数据填充
│   │   └── manager.go         # 数据库管理器
│   ├── models/                 # 数据模型
│   │   ├── user.go            # 用户模型
│   │   ├── vulnerability.go   # 漏洞模型
│   │   ├── crawler.go         # 采集器模型
│   │   ├── push.go            # 推送模型
│   │   ├── ioc.go             # IOC情报模型
│   │   └── models.go          # 模型注册
│   ├── service/                # 业务服务层
│   │   ├── base.go            # 基础服务
│   │   ├── interfaces.go      # 服务接口定义
│   │   ├── dto.go             # 数据传输对象
│   │   └── user_service.go    # 用户服务实现
│   ├── handlers/               # HTTP处理器
│   │   ├── base.go            # 基础处理器
│   │   ├── user.go            # 用户处理器
│   │   ├── vulnerability.go   # 漏洞处理器
│   │   └── router.go          # 路由管理器
│   ├── middleware/             # 中间件
│   │   ├── auth.go            # 认证中间件
│   │   └── response.go        # 响应中间件
│   └── utils/                  # 工具函数
│       ├── common.go          # 通用工具
│       ├── validator.go       # 验证工具
│       ├── http.go            # HTTP工具
│       └── logger.go          # 日志工具
├── crawlers/                   # 采集器模块（待重构）
├── push/                       # 推送模块（待重构）
├── IOC_Feed/                   # IOC数据接口（待重构）
├── GeoIP/                      # IP地理位置数据库
├── logs/                       # 日志文件目录
├── exports/                    # 导出文件目录
└── sensitive_words.txt         # 敏感词文件
```

## 架构特点

### 1. 分层架构
- **Handler层**: 处理HTTP请求和响应
- **Service层**: 业务逻辑处理
- **Repository层**: 数据访问（通过GORM）
- **Model层**: 数据模型定义

### 2. 依赖注入
- 使用接口定义服务契约
- 通过构造函数注入依赖
- 便于单元测试和模块替换

### 3. 统一的错误处理
- 标准化的错误响应格式
- 统一的验证错误处理
- 分层的错误传播机制

### 4. 配置管理
- 支持YAML配置文件
- 环境变量覆盖
- 配置验证和默认值设置

### 5. 数据库管理
- 自动化的数据库迁移
- 支持MySQL和SQLite
- 历史表迁移和数据备份

## 快速开始

### 1. 环境要求
- Go 1.19+
- MySQL 5.7+ 或 SQLite 3

### 2. 配置数据库
编辑 `config.yaml` 文件，配置数据库连接信息：

```yaml
database:
  type: "mysql"  # 或 "sqlite"
  mysql:
    host: "localhost"
    port: 3306
    user: "root"
    password: "your_password"
    database: "vuln_push"
```

### 3. 运行应用
```bash
go run main.go
```

### 4. 默认用户
系统会自动创建默认管理员用户：
- 用户名: `admin`
- 密码: `admin123`

## API文档

### 认证接口
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出

### 用户管理
- `GET /api/v1/users` - 获取用户列表
- `POST /api/v1/users` - 创建用户
- `GET /api/v1/users/:id` - 获取用户详情
- `PUT /api/v1/users/:id` - 更新用户
- `DELETE /api/v1/users/:id` - 删除用户

### 漏洞管理
- `GET /api/v1/vulnerabilities` - 获取漏洞列表
- `POST /api/v1/vulnerabilities` - 创建漏洞
- `GET /api/v1/vulnerabilities/:id` - 获取漏洞详情
- `PUT /api/v1/vulnerabilities/:id` - 更新漏洞
- `DELETE /api/v1/vulnerabilities/:id` - 删除漏洞

## 开发指南

### 1. 添加新的API接口
1. 在 `internal/service/interfaces.go` 中定义服务接口
2. 在对应的服务文件中实现接口
3. 在 `internal/handlers/` 中创建处理器
4. 在 `internal/handlers/router.go` 中注册路由

### 2. 添加新的数据模型
1. 在 `internal/models/` 中定义模型
2. 在 `internal/models/models.go` 中注册模型
3. 运行应用时会自动进行数据库迁移

### 3. 添加新的中间件
1. 在 `internal/middleware/` 中实现中间件
2. 在路由设置中应用中间件

## 部署

### 1. 编译
```bash
go build -o vuln_push main.go
```

### 2. 配置
- 复制 `config.yaml` 到部署目录
- 根据生产环境修改配置

### 3. 运行
```bash
./vuln_push
```

## 注意事项

1. **安全配置**: 生产环境中请修改JWT密钥和密码盐
2. **数据库备份**: 启用自动备份功能，定期备份数据
3. **日志管理**: 配置日志轮转，避免日志文件过大
4. **性能优化**: 根据实际负载调整数据库连接池和超时设置

## 待完成的重构

1. 采集器模块重构到新架构
2. 推送模块重构到新架构
3. IOC数据接口重构到新架构
4. 完善单元测试
5. 添加API文档生成
