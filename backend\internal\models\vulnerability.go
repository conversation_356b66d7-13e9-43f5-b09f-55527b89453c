package models

import (
	"strings"
	"time"
)

// Vulnerability 漏洞模型
type Vulnerability struct {
	ID             uint   `gorm:"primaryKey" json:"id"`
	Name           string `gorm:"size:255;not null" json:"name"`
	VulnID         string `gorm:"size:100;uniqueIndex" json:"vulnId"`
	Severity       string `gorm:"size:50;not null" json:"severity"`
	Tags           string `gorm:"size:500" json:"tags"`
	DisclosureDate string `gorm:"size:50" json:"disclosureDate"`
	PushReason     string `gorm:"type:text" json:"pushReason"`
	Source         string `gorm:"size:100" json:"source"`
	Description    string `gorm:"type:text" json:"description"`
	References     string `gorm:"type:text" json:"references"`
	Remediation    string `gorm:"type:text" json:"remediation"`
	CreatedAt      int64  `gorm:"autoCreateTime" json:"createdAt"`
}

// TableName 指定表名
func (Vulnerability) TableName() string {
	return "vulnerabilities"
}

// GetTags 获取标签数组
func (v *Vulnerability) GetTags() []string {
	if v.Tags == "" {
		return []string{}
	}
	tags := strings.Split(v.Tags, ",")
	result := make([]string, 0, len(tags))
	for _, tag := range tags {
		if trimmed := strings.TrimSpace(tag); trimmed != "" {
			result = append(result, trimmed)
		}
	}
	return result
}

// SetTags 设置标签数组
func (v *Vulnerability) SetTags(tags []string) {
	v.Tags = strings.Join(tags, ",")
}

// GetReferences 获取参考链接数组
func (v *Vulnerability) GetReferences() []string {
	if v.References == "" {
		return []string{}
	}
	refs := strings.Split(v.References, ",")
	result := make([]string, 0, len(refs))
	for _, ref := range refs {
		if trimmed := strings.TrimSpace(ref); trimmed != "" {
			result = append(result, trimmed)
		}
	}
	return result
}

// SetReferences 设置参考链接数组
func (v *Vulnerability) SetReferences(references []string) {
	v.References = strings.Join(references, ",")
}

// GetSeverityLevel 获取严重程度等级（用于排序）
func (v *Vulnerability) GetSeverityLevel() int {
	switch v.Severity {
	case "严重":
		return 5
	case "高危":
		return 4
	case "中危":
		return 3
	case "低危":
		return 2
	case "信息":
		return 1
	default:
		return 0
	}
}

// IsHighRisk 是否为高风险漏洞
func (v *Vulnerability) IsHighRisk() bool {
	return v.Severity == "严重" || v.Severity == "高危"
}

// GetCreatedTime 获取创建时间
func (v *Vulnerability) GetCreatedTime() time.Time {
	return time.Unix(v.CreatedAt, 0)
}

// HasCVE 检查是否包含CVE编号
func (v *Vulnerability) HasCVE() bool {
	tags := v.GetTags()
	for _, tag := range tags {
		if strings.HasPrefix(strings.ToUpper(tag), "CVE-") {
			return true
		}
	}
	return false
}

// GetCVEs 获取所有CVE编号
func (v *Vulnerability) GetCVEs() []string {
	tags := v.GetTags()
	cves := make([]string, 0)
	for _, tag := range tags {
		if strings.HasPrefix(strings.ToUpper(tag), "CVE-") {
			cves = append(cves, tag)
		}
	}
	return cves
}

// VulnInfo 漏洞信息结构（从crawlers包复制，避免循环导入）
type VulnInfo struct {
	UniqueKey   string   // 唯一标识，如CVE编号或其他ID
	Title       string   // 漏洞名称
	Description string   // 漏洞描述
	Severity    string   // 危害等级
	CVE         string   // CVE编号
	CWE         string   // CWE编号，可能有多个，用逗号分隔
	VulnType    string   // 漏洞类型，可能有多个，用逗号分隔
	Score       string   // CVSS评分
	Disclosure  string   // 披露日期
	References  []string // 参考链接
	From        string   // 来源URL
	Tags        []string // 标签
	Remediation string   // 修复建议
}

// SetFromVulnInfo 从VulnInfo设置漏洞信息
func (v *Vulnerability) SetFromVulnInfo(info *VulnInfo) {
	v.Name = info.Title
	v.VulnID = info.UniqueKey
	v.Severity = info.Severity
	v.Description = info.Description
	v.DisclosureDate = info.Disclosure
	v.Remediation = info.Remediation
	v.Source = info.From

	// 设置标签 - 参考V1版本的逻辑，确保去重
	tags := make([]string, 0)

	// 首先添加来自采集器的标签
	if info.Tags != nil {
		// 检查是否是阿里云来源，如果是则只保留CVE和CWE标签
		if strings.Contains(info.From, "aliyun.com") {
			for _, tag := range info.Tags {
				if strings.HasPrefix(tag, "CVE-") || strings.HasPrefix(tag, "CWE-") {
					if !v.containsTag(tags, tag) {
						tags = append(tags, tag)
					}
				}
			}
		} else {
			// 其他来源，保留所有标签
			for _, tag := range info.Tags {
				if !v.containsTag(tags, tag) {
					tags = append(tags, tag)
				}
			}
		}
	}

	// 如果有CVE但不在标签中，添加到标签
	if info.CVE != "" && !v.containsTag(tags, info.CVE) {
		tags = append(tags, info.CVE)
	}

	// 如果有CWE但不在标签中，添加到标签
	if info.CWE != "" {
		cweList := strings.Split(info.CWE, ",")
		for _, cwe := range cweList {
			cwe = strings.TrimSpace(cwe)
			if cwe != "" && !v.containsTag(tags, cwe) {
				tags = append(tags, cwe)
			}
		}
	}

	// 如果有漏洞类型但不在标签中，添加到标签
	if info.VulnType != "" && !v.containsTag(tags, info.VulnType) {
		tags = append(tags, info.VulnType)
	}

	// 如果有CVSS评分但不在标签中，添加到标签
	if info.Score != "" {
		cvssTag := "CVSS:" + info.Score
		if !v.containsTag(tags, cvssTag) {
			tags = append(tags, cvssTag)
		}
	}

	v.SetTags(tags)

	// 设置参考链接
	v.SetReferences(info.References)
}

// containsTag 检查标签是否包含特定值
func (v *Vulnerability) containsTag(tags []string, tag string) bool {
	for _, t := range tags {
		if t == tag {
			return true
		}
	}
	return false
}
