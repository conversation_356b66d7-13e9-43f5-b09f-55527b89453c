package handlers

import (
	"crypto/tls"
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
	"vulnerability_push/internal/models"
)

// SearchDataRecord CCCC黑科技API返回的数据记录
type SearchDataRecord struct {
	UUID       string `json:"uuid"`
	SrcIP      string `json:"src_ip"`
	DstIP      string `json:"dst_ip"`
	SrcPort    int    `json:"src_port"`
	DstPort    int    `json:"dst_port"`
	AppProto   string `json:"app_proto"`
	Category   string `json:"category"`
	Severity   string `json:"severity"`
	IOC        string `json:"ioc"`
	Desc       string `json:"desc"`
	Timestamp  int64  `json:"timestamp"`
	SrcLabel   string `json:"src_label"`
	DstLabel   string `json:"dst_label"`
}

// SearchDataResponse CCCC黑科技API响应（与原始版本保持一致）
type SearchDataResponse struct {
	Total           int                `json:"total"`
	Size            int                `json:"size"`
	Current         int                `json:"current"`
	Page            int                `json:"page"`
	Records         []SearchDataRecord `json:"records"`
	SearchHistoryID interface{}        `json:"searchHistoryId"`
}

// executeCCCCBlackTechInterface 执行CCCC黑科技接口采集
func (h *DataInterfaceHandler) executeCCCCBlackTechInterface(dataInterface *models.DataInterface, config map[string]interface{}, log *models.DataInterfaceLog) {
	// 获取配置参数
	host, ok := config["host"].(string)
	if !ok || host == "" {
		h.updateExecutionLog(log, "failed", "缺少主机地址配置")
		h.updateDataInterfaceStats(dataInterface, "failed", "缺少主机地址配置", 0)
		return
	}

	userKey, ok := config["user_key"].(string)
	if !ok || userKey == "" {
		h.updateExecutionLog(log, "failed", "缺少用户密钥配置")
		h.updateDataInterfaceStats(dataInterface, "failed", "缺少用户密钥配置", 0)
		return
	}

	// 获取时间范围配置
	timeRangeValue := dataInterface.TimeRangeValue
	if timeRangeValue <= 0 {
		timeRangeValue = 3600 // 默认1小时
	}

	// 计算时间范围
	endTime := time.Now().UnixMilli()
	startTime := endTime - int64(timeRangeValue)*1000

	fmt.Printf("CCCC黑科技接口采集 - 主机: %s, 时间范围: %s 到 %s\n",
		host,
		time.Unix(startTime/1000, 0).Format("2006-01-02 15:04:05"),
		time.Unix(endTime/1000, 0).Format("2006-01-02 15:04:05"))

	// 更新日志状态
	log.Message = "正在调用CCCC黑科技API获取数据..."
	h.db.Save(log)

	// 调用CCCC黑科技API
	records, err := h.callCCCCBlackTechAPI(host, userKey, startTime, endTime)
	if err != nil {
		h.updateExecutionLog(log, "failed", fmt.Sprintf("API调用失败: %v", err))
		h.updateDataInterfaceStats(dataInterface, "failed", fmt.Sprintf("API调用失败: %v", err), 0)
		return
	}

	// 输出原始API数据样本
	fmt.Printf("\n=== API原始数据样本 ===\n")
	for i, record := range records {
		if i >= 5 { // 只显示前5条
			break
		}
		fmt.Printf("记录 %d: UUID=%s, SrcIP=%s, DstIP=%s, Category=%s, Severity=%s, IOC=%s, Timestamp=%s\n",
			i+1, record.UUID, record.SrcIP, record.DstIP, record.Category, record.Severity, record.IOC, record.Timestamp)
	}
	if len(records) > 5 {
		fmt.Printf("... 还有 %d 条记录\n", len(records)-5)
	}

	// 处理和保存数据
	processedCount, err := h.processCCCCBlackTechData(records)
	if err != nil {
		h.updateExecutionLog(log, "failed", fmt.Sprintf("数据处理失败: %v", err))
		h.updateDataInterfaceStats(dataInterface, "failed", fmt.Sprintf("数据处理失败: %v", err), 0)
		return
	}

	// 更新成功日志
	successMessage := fmt.Sprintf("采集完成，获取 %d 条原始记录，处理后生成 %d 条IOC情报源数据", len(records), processedCount)
	h.updateExecutionLogWithDataCount(log, "success", successMessage, processedCount)

	// 更新数据接口统计信息
	h.updateDataInterfaceStats(dataInterface, "success", successMessage, processedCount)

	fmt.Printf("CCCC黑科技接口采集完成: %s\n", successMessage)
}

// callCCCCBlackTechAPI 调用CCCC黑科技API获取数据
func (h *DataInterfaceHandler) callCCCCBlackTechAPI(host, userKey string, startTime, endTime int64) ([]SearchDataRecord, error) {
	var allRecords []SearchDataRecord
	page := 1
	size := 100 // 每页获取100条记录

	fmt.Printf("开始调用CCCC黑科技API，每页 %d 条记录\n", size)

	for {
		fmt.Printf("正在获取第 %d 页数据\n", page)

		result, err := h.searchAdvancedDataPage(host, userKey, startTime, endTime, page, size)
		if err != nil {
			return nil, fmt.Errorf("获取第%d页数据失败: %v", page, err)
		}

		// 添加当前页的记录
		allRecords = append(allRecords, result.Records...)

		fmt.Printf("第 %d 页获取到 %d 条记录，累计 %d 条\n", page, len(result.Records), len(allRecords))

		// 检查是否还有更多数据
		if len(result.Records) < size || len(allRecords) >= result.Total {
			break
		}

		page++
	}

	fmt.Printf("API调用完成，总计获取 %d 条记录\n", len(allRecords))
	return allRecords, nil
}

// SearchDataRequest 搜索数据请求结构（与V1版本保持一致）
type SearchDataRequest struct {
	Severity         string   `json:"severity"`
	Category         string   `json:"category"`
	SrcIP           string   `json:"src_ip"`
	DstIP           string   `json:"dst_ip"`
	EmailFrom       string   `json:"email_from"`
	To              string   `json:"to"`
	AttackStatus    string   `json:"attack_status"`
	AppProto        string   `json:"app_proto"`
	Method          string   `json:"method"`
	StatusCode      string   `json:"status_code"`
	MD5             string   `json:"md5"`
	XFF             string   `json:"xff"`
	Domain          string   `json:"domain"`
	Username        string   `json:"username"`
	VisitDirection  string   `json:"visit_direction"`
	IOC             string   `json:"ioc"`
	AppendQuery     string   `json:"appendQuery"`
	Start           int64    `json:"start"`
	End             int64    `json:"end"`
	QuickQuery      string   `json:"quickQuery"`
	FilterQuery     string   `json:"filterQuery"`
	IsCollectHistory bool    `json:"isCollectHistory"`
	QueryFields     []string `json:"queryFields"`
	From            int      `json:"from"`
	Size            int      `json:"size"`
	IsDeal          string   `json:"is_deal"`
	Sort            string   `json:"sort"`
	Order           string   `json:"order"`
	PageQueryMode   string   `json:"pageQueryMode"`
	Groups          []string `json:"groups"`
	TableName       string   `json:"tableName"`
}

// searchAdvancedDataPage 获取单页数据
func (h *DataInterfaceHandler) searchAdvancedDataPage(host, userKey string, startTime, endTime int64, page, size int) (*SearchDataResponse, error) {
	// 创建HTTP客户端，跳过SSL验证
	client := resty.New()
	client.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	client.SetTimeout(30 * time.Second)

	// 构建请求数据（使用与V1版本相同的结构）
	requestData := SearchDataRequest{
		Severity:         "",
		Category:         "",
		SrcIP:           "",
		DstIP:           "",
		EmailFrom:       "",
		To:              "",
		AttackStatus:    "",
		AppProto:        "",
		Method:          "",
		StatusCode:      "",
		MD5:             "",
		XFF:             "",
		Domain:          "",
		Username:        "",
		VisitDirection:  "",
		IOC:             "",
		AppendQuery:     "",
		Start:           startTime,
		End:             endTime,
		QuickQuery:      "",
		FilterQuery:     "",
		IsCollectHistory: false,
		QueryFields: []string{
			"xff", "src_ip_country", "src_ip_city", "attacker", "victim",
			"src_ip_hostName", "src_ip_isHost", "host", "server_name",
			"dst_ip_country", "dst_ip_city", "dst_ip_hostName", "dst_ip_isHost",
			"dst_port", "method", "uri", "status_code", "user_agent", "query",
			"answers", "qtype_name", "email_from", "to", "attachment_names",
			"md5", "platform", "username", "password", "timestamp", "src_ip",
			"dst_ip", "app_proto", "category", "severity", "ioc", "attack_status",
			"desc", "rid",
		},
		From:          page,
		Size:          size,
		IsDeal:        "",
		Sort:          "",
		Order:         "",
		PageQueryMode: "DATA",
		Groups:        []string{"uuid"},
		TableName:     "event_data",
	}

	// 发送POST请求
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("user-key", userKey).
		SetBody(requestData).
		SetResult(&SearchDataResponse{}).
		Post(fmt.Sprintf("https://%s/workbenchApi/furious/searchCenter/advanced/searchData", host))

	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API返回错误状态码: %d, 响应: %s", resp.StatusCode(), resp.String())
	}

	result, ok := resp.Result().(*SearchDataResponse)
	if !ok {
		return nil, fmt.Errorf("响应解析失败")
	}

	return result, nil
}

// processCCCCBlackTechData 处理CCCC黑科技数据并保存到IOC情报源数据库
func (h *DataInterfaceHandler) processCCCCBlackTechData(records []SearchDataRecord) (int, error) {
	// 使用新的去重逻辑：先UUID去重，再攻击流合并
	attackSummaries, internalCount, whitelistCount, filteredCount := h.mergeAttackDataWithUUIDDedup(records)

	fmt.Printf("数据处理统计 - 原始记录: %d, 内网流量: %d, 白名单: %d, 非目标组织: %d, 最终合并记录: %d\n",
		len(records), internalCount, whitelistCount, filteredCount, len(attackSummaries))

	// 输出处理后的攻击统计样本
	fmt.Printf("\n=== 处理后的攻击统计样本 ===\n")
	count := 0
	for key, summary := range attackSummaries {
		if count >= 5 { // 只显示前5条
			break
		}
		fmt.Printf("攻击流 %d: Key=%s\n", count+1, key)
		fmt.Printf("  攻击IP=%s, 受害IP=%s, 来源标签=%s, 受害标签=%s\n",
			summary.SrcIP, summary.DstIP, summary.SrcLabel, summary.DstLabel)
		fmt.Printf("  攻击次数=%d, UUID数=%d, 受害IP数=%d\n",
			summary.AttackCount, len(summary.UUIDs), len(summary.DstIPs))
		fmt.Printf("  主要类别=%v, 严重程度=%v\n", summary.Categories, summary.Severities)
		fmt.Printf("  时间范围: %s 到 %s\n", summary.FirstSeen, summary.LastSeen)
		fmt.Printf("  UUID列表: %v\n", summary.UUIDs)
		fmt.Printf("  受害IP列表: %v\n", summary.DstIPs)
		fmt.Printf("  受害标签列表: %v\n", summary.DstLabels)
		fmt.Printf("---\n")
		count++
	}
	if len(attackSummaries) > 5 {
		fmt.Printf("... 还有 %d 条攻击流\n", len(attackSummaries)-5)
	}

	// 保存到IOC情报源数据库
	savedCount := 0
	var allProcessedUUIDs []string

	for _, summary := range attackSummaries {
		if err := h.saveAttackSummaryToIOC(summary); err != nil {
			fmt.Printf("保存攻击统计失败: %v\n", err)
			continue
		}
		savedCount++

		// 收集成功保存的UUID
		allProcessedUUIDs = append(allProcessedUUIDs, summary.UUIDs...)
	}

	// 保存所有成功处理的UUID到去重表
	if len(allProcessedUUIDs) > 0 {
		if err := h.saveProcessedUUIDs(allProcessedUUIDs, "cccc_black_tech"); err != nil {
			fmt.Printf("保存UUID到去重表失败: %v\n", err)
			// 不返回错误，因为主要数据已经保存成功
		}
	}

	return savedCount, nil
}
