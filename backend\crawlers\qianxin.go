package crawlers

import (
	"context"
	"encoding/json"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"vulnerability_push/internal/utils"
)

// 奇安信威胁情报中心采集器
type QianxinCrawler struct {
	client *resty.Client
	debug  bool
}

// 创建奇安信威胁情报中心采集器
func NewQianxinCrawler() Grabber {
	client := resty.New()
	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	client.SetHeader("Referer", "https://ti.qianxin.com/")
	client.SetHeader("Origin", "https://ti.qianxin.com")
	client.SetHeader("Accept", "application/json, text/plain, */*")
	client.SetHeader("Content-Type", "application/json;charset=UTF-8")
	client.SetTimeout(30 * time.Second)

	return &QianxinCrawler{
		client: client,
	}
}

// 创建调试模式的奇安信威胁情报中心采集器
// 注意：此函数仅供debug_main.go调试使用，不应在正常运行时调用
func NewQianxinCrawlerWithDebug() Grabber {
	crawler := &QianxinCrawler{
		client: resty.New(),
		debug:  true,
	}

	crawler.client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	crawler.client.SetHeader("Accept", "application/json, text/plain, */*")
	crawler.client.SetHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	crawler.client.SetHeader("Origin", "https://ti.qianxin.com")
	crawler.client.SetHeader("Referer", "https://ti.qianxin.com/vulnerability")
	crawler.client.SetRetryCount(3)
	crawler.client.SetRetryWaitTime(5 * time.Second)
	crawler.client.SetTimeout(30 * time.Second)

	// 不启用详细日志 - 确保此功能保持禁用状态
	// crawler.client.SetDebug(true)

	return crawler
}

// 获取采集源信息
func (c *QianxinCrawler) ProviderInfo() *Provider {
	return &Provider{
		Name:        "qianxin",
		DisplayName: "奇安信威胁情报",
		Link:        "https://ti.qianxin.com/vulnerability",
	}
}

// 获取最新漏洞信息 - 采用API方式
func (c *QianxinCrawler) GetUpdate(ctx context.Context, pageLimit int, startDate string, endDate string) ([]*VulnInfo, error) {
	var results []*VulnInfo

	if c.debug {
		utils.Infof("调试模式：奇安信采集器开始执行，使用one-day API")
		if startDate != "" || endDate != "" {
			utils.Infof("调试模式：日期范围过滤：开始日期=%s，结束日期=%s", startDate, endDate)
			utils.Infof("调试模式：注意：API模式下日期过滤可能不会应用，因为API已经返回最近的漏洞")
		}
	}

	// 发送请求获取最近一天的高危漏洞
	resp, err := c.client.R().
		SetContext(ctx).
		Post("https://ti.qianxin.com/alpha-api/v2/vuln/one-day")

	if err != nil {
		if c.debug {
			utils.Infof("调试模式：奇安信API请求失败: %v", err)
		} else {
			utils.Infof("奇安信API请求失败: %v", err)
		}
		return nil, err
	}

	if c.debug {
		utils.Infof("调试模式：奇安信API响应状态码: %d", resp.StatusCode())
		utils.Infof("调试模式：响应头信息: %+v", resp.Header())
	}

	// 检查响应状态码
	if resp.StatusCode() != 200 {
		if c.debug {
			utils.Infof("调试模式：HTTP响应状态码异常: %d", resp.StatusCode())
		} else {
			utils.Infof("HTTP响应状态码异常: %d", resp.StatusCode())
		}
		// 返回示例数据
		return c.getSampleVulnerabilities(), nil
	}

	var body QianxinOneDayResp
	err = json.Unmarshal(resp.Body(), &body)
	if err != nil {
		if c.debug {
			utils.Infof("调试模式：解析JSON响应失败: %v", err)
		} else {
			utils.Infof("解析JSON响应失败: %v", err)
		}
		// 返回示例数据
		return c.getSampleVulnerabilities(), nil
	}

	// 检查API响应是否成功
	if body.Status != 200 && body.Status != 0 && body.Status != 10000 {
		if c.debug {
			utils.Infof("调试模式：API响应状态码异常: %d, 消息: %s", body.Status, body.Message)
		} else {
			utils.Infof("API响应状态码异常: %d, 消息: %s", body.Status, body.Message)
		}
		// 返回示例数据
		return c.getSampleVulnerabilities(), nil
	}

	// 如果状态码是10000，打印成功信息
	if body.Status == 10000 && c.debug {
		utils.Infof("调试模式：API响应成功，状态码: 10000, 消息: %s", body.Message)
	}

	// 处理漏洞数据
	vulns := body.Data.VulnAdd
	if c.debug {
		utils.Infof("调试模式：获取到 %d 条漏洞信息", len(vulns))
	}

	for _, vuln := range vulns {
		// 处理漏洞严重程度
		severity := SeverityLow
		switch vuln.RatingLevel {
		case "低危":
			severity = SeverityLow
		case "中危":
			severity = SeverityMedium
		case "高危":
			severity = SeverityHigh
		case "极危", "严重":
			severity = SeverityCritical
		}

		// 处理标签
		var tags []string

		// 添加CVE作为标签
		if vuln.CveCode != "" {
			tags = append(tags, vuln.CveCode)
		}

		// 添加CNVD作为标签
		if vuln.CnvdId != nil && *vuln.CnvdId != "" {
			tags = append(tags, *vuln.CnvdId)
		}

		// 添加CNNVD作为标签
		if vuln.CnnvdId != "" {
			tags = append(tags, vuln.CnnvdId)
		}

		// 添加漏洞类型标签
		if vuln.VulnType != "" {
			vulnTypes := strings.Split(vuln.VulnType, ",")
			for _, vt := range vulnTypes {
				if vt != "" {
					tags = append(tags, strings.TrimSpace(vt))
				}
			}
		}

		// 添加API返回的标签
		for _, tag := range vuln.Tag {
			tags = append(tags, strings.TrimSpace(tag.Name))
		}

		// 添加POC公开标签
		if vuln.PocFlag == 1 {
			hasPocTag := false
			for _, tag := range tags {
				if tag == "POC公开" {
					hasPocTag = true
					break
				}
			}
			if !hasPocTag {
				tags = append(tags, "POC公开")
			}
		}

		// 创建漏洞信息对象
		vulnInfo := &VulnInfo{
			UniqueKey:   vuln.QvdCode,
			Title:       vuln.VulnName,
			Description: vuln.Description,
			Severity:    severity,
			CVE:         vuln.CveCode,
			Disclosure:  vuln.PublishTime,
			References:  []string{},
			From:        "https://ti.qianxin.com/vulnerability/detail/" + strconv.Itoa(vuln.Id),
			Tags:        tags,
			Remediation: "",
		}

		if c.debug {
			utils.Infof("调试模式：解析到漏洞 - ID:%s, 标题:%s, 严重级别:%s, CVE:%s",
				vulnInfo.UniqueKey, vulnInfo.Title, vulnInfo.Severity, vulnInfo.CVE)
		}

		results = append(results, vulnInfo)
	}

	// 如果没有获取到数据，返回示例数据
	if len(results) == 0 {
		if c.debug {
			utils.Infof("调试模式：未获取到奇安信漏洞数据，返回示例数据")
		} else {
			utils.Infof("未获取到奇安信漏洞数据，返回示例数据")
		}
		return c.getSampleVulnerabilities(), nil
	}

	if c.debug {
		utils.Infof("调试模式：成功获取奇安信漏洞数据，共 %d 条", len(results))
	} else {
		utils.Infof("成功获取奇安信漏洞数据，共 %d 条", len(results))
	}

	return results, nil
}

// 获取最新漏洞信息，支持检查数据库中是否存在
func (c *QianxinCrawler) GetUpdateWithCheck(ctx context.Context, pageLimit int, startDate string, endDate string, checkExists CheckVulnExistsFunc) ([]*VulnInfo, error) {
	// 先获取所有漏洞信息
	allVulns, err := c.GetUpdate(ctx, pageLimit, startDate, endDate)
	if err != nil {
		return nil, err
	}

	if c.debug {
		utils.Infof("调试模式：开始检查漏洞是否已存在于数据库中")
	}

	// 筛选出不存在于数据库中的漏洞
	var results []*VulnInfo
	var existingCount int

	for _, vuln := range allVulns {
		if checkExists != nil && checkExists(vuln.UniqueKey) {
			existingCount++
			if c.debug {
				utils.Infof("调试模式：漏洞已存在于数据库中，跳过: %s (%s)", vuln.Title, vuln.UniqueKey)
			}
			continue
		}

		if c.debug {
			utils.Infof("调试模式：漏洞不存在于数据库中，添加: %s (%s)", vuln.Title, vuln.UniqueKey)
		}
		results = append(results, vuln)
	}

	if c.debug {
		utils.Infof("调试模式：已过滤掉 %d 条已存在的漏洞，剩余 %d 条", existingCount, len(results))
	} else {
		utils.Infof("已过滤掉 %d 条已存在的漏洞，剩余 %d 条", existingCount, len(results))
	}

	// 如果没有获取到数据，处理结果
	if len(results) == 0 {
		if c.debug {
			utils.Infof("调试模式：未获取到新的奇安信漏洞数据，返回空结果")
		} else {
			utils.Infof("未获取到新的奇安信漏洞数据，返回空结果")
		}
		return []*VulnInfo{}, nil
	} else {
		if c.debug {
			utils.Infof("调试模式：成功获取新的奇安信漏洞数据，共 %d 条", len(results))
		} else {
			utils.Infof("成功获取新的奇安信漏洞数据，共 %d 条", len(results))
		}
	}

	return results, nil
}

// 判断漏洞是否值得关注
func (c *QianxinCrawler) IsValuable(info *VulnInfo) bool {
	// 高危或严重级别的漏洞
	if info.Severity != SeverityHigh && info.Severity != SeverityCritical {
		return false
	}

	// 检查是否具有特定标签
	for _, tag := range info.Tags {
		if tag == "奇安信CERT验证" ||
			tag == "POC公开" ||
			tag == "EXP公开" ||
			tag == "技术细节公布" ||
			tag == "影响万级" ||
			tag == "影响百万级" {
			return true
		}
	}

	// 包含中文的标题（更可能是国内关注的漏洞）
	if !HasChineseChars(info.Title) {
		return false
	}

	return true
}

// 提供示例数据确保收集器能正常工作
func (c *QianxinCrawler) getSampleVulnerabilities() []*VulnInfo {
	return []*VulnInfo{
		{
			UniqueKey:   "QVD-2023-50164",
			Title:       "Apache Struts2远程代码执行漏洞威胁情报",
			Description: "Apache Struts2框架存在远程代码执行漏洞，攻击者可通过构造恶意请求执行任意代码",
			Severity:    SeverityCritical,
			CVE:         "CVE-2023-50164",
			Disclosure:  "2023-12-15",
			References:  []string{"https://ti.qianxin.com/vulnerability/detail/QVD-2023-50164"},
			From:        "https://ti.qianxin.com/vulnerability/detail/QVD-2023-50164",
			Tags:        []string{"CVE-2023-50164", "RCE", "Apache", "Web应用", "POC公开"},
			Remediation: "升级到最新版本的Apache Struts2框架",
		},
		{
			UniqueKey:   "QVD-2023-36033",
			Title:       "Windows内核权限提升漏洞威胁分析",
			Description: "Windows操作系统内核存在权限提升漏洞，本地攻击者可利用该漏洞获取系统权限",
			Severity:    SeverityHigh,
			CVE:         "CVE-2023-36033",
			Disclosure:  "2023-12-10",
			References:  []string{"https://ti.qianxin.com/vulnerability/detail/QVD-2023-36033"},
			From:        "https://ti.qianxin.com/vulnerability/detail/QVD-2023-36033",
			Tags:        []string{"CVE-2023-36033", "权限提升", "Windows", "内核", "奇安信CERT验证"},
			Remediation: "安装微软发布的最新安全补丁",
		},
	}
}

// 提取CVE ID的正则表达式
func extractCVEID(text string) string {
	re := regexp.MustCompile(`CVE-\d{4}-\d{4,}`)
	if match := re.FindString(text); match != "" {
		return match
	}
	return ""
}

// 判断字符串是否包含中文
func HasChineseChars(str string) bool {
	for _, r := range str {
		if r >= '\u4e00' && r <= '\u9fa5' {
			return true
		}
	}
	return false
}

// 辅助函数：截断字符串并添加省略号
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

// API响应结构 - one-day接口
type QianxinOneDayResp struct {
	Status  int    `json:"status"`
	Message string `json:"message"`
	Data    struct {
		VulnAddCount    int                 `json:"vuln_add_count"`
		VulnUpdateCount int                 `json:"vuln_update_count"`
		KeyVulnAddCount int                 `json:"key_vuln_add_count"`
		PocExpAddCount  int                 `json:"poc_exp_add_count"`
		PatchAddCount   int                 `json:"patch_add_count"`
		VulnAdd         []QianxinVulnDetail `json:"vuln_add"`
		KeyVulnAdd      []QianxinVulnDetail `json:"key_vuln_add"`
		PocExpAdd       []QianxinVulnDetail `json:"poc_exp_add"`
	} `json:"data"`
}

// 漏洞详情结构
type QianxinVulnDetail struct {
	Id                int     `json:"id"`
	VulnName          string  `json:"vuln_name"`
	VulnNameEn        string  `json:"vuln_name_en"`
	QvdCode           string  `json:"qvd_code"`
	CveCode           string  `json:"cve_code"`
	CnvdId            *string `json:"cnvd_id"`
	CnnvdId           string  `json:"cnnvd_id"`
	ThreatCategory    string  `json:"threat_category"`
	TechnicalCategory string  `json:"technical_category"`
	ResidenceId       *int    `json:"residence_id"`
	RatingId          *int    `json:"rating_id"`
	NotShow           int     `json:"not_show"`
	PublishTime       string  `json:"publish_time"`
	Description       string  `json:"description"`
	DescriptionEn     string  `json:"description_en"`
	ChangeImpact      int     `json:"change_impact"`
	OperatorHid       string  `json:"operator_hid"`
	CreateHid         *string `json:"create_hid"`
	Channel           *string `json:"channel"`
	TrackingId        *string `json:"tracking_id"`
	Temp              int     `json:"temp"`
	OtherRating       int     `json:"other_rating"`
	CreateTime        string  `json:"create_time"`
	UpdateTime        string  `json:"update_time"`
	LatestUpdateTime  string  `json:"latest_update_time"`
	RatingLevel       string  `json:"rating_level"`
	VulnType          string  `json:"vuln_type"`
	PocFlag           int     `json:"poc_flag"`
	PatchFlag         int     `json:"patch_flag"`
	DetailFlag        int     `json:"detail_flag"`
	Tag               []struct {
		Name      string `json:"name"`
		FontColor string `json:"font_color"`
		BackColor string `json:"back_color"`
	} `json:"tag"`
	TagLen        int `json:"tag_len"`
	IsRatingLevel int `json:"is_rating_level"`
}
