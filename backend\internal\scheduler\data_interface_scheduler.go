package scheduler

import (
	"fmt"
	"sync"
	"time"

	"gorm.io/gorm"
	"vulnerability_push/internal/handlers"
	"vulnerability_push/internal/models"
	"vulnerability_push/internal/utils"
)

// DataInterfaceScheduler 数据接口调度器
type DataInterfaceScheduler struct {
	db      *gorm.DB
	handler *handlers.DataInterfaceHandler
	timers  map[uint]*time.Timer
	mutex   sync.RWMutex
	running bool
}

// NewDataInterfaceScheduler 创建数据接口调度器
func NewDataInterfaceScheduler(db *gorm.DB) *DataInterfaceScheduler {
	return &DataInterfaceScheduler{
		db:      db,
		handler: handlers.NewDataInterfaceHandler(db),
		timers:  make(map[uint]*time.Timer),
		running: false,
	}
}

// Start 启动调度器
func (s *DataInterfaceScheduler) Start() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.running {
		return
	}

	s.running = true
	utils.Infof("数据接口调度器启动")

	// 加载所有启用的数据接口
	s.loadEnabledInterfaces()

	// 启动定期检查任务（每分钟检查一次）
	go s.periodicCheck()
}

// Stop 停止调度器
func (s *DataInterfaceScheduler) Stop() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.running {
		return
	}

	s.running = false
	utils.Infof("数据接口调度器停止")

	// 停止所有定时器
	for id, timer := range s.timers {
		timer.Stop()
		delete(s.timers, id)
	}
}

// loadEnabledInterfaces 加载所有启用的数据接口
func (s *DataInterfaceScheduler) loadEnabledInterfaces() {
	utils.Infof("开始加载启用的数据接口...")

	var interfaces []models.DataInterface
	err := s.db.Where("status = ? AND collection_enabled = ?", "enabled", true).Find(&interfaces).Error
	if err != nil {
		utils.Errorf("加载启用的数据接口失败: %v", err)
		return
	}

	utils.Infof("加载到 %d 个启用的数据接口", len(interfaces))

	for i, iface := range interfaces {
		utils.Debugf("正在调度第 %d 个接口: %s (ID: %d, Freq: %s, Interval: %d, Enabled: %t)",
			i+1, iface.Name, iface.ID, iface.CollectionFreq, iface.CollectionInterval, iface.CollectionEnabled)
		s.scheduleInterface(&iface)
		utils.Debugf("第 %d 个接口调度完成", i+1)
	}

	utils.Infof("所有接口调度完成")
}

// scheduleInterface 为数据接口安排定时任务
func (s *DataInterfaceScheduler) scheduleInterface(iface *models.DataInterface) {
	utils.Debugf("开始调度接口: %s", iface.Name)

	utils.Debugf("停止现有定时器")

	// 停止现有的定时器（暂时不使用锁）
	if timer, exists := s.timers[iface.ID]; exists {
		timer.Stop()
		delete(s.timers, iface.ID)
		utils.Debugf("停止了现有定时器")
	}

	utils.Debugf("开始计算采集间隔")

	// 计算下次执行时间
	interval := s.calculateInterval(iface)
	utils.Debugf("计算得到间隔: %v", interval)

	if interval <= 0 {
		utils.Warnf("数据接口 %s 的采集间隔无效，跳过调度", iface.Name)
		return
	}

	utils.Debugf("创建定时器")

	// 创建新的定时器
	timer := time.AfterFunc(interval, func() {
		s.executeInterface(iface)
	})

	s.timers[iface.ID] = timer

	utils.Infof("数据接口 %s 已安排定时任务，下次执行时间: %s",
		iface.Name, time.Now().Add(interval).Format("2006-01-02 15:04:05"))
}

// calculateInterval 计算采集间隔
func (s *DataInterfaceScheduler) calculateInterval(iface *models.DataInterface) time.Duration {
	switch iface.CollectionFreq {
	case "hourly":
		return time.Hour
	case "daily":
		return 24 * time.Hour
	case "weekly":
		return 7 * 24 * time.Hour
	case "custom":
		// 使用 CollectionInterval 字段（1-3600分钟）
		if iface.CollectionInterval >= 1 && iface.CollectionInterval <= 3600 {
			fmt.Printf("数据接口 %s 使用自定义间隔: %d分钟\n", iface.Name, iface.CollectionInterval)
			return time.Duration(iface.CollectionInterval) * time.Minute
		}
		// 如果自定义间隔无效，使用默认60分钟
		fmt.Printf("数据接口 %s 的自定义间隔配置无效 (%d)，必须是1-3600之间的数字，使用默认60分钟\n", iface.Name, iface.CollectionInterval)
		return 60 * time.Minute
	default:
		// 默认使用1小时间隔
		fmt.Printf("数据接口 %s 的采集频率未设置，使用默认1小时间隔\n", iface.Name)
		return time.Hour
	}
}

// executeInterface 执行数据接口采集
func (s *DataInterfaceScheduler) executeInterface(iface *models.DataInterface) {
	fmt.Printf("开始执行定时采集: %s (ID: %d)\n", iface.Name, iface.ID)

	// 重新从数据库加载接口信息（确保是最新的）
	var currentInterface models.DataInterface
	err := s.db.First(&currentInterface, iface.ID).Error
	if err != nil {
		fmt.Printf("加载数据接口失败: %v\n", err)
		return
	}

	// 检查接口是否仍然启用
	if currentInterface.Status != "enabled" || !currentInterface.CollectionEnabled {
		fmt.Printf("数据接口 %s 已被禁用，停止定时采集\n", currentInterface.Name)
		s.mutex.Lock()
		if timer, exists := s.timers[currentInterface.ID]; exists {
			timer.Stop()
			delete(s.timers, currentInterface.ID)
		}
		s.mutex.Unlock()
		return
	}

	// 执行采集
	go func() {
		s.handler.ExecuteDataInterface(&currentInterface)
		
		// 重新安排下次执行
		if s.running {
			s.scheduleInterface(&currentInterface)
		}
	}()
}

// periodicCheck 定期检查任务（每分钟执行一次）
func (s *DataInterfaceScheduler) periodicCheck() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if !s.running {
				return
			}
			s.checkForUpdates()
		}
	}
}

// checkForUpdates 检查数据接口更新
func (s *DataInterfaceScheduler) checkForUpdates() {
	var interfaces []models.DataInterface
	err := s.db.Where("status = ? AND collection_enabled = ?", "enabled", true).Find(&interfaces).Error
	if err != nil {
		fmt.Printf("检查数据接口更新失败: %v\n", err)
		return
	}

	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 检查新增的接口
	for _, iface := range interfaces {
		if _, exists := s.timers[iface.ID]; !exists {
			fmt.Printf("发现新启用的数据接口: %s\n", iface.Name)
			s.scheduleInterface(&iface)
		}
	}

	// 检查已删除或禁用的接口
	enabledIDs := make(map[uint]bool)
	for _, iface := range interfaces {
		enabledIDs[iface.ID] = true
	}

	for id, timer := range s.timers {
		if !enabledIDs[id] {
			fmt.Printf("数据接口 ID %d 已被禁用或删除，停止定时任务\n", id)
			timer.Stop()
			delete(s.timers, id)
		}
	}
}

// UpdateInterface 更新数据接口调度
func (s *DataInterfaceScheduler) UpdateInterface(interfaceID uint) {
	var iface models.DataInterface
	err := s.db.First(&iface, interfaceID).Error
	if err != nil {
		utils.Errorf("加载数据接口失败: %v", err)
		return
	}

	if iface.Status == "enabled" && iface.CollectionEnabled {
		utils.Infof("更新数据接口调度: %s", iface.Name)
		s.scheduleInterface(&iface)
	} else {
		// 停止调度
		s.mutex.Lock()
		if timer, exists := s.timers[iface.ID]; exists {
			timer.Stop()
			delete(s.timers, iface.ID)
			utils.Infof("停止数据接口调度: %s", iface.Name)
		}
		s.mutex.Unlock()
	}
}

// GetScheduledInterfaces 获取已调度的接口列表
func (s *DataInterfaceScheduler) GetScheduledInterfaces() []uint {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	var ids []uint
	for id := range s.timers {
		ids = append(ids, id)
	}
	return ids
}
