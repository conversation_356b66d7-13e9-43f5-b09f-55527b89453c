package handlers

// 数据接口模板文件
// 这个文件展示了如何为新的数据源创建专用的处理逻辑
// 复制这个模板并根据具体数据源的需求进行修改

import (
	"fmt"
	"net"

	"vulnerability_push/internal/models"
	"vulnerability_push/internal/utils"
)

// ExampleDataInterface 示例数据接口实现
// 为新的数据源创建类似的结构体
type ExampleDataInterface struct {
	*BaseDataInterface
}

// NewExampleDataInterface 创建示例数据接口实例
func NewExampleDataInterface(handler *DataInterfaceHandler) *ExampleDataInterface {
	return &ExampleDataInterface{
		BaseDataInterface: NewBaseDataInterface(handler),
	}
}

// Execute 执行数据采集
func (e *ExampleDataInterface) Execute(config map[string]interface{}) (int, error) {
	utils.Infof("开始执行示例数据接口采集")

	// 1. 从配置中获取参数
	host, _ := config["host"].(string)
	userKey, _ := config["user_key"].(string)

	if host == "" || userKey == "" {
		return 0, fmt.Errorf("缺少必要的配置参数")
	}

	// 2. 调用外部API获取数据
	records, err := e.fetchDataFromAPI(host, userKey)
	if err != nil {
		return 0, fmt.Errorf("获取数据失败: %v", err)
	}

	// 3. 处理和保存数据
	savedCount, err := e.processAndSaveData(records)
	if err != nil {
		return 0, fmt.Errorf("处理数据失败: %v", err)
	}

	utils.Infof("示例数据接口采集完成 - 处理了 %d 条数据", savedCount)
	return savedCount, nil
}

// fetchDataFromAPI 从外部API获取数据
func (e *ExampleDataInterface) fetchDataFromAPI(host, userKey string) ([]SearchDataRecord, error) {
	// TODO: 实现具体的API调用逻辑
	// 这里应该包含：
	// 1. HTTP客户端配置
	// 2. API请求构建
	// 3. 响应解析
	// 4. 数据格式转换
	
	fmt.Printf("从API获取数据: host=%s\n", host)
	
	// 示例：返回空数据
	return []SearchDataRecord{}, nil
}

// processAndSaveData 处理并保存数据
func (e *ExampleDataInterface) processAndSaveData(records []SearchDataRecord) (int, error) {
	if len(records) == 0 {
		utils.Infof("没有数据需要处理")
		return 0, nil
	}

	// 使用专用的攻击流合并逻辑
	attackSummaries, internalCount, whitelistCount, filteredCount := e.mergeAttackDataWithUUIDDedup(records)

	utils.Infof("数据处理统计 - 原始记录: %d, 内网流量: %d, 白名单: %d, 非目标组织: %d, 最终合并记录: %d",
		len(records), internalCount, whitelistCount, filteredCount, len(attackSummaries))

	// 转换为IOC源数据并保存
	var sourceDataList []models.IOCIntelligenceData
	var allProcessedUUIDs []string

	for _, summary := range attackSummaries {
		// 获取主要类别
		mainCategory := e.getMainCategory(summary.Categories)

		// 使用示例数据源专用的来源标签逻辑
		sourceLabel := e.getExampleSourceLabel(summary)

		sourceData := models.IOCIntelligenceData{
			UUID:            summary.UUID,
			AttackIP:        summary.SrcIP,
			VictimIP:        summary.DstIP,
			SourceLabel:     sourceLabel,
			Category:        e.GetAttackCategoryName(mainCategory),
			AttackCount:     summary.AttackCount,
			FirstAttackTime: fmt.Sprintf("%d", summary.FirstSeen),
			LastAttackTime:  fmt.Sprintf("%d", summary.LastSeen),
			ThreatScore:     e.calculateThreatScore(summary.Severities, summary.AttackCount),
		}
		sourceDataList = append(sourceDataList, sourceData)

		// 收集所有UUID用于标记为已处理
		allProcessedUUIDs = append(allProcessedUUIDs, summary.UUIDs...)
	}

	// 批量保存IOC源数据
	if err := e.BatchSaveIOCSourceData(sourceDataList); err != nil {
		return 0, fmt.Errorf("保存IOC源数据失败: %v", err)
	}

	// 批量保存所有已处理的UUID（优化版本）
	if err := e.BatchSaveProcessedUUIDs(allProcessedUUIDs, "example_source", e.dedupConfig.SaveBatchSize); err != nil {
		fmt.Printf("批量保存UUID失败: %v\n", err)
		// 如果批量保存失败，回退到单个保存模式
		savedCount := 0
		for _, uuid := range allProcessedUUIDs {
			if uuid != "" && !e.IsUUIDProcessed(uuid, "example_source") {
				if err := e.SaveProcessedUUID(uuid, "example_source"); err != nil {
					fmt.Printf("保存UUID失败: %s, 错误: %v\n", uuid, err)
				} else {
					savedCount++
				}
			}
		}
		fmt.Printf("回退模式保存了 %d 个已处理的UUID\n", savedCount)
	}
	fmt.Printf("成功保存 %d 条IOC源数据\n", len(sourceDataList))
	return len(sourceDataList), nil
}

// getExampleSourceLabel 获取示例数据源专用的来源标签
// 每个数据源都应该有自己的标签映射逻辑
func (e *ExampleDataInterface) getExampleSourceLabel(summary *AttackSummary) string {
	// 示例数据源的标签确定逻辑
	// 优先使用受害方标签
	if summary.DstLabel == "目标组织A" || summary.DstLabel == "目标组织B" {
		return summary.DstLabel
	} else if summary.SrcLabel == "目标组织A" || summary.SrcLabel == "目标组织B" {
		return summary.SrcLabel
	}
	
	return ""
}

// getExampleIPLabel 获取示例数据源的IP标签
// 不同数据源可能有不同的IP段映射规则
func (e *ExampleDataInterface) getExampleIPLabel(ip string) string {
	if ip == "" {
		return "未知"
	}

	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return "未知"
	}

	if ipv4 := parsedIP.To4(); ipv4 != nil {
		// 示例数据源的IP段映射规则：
		// 可以根据具体数据源的网络环境进行配置
		
		// 示例：10.0.x.x段 - 组织A
		if ipv4[0] == 10 && ipv4[1] == 0 {
			return "目标组织A"
		}

		// 示例：10.1.x.x段 - 组织B  
		if ipv4[0] == 10 && ipv4[1] == 1 {
			return "目标组织B"
		}

		// 示例：192.168.x.x段 - 内网设备
		if ipv4[0] == 192 && ipv4[1] == 168 {
			return "内网设备"
		}
	}

	// 外网地址
	if !e.IsInternalIP(ip) {
		return "外网"
	}

	return "内网"
}

// mergeAttackDataWithUUIDDedup 示例数据源的攻击流合并逻辑
// 可以根据数据源特点进行定制
func (e *ExampleDataInterface) mergeAttackDataWithUUIDDedup(records []SearchDataRecord) (map[string]*AttackSummary, int, int, int) {
	// TODO: 实现具体的合并逻辑
	// 这里应该包含：
	// 1. UUID去重
	// 2. 内网流量过滤
	// 3. 攻击流合并
	// 4. 使用getExampleIPLabel获取IP标签
	
	fmt.Printf("开始处理示例数据源的攻击流合并\n")
	
	// 示例：返回空结果
	return make(map[string]*AttackSummary), 0, 0, 0
}

// calculateThreatScore 计算威胁评分
// 可以根据数据源特点调整评分逻辑
func (e *ExampleDataInterface) calculateThreatScore(severities map[string]int, attackCount int) float64 {
	// 示例：使用标准的威胁评分逻辑
	// 不同数据源可能需要不同的评分权重
	
	var baseScore float64 = 0
	for severity := range severities {
		var score float64
		switch severity {
		case "高危":
			score = 8.0
		case "中危":
			score = 5.0
		case "低危":
			score = 2.0
		case "信息":
			score = 1.0
		default:
			score = 3.0
		}
		if score > baseScore {
			baseScore = score
		}
	}

	// 频次系数
	var frequencyMultiplier float64 = 1.0
	if attackCount >= 100 {
		frequencyMultiplier = 1.8
	} else if attackCount >= 50 {
		frequencyMultiplier = 1.6
	} else if attackCount >= 20 {
		frequencyMultiplier = 1.4
	} else if attackCount >= 10 {
		frequencyMultiplier = 1.2
	} else if attackCount >= 5 {
		frequencyMultiplier = 1.1
	}

	finalScore := baseScore * frequencyMultiplier

	if finalScore > 10.0 {
		finalScore = 10.0
	} else if finalScore < 1.0 {
		finalScore = 1.0
	}

	return finalScore
}

// getMainCategory 获取主要攻击类别
func (e *ExampleDataInterface) getMainCategory(categories map[string]int) string {
	var mainCategory string
	maxCount := 0
	for category, count := range categories {
		if count > maxCount {
			maxCount = count
			mainCategory = category
		}
	}
	return mainCategory
}
