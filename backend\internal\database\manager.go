package database

import (
	"fmt"
	"strings"

	"gorm.io/gorm"
)

// Manager 数据库总管理器
type Manager struct {
	DatabaseManager  *DatabaseManager
	Seeder          *Seeder
	logger          Logger
}

// NewManager 创建数据库总管理器
func NewManager(config *DatabaseConfig, logger Logger) *Manager {
	dbManager := NewDatabaseManager(config, logger)

	return &Manager{
		DatabaseManager: dbManager,
		logger:         logger,
	}
}

// Initialize 初始化数据库
func (m *Manager) Initialize(models []interface{}, skipSeeding bool) error {
	// 1. 连接数据库
	if err := m.DatabaseManager.Connect(); err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取数据库实例
	db := m.DatabaseManager.GetDB()

	// 2. 初始化数据填充器
	m.Seeder = NewSeeder(db, m.logger)

	// 3. 自动迁移数据库表结构（包含所有字段和索引）
	m.logger.Infof("正在初始化数据库表结构...")
	if err := m.safeAutoMigrate(db, models); err != nil {
		return fmt.Errorf("表结构初始化失败: %w", err)
	}
	m.logger.Infof("表结构初始化成功")

	// 4. 填充默认数据
	if !skipSeeding {
		m.logger.Infof("开始填充默认数据...")
		if err := m.Seeder.SeedDefaultData(); err != nil {
			return fmt.Errorf("填充默认数据失败: %w", err)
		}
		m.logger.Infof("默认数据填充完成")
	} else {
		m.logger.Infof("跳过默认数据填充")
	}

	m.logger.Infof("数据库初始化完成")
	return nil
}

// GetDB 获取数据库实例
func (m *Manager) GetDB() *gorm.DB {
	return m.DatabaseManager.GetDB()
}

// Close 关闭数据库连接
func (m *Manager) Close() error {
	return m.DatabaseManager.Close()
}

// Ping 测试数据库连接
func (m *Manager) Ping() error {
	return m.DatabaseManager.Ping()
}

// Transaction 执行事务
func (m *Manager) Transaction(fn func(*gorm.DB) error) error {
	return m.DatabaseManager.Transaction(fn)
}

// Health 健康检查
func (m *Manager) Health() error {
	if err := m.Ping(); err != nil {
		return fmt.Errorf("数据库连接异常: %w", err)
	}
	return nil
}

// GetStats 获取数据库统计信息
func (m *Manager) GetStats() (map[string]interface{}, error) {
	db := m.GetDB()
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取底层数据库连接失败: %w", err)
	}
	
	stats := sqlDB.Stats()
	
	return map[string]interface{}{
		"max_open_connections":     stats.MaxOpenConnections,
		"open_connections":         stats.OpenConnections,
		"in_use":                  stats.InUse,
		"idle":                    stats.Idle,
		"wait_count":              stats.WaitCount,
		"wait_duration":           stats.WaitDuration.String(),
		"max_idle_closed":         stats.MaxIdleClosed,
		"max_idle_time_closed":    stats.MaxIdleTimeClosed,
		"max_lifetime_closed":     stats.MaxLifetimeClosed,
	}, nil
}

// Backup 备份数据库（仅支持SQLite）
func (m *Manager) Backup(backupPath string) error {
	// TODO: 实现数据库备份功能
	// 对于SQLite，可以直接复制文件
	// 对于MySQL，需要使用mysqldump或其他工具
	return fmt.Errorf("备份功能尚未实现")
}

// Restore 恢复数据库（仅支持SQLite）
func (m *Manager) Restore(backupPath string) error {
	// TODO: 实现数据库恢复功能
	return fmt.Errorf("恢复功能尚未实现")
}

// ValidateSchema 验证数据库模式
func (m *Manager) ValidateSchema(models []interface{}) error {
	// TODO: 实现数据库模式验证
	// 检查表结构是否与模型定义一致
	return fmt.Errorf("模式验证功能尚未实现")
}

// CleanupOldData 清理旧数据
func (m *Manager) CleanupOldData(retentionDays int) error {
	// TODO: 实现旧数据清理功能
	// 清理超过指定天数的日志、记录等
	return fmt.Errorf("数据清理功能尚未实现")
}

// OptimizeTables 优化表（仅MySQL）
func (m *Manager) OptimizeTables() error {
	if !m.isMySQL() {
		return fmt.Errorf("表优化仅支持MySQL数据库")
	}

	// 获取所有表
	tables, err := m.getAllTables()
	if err != nil {
		return fmt.Errorf("获取表列表失败: %w", err)
	}

	db := m.GetDB()
	for _, table := range tables {
		m.logger.Infof("正在优化表: %s", table)
		if err := db.Exec(fmt.Sprintf("OPTIMIZE TABLE `%s`", table)).Error; err != nil {
			m.logger.Warnf("优化表 %s 失败: %v", table, err)
		}
	}

	m.logger.Infof("表优化完成")
	return nil
}

// AnalyzeTables 分析表（仅MySQL）
func (m *Manager) AnalyzeTables() error {
	if !m.isMySQL() {
		return fmt.Errorf("表分析仅支持MySQL数据库")
	}

	// 获取所有表
	tables, err := m.getAllTables()
	if err != nil {
		return fmt.Errorf("获取表列表失败: %w", err)
	}

	db := m.GetDB()
	for _, table := range tables {
		m.logger.Infof("正在分析表: %s", table)
		if err := db.Exec(fmt.Sprintf("ANALYZE TABLE `%s`", table)).Error; err != nil {
			m.logger.Warnf("分析表 %s 失败: %v", table, err)
		}
	}

	m.logger.Infof("表分析完成")
	return nil
}

// GetTableSizes 获取表大小信息（仅MySQL）
func (m *Manager) GetTableSizes() ([]map[string]interface{}, error) {
	if !m.isMySQL() {
		return nil, fmt.Errorf("表大小查询仅支持MySQL数据库")
	}

	db := m.GetDB()
	rows, err := db.Raw(`
		SELECT
			table_name,
			ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
			table_rows
		FROM information_schema.tables
		WHERE table_schema = DATABASE()
		ORDER BY (data_length + index_length) DESC
	`).Rows()

	if err != nil {
		return nil, fmt.Errorf("查询表大小失败: %w", err)
	}
	defer rows.Close()

	var results []map[string]interface{}
	for rows.Next() {
		var tableName string
		var sizeMB float64
		var tableRows int64

		if err := rows.Scan(&tableName, &sizeMB, &tableRows); err != nil {
			return nil, fmt.Errorf("读取表大小数据失败: %w", err)
		}

		results = append(results, map[string]interface{}{
			"table_name": tableName,
			"size_mb":    sizeMB,
			"table_rows": tableRows,
		})
	}

	return results, nil
}

// isMySQL 检查是否为MySQL数据库
func (m *Manager) isMySQL() bool {
	return m.DatabaseManager.config.Type == "mysql"
}

// getAllTables 获取所有表名
func (m *Manager) getAllTables() ([]string, error) {
	db := m.GetDB()
	var tables []string

	if m.isMySQL() {
		rows, err := db.Raw("SHOW TABLES").Rows()
		if err != nil {
			return nil, fmt.Errorf("查询MySQL表失败: %w", err)
		}
		defer rows.Close()

		for rows.Next() {
			var tableName string
			if err := rows.Scan(&tableName); err != nil {
				return nil, fmt.Errorf("读取表名失败: %w", err)
			}
			tables = append(tables, tableName)
		}
	} else {
		// SQLite
		rows, err := db.Raw("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'").Rows()
		if err != nil {
			return nil, fmt.Errorf("查询SQLite表失败: %w", err)
		}
		defer rows.Close()

		for rows.Next() {
			var tableName string
			if err := rows.Scan(&tableName); err != nil {
				return nil, fmt.Errorf("读取表名失败: %w", err)
			}
			tables = append(tables, tableName)
		}
	}

	return tables, nil
}

// safeAutoMigrate 安全的自动迁移，避免索引冲突
func (m *Manager) safeAutoMigrate(db *gorm.DB, models []interface{}) error {
	// 检查是否需要迁移
	needsMigration, err := m.checkMigrationNeeded(db, models)
	if err != nil {
		m.logger.Warnf("检查迁移状态失败，将执行迁移: %v", err)
		needsMigration = true
	}

	if !needsMigration {
		m.logger.Infof("数据库表结构已是最新，跳过迁移")
		return nil
	}

	m.logger.Infof("检测到表结构变化，开始迁移...")

	// 首先尝试正常的AutoMigrate
	err = db.AutoMigrate(models...)
	if err != nil {
		// 如果出现索引相关错误，尝试修复
		if strings.Contains(err.Error(), "Can't DROP") && strings.Contains(err.Error(), "check that column/key exists") {
			m.logger.Warnf("检测到索引冲突，尝试修复: %v", err)

			// 清理所有可能冲突的索引
			if cleanErr := m.cleanConflictingIndexes(db); cleanErr != nil {
				m.logger.Warnf("清理索引失败: %v", cleanErr)
			}

			// 重新尝试迁移
			err = db.AutoMigrate(models...)
			if err != nil {
				return fmt.Errorf("重新迁移失败: %w", err)
			}
			m.logger.Infof("索引冲突已修复，迁移成功")
		} else {
			return err
		}
	}
	return nil
}

// checkMigrationNeeded 检查是否需要执行迁移
func (m *Manager) checkMigrationNeeded(db *gorm.DB, models []interface{}) (bool, error) {
	// 检查所有模型对应的表是否存在
	for _, model := range models {
		if !db.Migrator().HasTable(model) {
			m.logger.Infof("表不存在，需要迁移: %T", model)
			return true, nil
		}
	}

	// 简单检查：如果所有表都存在，假设不需要迁移
	// 这里可以根据需要添加更复杂的列检查逻辑
	return false, nil
}

// cleanConflictingIndexes 清理可能冲突的索引
func (m *Manager) cleanConflictingIndexes(db *gorm.DB) error {
	// 获取所有表名
	tables, err := m.getAllTables()
	if err != nil {
		return fmt.Errorf("获取表列表失败: %w", err)
	}

	for _, tableName := range tables {
		// 获取表的所有索引
		var indexes []struct {
			KeyName string `gorm:"column:Key_name"`
		}

		err := db.Raw("SHOW INDEX FROM `" + tableName + "`").Scan(&indexes).Error
		if err != nil {
			m.logger.Warnf("获取表 %s 的索引失败: %v", tableName, err)
			continue
		}

		// 删除所有非主键索引
		for _, index := range indexes {
			if index.KeyName != "PRIMARY" {
				m.logger.Infof("删除表 %s 的索引: %s", tableName, index.KeyName)
				err = db.Exec(fmt.Sprintf("DROP INDEX `%s` ON `%s`", index.KeyName, tableName)).Error
				if err != nil {
					m.logger.Warnf("删除索引 %s 失败: %v", index.KeyName, err)
				}
			}
		}
	}

	return nil
}
