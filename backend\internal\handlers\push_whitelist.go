package handlers

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/internal/models"
)

// UpdatePushWhitelistRequest 更新推送白名单请求
type UpdatePushWhitelistRequest struct {
	Keywords    string `json:"keywords"`
	AutoPush    bool   `json:"autoPush"`
	PolicyID    uint   `json:"policyId"`
	Description string `json:"description"`
}

// GetPushWhitelist 获取推送白名单
func (h *PushHandler) GetPushWhitelist(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var whitelist models.PushWhitelist
	
	// 查询白名单配置，如果不存在则创建一个默认的
	result := h.db.First(&whitelist)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// 创建默认白名单配置
			// 先获取默认策略ID
			var defaultPolicy models.PushPolicy
			if err := h.db.Where("is_default = ?", true).First(&defaultPolicy).Error; err != nil {
				h.InternalServerError(c, "获取默认策略失败: "+err.Error())
				return
			}
			
			whitelist = models.PushWhitelist{
				Keywords:    "CVE,远程代码执行,RCE,任意代码执行,ACE",
				AutoPush:    true,
				PolicyID:    defaultPolicy.ID,
				Description: "系统默认白名单配置，匹配关键漏洞自动推送",
			}
			if err := h.db.Create(&whitelist).Error; err != nil {
				h.InternalServerError(c, "创建默认白名单失败: "+err.Error())
				return
			}
		} else {
			h.InternalServerError(c, "获取推送白名单失败: "+result.Error.Error())
			return
		}
	}
	
	// 如果设置了策略ID，获取策略信息
	var policyName string
	if whitelist.PolicyID > 0 {
		var policy models.PushPolicy
		if err := h.db.First(&policy, whitelist.PolicyID).Error; err == nil {
			policyName = policy.Name
		}
	}
	
	// 构建响应数据
	response := map[string]interface{}{
		"id":          whitelist.ID,
		"keywords":    whitelist.Keywords,
		"autoPush":    whitelist.AutoPush,
		"policyId":    whitelist.PolicyID,
		"policyName":  policyName,
		"description": whitelist.Description,
		"createdAt":   whitelist.CreatedAt,
		"updatedAt":   whitelist.UpdatedAt,
	}

	h.Success(c, response)
}

// UpdatePushWhitelist 更新推送白名单
func (h *PushHandler) UpdatePushWhitelist(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req UpdatePushWhitelistRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 如果启用了自动推送，检查策略是否存在
	if req.AutoPush && req.PolicyID > 0 {
		var policy models.PushPolicy
		if err := h.db.First(&policy, req.PolicyID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				h.BadRequest(c, "指定的推送策略不存在")
				return
			}
			h.InternalServerError(c, "检查推送策略失败: "+err.Error())
			return
		}
	}

	var whitelist models.PushWhitelist
	result := h.db.First(&whitelist)
	
	// 如果不存在，创建一个新的
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			whitelist = models.PushWhitelist{
				Keywords:    req.Keywords,
				AutoPush:    req.AutoPush,
				PolicyID:    req.PolicyID,
				Description: req.Description,
			}
			if err := h.db.Create(&whitelist).Error; err != nil {
				h.InternalServerError(c, "创建推送白名单失败: "+err.Error())
				return
			}
		} else {
			h.InternalServerError(c, "获取推送白名单失败: "+result.Error.Error())
			return
		}
	} else {
		// 更新现有配置
		whitelist.Keywords = req.Keywords
		whitelist.AutoPush = req.AutoPush
		whitelist.PolicyID = req.PolicyID
		whitelist.Description = req.Description
		if err := h.db.Save(&whitelist).Error; err != nil {
			h.InternalServerError(c, "更新推送白名单失败: "+err.Error())
			return
		}
	}

	h.SuccessWithMessage(c, "更新推送白名单成功", map[string]interface{}{
		"id": whitelist.ID,
	})
}
