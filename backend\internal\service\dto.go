package service

import (
	"vulnerability_push/internal/models"
)

// 导入模型定义（临时，后续会重构）
// 这些类型定义将从现有的模型文件中迁移过来

// User 用户模型（临时定义）
type User struct {
	ID        uint   `json:"id"`
	Username  string `json:"username"`
	Password  string `json:"-"`
	Email     string `json:"email"`
	Role      string `json:"role"`
	Status    bool   `json:"status"`
	APIKey    string `json:"apiKey"`
	CreatedAt int64  `json:"createdAt"`
	UpdatedAt int64  `json:"updatedAt"`
}

// IsAdmin 检查是否为管理员
func (u *User) IsAdmin() bool {
	return u.Role == "admin"
}

// IsActive 检查用户是否激活
func (u *User) IsActive() bool {
	return u.Status
}

// Vulnerability 漏洞模型（临时定义）
type Vulnerability struct {
	ID             uint   `json:"id"`
	Name           string `json:"name"`
	VulnID         string `json:"vulnId"`
	Severity       string `json:"severity"`
	Tags           string `json:"tags"`
	DisclosureDate string `json:"disclosureDate"`
	PushReason     string `json:"pushReason"`
	Source         string `json:"source"`
	Description    string `json:"description"`
	References     string `json:"references"`
	Remediation    string `json:"remediation"`
	CreatedAt      int64  `json:"createdAt"`
	UpdatedAt      int64  `json:"updatedAt"`
}

// Crawler 采集器模型（临时定义）
type Crawler struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	Description string `json:"description"`
	Config      string `json:"config"`
	Interval    int    `json:"interval"`
	Status      bool   `json:"status"`
	LastRunAt   int64  `json:"lastRunAt"`
	CreatedAt   int64  `json:"createdAt"`
	UpdatedAt   int64  `json:"updatedAt"`
}

// CrawlerLog 采集器日志模型（临时定义）
type CrawlerLog struct {
	ID        uint   `json:"id"`
	CrawlerID uint   `json:"crawlerId"`
	Status    string `json:"status"`
	Count     int    `json:"count"`
	Message   string `json:"message"`
	CreatedAt int64  `json:"createdAt"`
}

// PushChannel 推送通道模型（临时定义）
type PushChannel struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	Description string `json:"description"`
	Config      string `json:"config"`
	Status      bool   `json:"status"`
	CreatedAt   int64  `json:"createdAt"`
	UpdatedAt   int64  `json:"updatedAt"`
}

// PushPolicy 推送策略模型（临时定义）
type PushPolicy struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	ChannelIDs  string `json:"channelIDs"`
	IsDefault   bool   `json:"isDefault"`
	CreatedAt   int64  `json:"createdAt"`
	UpdatedAt   int64  `json:"updatedAt"`
}

// 注意：IOC相关模型已移至 internal/models 包中

// DataInterface 数据接口模型（临时定义）
type DataInterface struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	Description string `json:"description"`
	Config      string `json:"config"`
	Status      string `json:"status"`
	Interval    int    `json:"interval"`
	LastRunAt   int64  `json:"lastRunAt"`
	CreatedAt   int64  `json:"createdAt"`
	UpdatedAt   int64  `json:"updatedAt"`
}

// 通用请求和响应结构

// PaginationRequest 分页请求
type PaginationRequest struct {
	Page     int `json:"page" form:"page"`
	PageSize int `json:"pageSize" form:"pageSize"`
	// 兼容下划线命名
	PageSizeUnderscore int `json:"page_size" form:"page_size"`
}

// NormalizePagination 标准化分页参数
func (p *PaginationRequest) NormalizePagination() {
	// 处理分页参数兼容性
	if p.PageSize <= 0 && p.PageSizeUnderscore > 0 {
		p.PageSize = p.PageSizeUnderscore
	}

	// 设置默认值
	if p.Page <= 0 {
		p.Page = 1
	}
	if p.PageSize <= 0 {
		p.PageSize = 10
	}

	// 验证分页参数范围
	if p.PageSize > 100 {
		p.PageSize = 100
	}
	if p.PageSize < 5 {
		p.PageSize = 10
	}
}

// PaginationResponse 分页响应
type PaginationResponse struct {
	Total       int64 `json:"total"`
	Page        int   `json:"page"`
	PageSize    int   `json:"pageSize"`
	TotalPages  int   `json:"totalPages"`
	HasNext     bool  `json:"hasNext"`
	HasPrevious bool  `json:"hasPrevious"`
}

// 用户相关DTO

// LoginResult 登录结果
type LoginResult struct {
	Token string `json:"token"`
	User  *User  `json:"user"`
}

// GetUsersRequest 获取用户列表请求
type GetUsersRequest struct {
	PaginationRequest
	Username string `json:"username" form:"username"`
	Role     string `json:"role" form:"role"`
	Status   *bool  `json:"status" form:"status"`
}

// GetUsersResponse 获取用户列表响应
type GetUsersResponse struct {
	PaginationResponse
	Users []*User `json:"users"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required,min=6"`
	Email    string `json:"email" binding:"required,email"`
	Role     string `json:"role" binding:"required,oneof=admin user"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Email  string `json:"email" binding:"omitempty,email"`
	Role   string `json:"role" binding:"omitempty,oneof=admin user"`
	Status *bool  `json:"status"`
}

// 漏洞相关DTO

// GetVulnerabilitiesRequest 获取漏洞列表请求
type GetVulnerabilitiesRequest struct {
	PaginationRequest
	Keyword   string `json:"keyword" form:"keyword"`
	Severity  string `json:"severity" form:"severity"`
	Source    string `json:"source" form:"source"`
	StartDate string `json:"startDate" form:"startDate"`
	EndDate   string `json:"endDate" form:"endDate"`
	SortBy    string `json:"sortBy" form:"sortBy"`
	SortOrder string `json:"sortOrder" form:"sortOrder"`
}

// GetVulnerabilitiesResponse 获取漏洞列表响应
type GetVulnerabilitiesResponse struct {
	PaginationResponse
	Vulnerabilities []*Vulnerability `json:"vulnerabilities"`
}

// CreateVulnerabilityRequest 创建漏洞请求
type CreateVulnerabilityRequest struct {
	Name           string   `json:"name" binding:"required"`
	VulnID         string   `json:"vulnId" binding:"required"`
	Severity       string   `json:"severity" binding:"required"`
	Tags           []string `json:"tags"`
	DisclosureDate string   `json:"disclosureDate"`
	PushReason     string   `json:"pushReason"`
	Source         string   `json:"source"`
	Description    string   `json:"description"`
	References     []string `json:"references"`
	Remediation    string   `json:"remediation"`
}

// UpdateVulnerabilityRequest 更新漏洞请求
type UpdateVulnerabilityRequest struct {
	Name           string   `json:"name"`
	Severity       string   `json:"severity"`
	Tags           []string `json:"tags"`
	DisclosureDate string   `json:"disclosureDate"`
	PushReason     string   `json:"pushReason"`
	Source         string   `json:"source"`
	Description    string   `json:"description"`
	References     []string `json:"references"`
	Remediation    string   `json:"remediation"`
}

// VulnerabilityStats 漏洞统计
type VulnerabilityStats struct {
	Total      int64                    `json:"total"`
	BySeverity map[string]int64         `json:"bySeverity"`
	BySource   map[string]int64         `json:"bySource"`
	Recent     []*VulnerabilityRecent   `json:"recent"`
}

// VulnerabilityRecent 最近漏洞
type VulnerabilityRecent struct {
	Date  string `json:"date"`
	Count int64  `json:"count"`
}

// ExportVulnerabilitiesRequest 导出漏洞请求
type ExportVulnerabilitiesRequest struct {
	StartDate string   `json:"startDate"`
	EndDate   string   `json:"endDate"`
	Severity  []string `json:"severity"`
	Source    []string `json:"source"`
	Format    string   `json:"format" binding:"oneof=xlsx csv"`
}

// 采集器相关DTO

// GetCrawlersRequest 获取采集器列表请求
type GetCrawlersRequest struct {
	PaginationRequest
	Name   string `json:"name" form:"name"`
	Type   string `json:"type" form:"type"`
	Status *bool  `json:"status" form:"status"`
}

// GetCrawlersResponse 获取采集器列表响应
type GetCrawlersResponse struct {
	PaginationResponse
	Crawlers []*Crawler `json:"crawlers"`
}

// CreateCrawlerRequest 创建采集器请求
type CreateCrawlerRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Type        string                 `json:"type" binding:"required"`
	Description string                 `json:"description"`
	Config      map[string]interface{} `json:"config"`
	Interval    int                    `json:"interval" binding:"required,min=1"`
	Status      bool                   `json:"status"`
}

// UpdateCrawlerRequest 更新采集器请求
type UpdateCrawlerRequest struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Config      map[string]interface{} `json:"config"`
	Interval    int                    `json:"interval" binding:"omitempty,min=1"`
	Status      *bool                  `json:"status"`
}

// CrawlerRunResult 采集器运行结果
type CrawlerRunResult struct {
	Total int    `json:"total"`
	New   int    `json:"new"`
	Error string `json:"error,omitempty"`
}

// GetCrawlerLogsRequest 获取采集器日志请求
type GetCrawlerLogsRequest struct {
	PaginationRequest
	CrawlerID uint   `json:"crawlerId" form:"crawlerId"`
	Status    string `json:"status" form:"status"`
	StartDate string `json:"startDate" form:"startDate"`
	EndDate   string `json:"endDate" form:"endDate"`
}

// GetCrawlerLogsResponse 获取采集器日志响应
type GetCrawlerLogsResponse struct {
	PaginationResponse
	Logs []*CrawlerLog `json:"logs"`
}

// 推送相关DTO

// CreatePushChannelRequest 创建推送通道请求
type CreatePushChannelRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Type        string                 `json:"type" binding:"required"`
	Description string                 `json:"description"`
	Config      map[string]interface{} `json:"config" binding:"required"`
	Status      bool                   `json:"status"`
}

// UpdatePushChannelRequest 更新推送通道请求
type UpdatePushChannelRequest struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Config      map[string]interface{} `json:"config"`
	Status      *bool                  `json:"status"`
}

// CreatePushPolicyRequest 创建推送策略请求
type CreatePushPolicyRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	ChannelIDs  []uint `json:"channelIds" binding:"required"`
	IsDefault   bool   `json:"isDefault"`
}

// UpdatePushPolicyRequest 更新推送策略请求
type UpdatePushPolicyRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	ChannelIDs  []uint `json:"channelIds"`
	IsDefault   *bool  `json:"isDefault"`
}

// GetPushRecordsRequest 获取推送记录请求
type GetPushRecordsRequest struct {
	PaginationRequest
	VulnerabilityID uint   `json:"vulnerabilityId" form:"vulnerabilityId"`
	ChannelID       uint   `json:"channelId" form:"channelId"`
	Status          string `json:"status" form:"status"`
	StartDate       string `json:"startDate" form:"startDate"`
	EndDate         string `json:"endDate" form:"endDate"`
}

// GetPushRecordsResponse 获取推送记录响应
type GetPushRecordsResponse struct {
	PaginationResponse
	Records []*PushRecord `json:"records"`
}

// PushRecord 推送记录模型（临时定义）
type PushRecord struct {
	ID              uint   `json:"id"`
	VulnerabilityID uint   `json:"vulnerabilityId"`
	ChannelID       uint   `json:"channelId"`
	Status          string `json:"status"`
	ErrorMessage    string `json:"errorMessage"`
	PushedAt        int64  `json:"pushedAt"`
}

// IOC相关DTO

// GetIOCIntelligenceRequest 获取IOC情报请求
type GetIOCIntelligenceRequest struct {
	PaginationRequest
	IOC       string `json:"ioc" form:"ioc"`
	IOCType   string `json:"iocType" form:"iocType"`
	Type      string `json:"type" form:"type"`
	RiskLevel string `json:"riskLevel" form:"riskLevel"`
	StartDate string `json:"startDate" form:"startDate"`
	EndDate   string `json:"endDate" form:"endDate"`
}

// GetIOCIntelligenceResponse 获取IOC情报响应
type GetIOCIntelligenceResponse struct {
	PaginationResponse
	Intelligence []*models.IOCIntelligence `json:"intelligence"`
}

// CreateIOCIntelligenceRequest 创建IOC情报请求
type CreateIOCIntelligenceRequest struct {
	IOC         string   `json:"ioc" binding:"required"`
	IOCType     string   `json:"iocType" binding:"required,oneof=ip domain"`
	Location    string   `json:"location"`
	Type        string   `json:"type" binding:"required"`
	RiskLevel   string   `json:"riskLevel" binding:"required"`
	HitCount    int      `json:"hitCount"`
	Description string   `json:"description"`
	Tags        []string `json:"tags"`
	TargetOrg   string   `json:"targetOrg"`
	PushReason  string   `json:"pushReason"`
}

// UpdateIOCIntelligenceRequest 更新IOC情报请求
type UpdateIOCIntelligenceRequest struct {
	Location    string   `json:"location"`
	Type        string   `json:"type"`
	RiskLevel   string   `json:"riskLevel"`
	HitCount    *int     `json:"hitCount"`
	Description string   `json:"description"`
	Tags        []string `json:"tags"`
	TargetOrg   string   `json:"targetOrg"`
	PushReason  string   `json:"pushReason"`
}

// GetIOCWhitelistRequest 获取IOC白名单请求
type GetIOCWhitelistRequest struct {
	PaginationRequest
	IOC     string `json:"ioc" form:"ioc"`
	IOCType string `json:"iocType" form:"iocType"`
}

// GetIOCWhitelistResponse 获取IOC白名单响应
type GetIOCWhitelistResponse struct {
	PaginationResponse
	Whitelist []*models.IOCWhitelist `json:"whitelist"`
}

// CreateIOCWhitelistRequest 创建IOC白名单请求
type CreateIOCWhitelistRequest struct {
	IOC     string `json:"ioc" binding:"required"`
	IOCType string `json:"iocType" binding:"required,oneof=ip domain"`
	Remark  string `json:"remark"`
}

// UpdateIOCWhitelistRequest 更新IOC白名单请求
type UpdateIOCWhitelistRequest struct {
	IOC     string `json:"ioc"`
	IOCType string `json:"iocType" binding:"omitempty,oneof=ip domain"`
	Remark  string `json:"remark"`
}

// 数据接口相关DTO

// GetDataInterfacesRequest 获取数据接口请求
type GetDataInterfacesRequest struct {
	PaginationRequest
	Name   string `json:"name" form:"name"`
	Type   string `json:"type" form:"type"`
	Status string `json:"status" form:"status"`
}

// GetDataInterfacesResponse 获取数据接口响应
type GetDataInterfacesResponse struct {
	PaginationResponse
	Interfaces []*DataInterface `json:"interfaces"`
}

// CreateDataInterfaceRequest 创建数据接口请求
type CreateDataInterfaceRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Type        string                 `json:"type" binding:"required"`
	Description string                 `json:"description"`
	Config      map[string]interface{} `json:"config" binding:"required"`
	Interval    int                    `json:"interval" binding:"required,min=1"`
	Status      string                 `json:"status" binding:"required,oneof=enabled disabled"`
}

// UpdateDataInterfaceRequest 更新数据接口请求
type UpdateDataInterfaceRequest struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Config      map[string]interface{} `json:"config"`
	Interval    *int                   `json:"interval" binding:"omitempty,min=1"`
	Status      string                 `json:"status" binding:"omitempty,oneof=enabled disabled"`
}

// GetDataInterfaceLogsRequest 获取数据接口日志请求
type GetDataInterfaceLogsRequest struct {
	PaginationRequest
	InterfaceID uint   `json:"interfaceId" form:"interfaceId"`
	Status      string `json:"status" form:"status"`
	StartDate   string `json:"startDate" form:"startDate"`
	EndDate     string `json:"endDate" form:"endDate"`
}

// GetDataInterfaceLogsResponse 获取数据接口日志响应
type GetDataInterfaceLogsResponse struct {
	PaginationResponse
	Logs []*DataInterfaceLog `json:"logs"`
}

// DataInterfaceLog 数据接口日志模型（临时定义）
type DataInterfaceLog struct {
	ID          uint   `json:"id"`
	InterfaceID uint   `json:"interfaceId"`
	Status      string `json:"status"`
	Message     string `json:"message"`
	StartedAt   int64  `json:"startedAt"`    // 开始时间
	EndedAt     int64  `json:"endedAt"`      // 结束时间
	Duration    int    `json:"duration"`     // 执行时长（秒）
	DataCount   int    `json:"dataCount"`    // 处理的数据量
	ProcessedAt int64  `json:"processedAt"`
	CreatedAt   int64  `json:"createdAt"`
}
