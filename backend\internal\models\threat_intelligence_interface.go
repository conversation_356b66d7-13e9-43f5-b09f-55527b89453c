package models

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// ThreatIntelligenceInterface 威胁情报接口配置模型
type ThreatIntelligenceInterface struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"size:100;not null;comment:接口名称"`
	Type        string         `json:"type" gorm:"size:50;not null;comment:接口类型(tjun,weibu)"`
	Description string         `json:"description" gorm:"type:text;comment:接口描述"`
	Config      string         `json:"config" gorm:"type:text;not null;comment:接口配置(JSON格式)"`
	Status      string         `json:"status" gorm:"size:20;not null;default:disabled;comment:状态(enabled,disabled)"`
	CreatedAt   int64          `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   int64          `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (ThreatIntelligenceInterface) TableName() string {
	return "threat_intelligence_interfaces"
}

// TJUNConfig 天际友盟配置结构
type TJUNConfig struct {
	Host      string `json:"host" binding:"required"`       // API域名
	AppKey    string `json:"app_key" binding:"required"`    // APP KEY
	AppSecret string `json:"app_secret" binding:"required"` // APP密钥
	Token     string `json:"token" binding:"required"`      // 用户身份标识
	Timeout   int    `json:"timeout"`                       // 超时时间（秒）
}

// WeibuConfig 微步威胁情报配置结构
type WeibuConfig struct {
	Host    string `json:"host" binding:"required"`    // API域名
	APIKey  string `json:"api_key" binding:"required"` // API密钥
	Timeout int    `json:"timeout"`                    // 超时时间（秒）
}

// GetTJUNConfig 获取天际友盟配置
func (t *ThreatIntelligenceInterface) GetTJUNConfig() (*TJUNConfig, error) {
	if t.Type != "tjun" {
		return nil, ErrInvalidInterfaceType
	}

	var config TJUNConfig
	if err := json.Unmarshal([]byte(t.Config), &config); err != nil {
		return nil, err
	}

	// 设置默认超时时间
	if config.Timeout <= 0 {
		config.Timeout = 10
	}

	return &config, nil
}

// GetWeibuConfig 获取微步威胁情报配置
func (t *ThreatIntelligenceInterface) GetWeibuConfig() (*WeibuConfig, error) {
	if t.Type != "weibu" {
		return nil, ErrInvalidInterfaceType
	}

	var config WeibuConfig
	if err := json.Unmarshal([]byte(t.Config), &config); err != nil {
		return nil, err
	}

	// 设置默认超时时间
	if config.Timeout <= 0 {
		config.Timeout = 10
	}

	return &config, nil
}

// SetTJUNConfig 设置天际友盟配置
func (t *ThreatIntelligenceInterface) SetTJUNConfig(config *TJUNConfig) error {
	if t.Type != "tjun" {
		return ErrInvalidInterfaceType
	}

	configBytes, err := json.Marshal(config)
	if err != nil {
		return err
	}

	t.Config = string(configBytes)
	return nil
}

// SetWeibuConfig 设置微步威胁情报配置
func (t *ThreatIntelligenceInterface) SetWeibuConfig(config *WeibuConfig) error {
	if t.Type != "weibu" {
		return ErrInvalidInterfaceType
	}

	configBytes, err := json.Marshal(config)
	if err != nil {
		return err
	}

	t.Config = string(configBytes)
	return nil
}

// IsEnabled 检查接口是否启用
func (t *ThreatIntelligenceInterface) IsEnabled() bool {
	return t.Status == "enabled"
}

// Enable 启用接口
func (t *ThreatIntelligenceInterface) Enable() {
	t.Status = "enabled"
}

// Disable 禁用接口
func (t *ThreatIntelligenceInterface) Disable() {
	t.Status = "disabled"
}

// BeforeCreate GORM钩子：创建前
func (t *ThreatIntelligenceInterface) BeforeCreate(tx *gorm.DB) error {
	if t.CreatedAt == 0 {
		t.CreatedAt = time.Now().Unix()
	}
	if t.UpdatedAt == 0 {
		t.UpdatedAt = time.Now().Unix()
	}
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (t *ThreatIntelligenceInterface) BeforeUpdate(tx *gorm.DB) error {
	t.UpdatedAt = time.Now().Unix()
	return nil
}

// ValidateConfig 验证配置是否有效
func (t *ThreatIntelligenceInterface) ValidateConfig() error {
	switch t.Type {
	case "tjun":
		_, err := t.GetTJUNConfig()
		return err
	case "weibu":
		_, err := t.GetWeibuConfig()
		return err
	default:
		return ErrInvalidInterfaceType
	}
}

// GetSupportedTypes 获取支持的接口类型
func GetSupportedThreatIntelligenceTypes() []string {
	return []string{"tjun", "weibu"}
}

// GetTypeDisplayName 获取接口类型显示名称
func GetThreatIntelligenceTypeDisplayName(interfaceType string) string {
	switch interfaceType {
	case "tjun":
		return "天际友盟"
	case "weibu":
		return "微步威胁情报"
	default:
		return interfaceType
	}
}

// 自定义错误
var (
	ErrInvalidInterfaceType = &CustomError{
		Code:    "INVALID_INTERFACE_TYPE",
		Message: "无效的威胁情报接口类型",
	}
	ErrConfigParseError = &CustomError{
		Code:    "CONFIG_PARSE_ERROR",
		Message: "配置解析失败",
	}
)

// CustomError 自定义错误类型
type CustomError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

func (e *CustomError) Error() string {
	return e.Message
}

// ThreatIntelligenceInterfaceService 威胁情报接口服务接口
type ThreatIntelligenceInterfaceService interface {
	// 获取所有启用的接口
	GetEnabledInterfaces() ([]*ThreatIntelligenceInterface, error)
	
	// 根据类型获取启用的接口
	GetEnabledInterfacesByType(interfaceType string) ([]*ThreatIntelligenceInterface, error)
	
	// 获取默认接口（每种类型的第一个启用接口）
	GetDefaultInterface(interfaceType string) (*ThreatIntelligenceInterface, error)
	
	// 测试接口连接
	TestInterface(interfaceID uint, ioc string, iocType string) (interface{}, error)
}

// ThreatIntelligenceInterfaceRepository 威胁情报接口仓库接口
type ThreatIntelligenceInterfaceRepository interface {
	// 基础CRUD操作
	Create(interface_ *ThreatIntelligenceInterface) error
	GetByID(id uint) (*ThreatIntelligenceInterface, error)
	Update(interface_ *ThreatIntelligenceInterface) error
	Delete(id uint) error
	
	// 查询操作
	List(page, pageSize int, filters map[string]interface{}) ([]*ThreatIntelligenceInterface, int64, error)
	GetByType(interfaceType string) ([]*ThreatIntelligenceInterface, error)
	GetEnabledByType(interfaceType string) ([]*ThreatIntelligenceInterface, error)
	
	// 批量操作
	BatchDelete(ids []uint) error
	BatchUpdateStatus(ids []uint, status string) error
}
