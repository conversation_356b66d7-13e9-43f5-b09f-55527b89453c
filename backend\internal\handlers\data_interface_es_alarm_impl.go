package handlers

// ES告警数据接口实现
// 这个文件包含ES告警数据源的专用处理逻辑，包括：
// 1. ES连接和查询逻辑
// 2. 告警数据解析和转换
// 3. ES专用的节点代码映射规则
// 4. 攻击流合并和去重逻辑
// 5. 海事局内网IP过滤规则

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"net"
	"time"

	"github.com/elastic/go-elasticsearch/v6"
	"github.com/elastic/go-elasticsearch/v6/esapi"
	"vulnerability_push/internal/models"
)

// ESAlarmRecord ES告警数据记录
type ESAlarmRecord struct {
	NodeCode        string      `json:"nodeCode"`
	SourceIP        string      `json:"sourceIP"`
	DestinationIP   string      `json:"destinationIP"`
	IncidentTypeSub interface{} `json:"incidentTypeSub"`
	ActionTimeShow  string      `json:"actionTimeShow"`
	IncidentName    string      `json:"incidentName"`
	IncidentLevel   interface{} `json:"incidentLevel"`
	DeviceBrand     string      `json:"deviceBrand"`
	AttackTime      string      `json:"attackTime"`
	LogID           string      `json:"logId"`
}

// ESSearchResponse Elasticsearch 6.8.0搜索响应结构体
type ESSearchResponse struct {
	ScrollID string `json:"_scroll_id"`
	Hits     struct {
		Total int `json:"total"` // ES 6.x中total是数字，不是对象
		Hits  []struct {
			Source map[string]interface{} `json:"_source"`
		} `json:"hits"`
	} `json:"hits"`
}

// ESAlarmSummary ES告警流汇总数据
type ESAlarmSummary struct {
	UUID            string            `json:"uuid"`
	SourceIP        string            `json:"source_ip"`
	DestinationIP   string            `json:"destination_ip"`
	NodeCode        string            `json:"node_code"`
	SourceLabel     string            `json:"source_label"`
	AttackCount     int               `json:"attack_count"`
	Categories      map[string]int    `json:"categories"`
	Levels          map[string]int    `json:"levels"`
	FirstSeen       int64             `json:"first_seen"`
	LastSeen        int64             `json:"last_seen"`
	SampleRecord    ESAlarmRecord     `json:"sample_record"`
	LogIDs          []string          `json:"log_ids"`
}

// ESAlarmInterface ES告警数据接口实现
type ESAlarmInterface struct {
	*BaseDataInterface
	handler *DataInterfaceHandler
	client  *elasticsearch.Client
}

// NewESAlarmInterface 创建ES告警接口实例
func NewESAlarmInterface(handler *DataInterfaceHandler) *ESAlarmInterface {
	return &ESAlarmInterface{
		BaseDataInterface: NewBaseDataInterface(handler),
		handler:          handler,
	}
}

// GetType 获取接口类型
func (e *ESAlarmInterface) GetType() string {
	return "es_alarm"
}

// GetDescription 获取接口描述
func (e *ESAlarmInterface) GetDescription() string {
	return "Elasticsearch告警数据接口"
}

// ValidateConfig 验证配置
func (e *ESAlarmInterface) ValidateConfig(config map[string]interface{}) error {
	// 检查必需的配置项
	requiredFields := []string{"es_url"}
	for _, field := range requiredFields {
		if _, exists := config[field]; !exists {
			return fmt.Errorf("缺少必需的配置项: %s", field)
		}
	}

	// 验证es_url格式
	if esURL, ok := config["es_url"].(string); !ok || esURL == "" {
		return fmt.Errorf("es_url配置无效")
	}

	return nil
}

// GetConfigSchema 获取配置模式
func (e *ESAlarmInterface) GetConfigSchema() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"es_url": map[string]interface{}{
				"type":        "string",
				"title":       "Elasticsearch地址",
				"description": "Elasticsearch服务器地址",
				"required":    true,
				"placeholder": "例如: http://127.0.0.1:9200",
			},
		},
		"required": []string{"es_url"},
	}
}

// Execute 执行数据接口采集
func (e *ESAlarmInterface) Execute(dataInterface *models.DataInterface, config map[string]interface{}, log *models.DataInterfaceLog) error {
	// 验证配置
	if err := e.ValidateConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}
	
	// 获取配置参数
	esURL := config["es_url"].(string)
	indexPrefix := "alarm_info" // 固定索引前缀

	// 目标组织列表为空，表示监控所有组织
	var targetOrganizations []string
	
	// 计算时间范围
	var startTime, endTime time.Time

	if dataInterface.TimeRangeType == "absolute" && dataInterface.StartTime != "" && dataInterface.EndTime != "" {
		// 使用绝对时间，解析为东八区时间（与ES数据时区一致）
		loc, _ := time.LoadLocation("Asia/Shanghai")
		var err error
		startTime, err = time.ParseInLocation("2006-01-02 15:04:05", dataInterface.StartTime, loc)
		if err != nil {
			return fmt.Errorf("解析开始时间失败: %v", err)
		}

		endTime, err = time.ParseInLocation("2006-01-02 15:04:05", dataInterface.EndTime, loc)
		if err != nil {
			return fmt.Errorf("解析结束时间失败: %v", err)
		}
	} else {
		// 使用相对时间
		timeRangeValue := dataInterface.TimeRangeValue
		if timeRangeValue <= 0 {
			timeRangeValue = 3600 // 默认1小时
		}

		endTime = time.Now()
		startTime = endTime.Add(-time.Duration(timeRangeValue) * time.Second)
	}
	
	timeRangeTypeDesc := "相对时间"
	if dataInterface.TimeRangeType == "absolute" {
		timeRangeTypeDesc = "绝对时间"
	}

	fmt.Printf("ES告警接口采集 - ES地址: %s, 索引前缀: %s, 时间类型: %s, 时间范围: %s 到 %s\n",
		esURL, indexPrefix, timeRangeTypeDesc,
		startTime.Format("2006-01-02 15:04:05"),
		endTime.Format("2006-01-02 15:04:05"))
	
	// 更新日志状态
	log.Message = "正在连接Elasticsearch并获取数据..."
	e.handler.db.Save(log)
	
	// 创建ES客户端
	if err := e.createESClient(esURL); err != nil {
		return fmt.Errorf("创建ES客户端失败: %v", err)
	}
	
	// 调用ES API获取数据
	records, err := e.searchESAlarmData(indexPrefix, startTime, endTime)
	if err != nil {
		return fmt.Errorf("ES查询失败: %v", err)
	}
	
	// 输出原始ES数据样本
	fmt.Printf("\n=== ES原始数据样本 ===\n")
	for i, record := range records {
		if i >= 5 { // 只显示前5条
			break
		}
		fmt.Printf("记录 %d: NodeCode=%s, SourceIP=%s, DestinationIP=%s, IncidentTypeSub=%v, ActionTimeShow=%s\n",
			i+1, record.NodeCode, record.SourceIP, record.DestinationIP, record.IncidentTypeSub, record.ActionTimeShow)
	}
	if len(records) > 5 {
		fmt.Printf("... 还有 %d 条记录\n", len(records)-5)
	}
	
	// 处理和保存数据
	processedCount, err := e.processESAlarmData(records, targetOrganizations)
	if err != nil {
		return fmt.Errorf("数据处理失败: %v", err)
	}
	
	// 更新执行日志
	log.MarkAsCompleted("success", fmt.Sprintf("成功采集并处理 %d 条数据", processedCount), processedCount)
	e.handler.db.Save(log)
	
	// 更新数据接口统计
	e.handler.updateDataInterfaceStats(dataInterface, "success", fmt.Sprintf("成功采集并处理 %d 条数据", processedCount), processedCount)
	
	fmt.Printf("ES告警接口采集完成 - 处理了 %d 条数据\n", processedCount)
	return nil
}

// createESClient 创建ES客户端
func (e *ESAlarmInterface) createESClient(esURL string) error {
	cfg := elasticsearch.Config{
		Addresses: []string{esURL},
	}

	client, err := elasticsearch.NewClient(cfg)
	if err != nil {
		return fmt.Errorf("创建Elasticsearch客户端失败: %v", err)
	}

	e.client = client
	
	// 测试连接
	res, err := e.client.Info()
	if err != nil {
		return fmt.Errorf("连接ES失败: %v", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("ES连接返回错误: %s", res.String())
	}

	fmt.Printf("ES连接成功\n")
	return nil
}

// searchESAlarmData 搜索ES告警数据
func (e *ESAlarmInterface) searchESAlarmData(indexPrefix string, startTime, endTime time.Time) ([]ESAlarmRecord, error) {
	// 生成测试索引（临时使用固定日期进行测试）
	testIndex := indexPrefix + "2025-07-23"
	indices := []string{testIndex}

	// TODO: 后续改为自动使用当前日期
	// todayIndex := indexPrefix + time.Now().Format("2006-01-02")
	// indices := []string{todayIndex}

	// 构建查询体
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					{
						"range": map[string]interface{}{
							"actionTimeShow": map[string]interface{}{
								"gte": startTime.Format("2006-01-02T15:04:05+08:00"),
								"lte": endTime.Format("2006-01-02T15:04:05+08:00"),
							},
						},
					},
				},
			},
		},
		"sort": []map[string]interface{}{
			{
				"actionTimeShow": map[string]interface{}{
					"order": "desc",
				},
			},
		},
		"size": 1000,
	}

	queryJSON, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("构建查询JSON失败: %v", err)
	}

	fmt.Printf("查询索引: %v\n", indices)
	fmt.Printf("查询语句: %s\n", string(queryJSON))

	// 执行搜索请求
	req := esapi.SearchRequest{
		Index:  indices,
		Body:   bytes.NewReader(queryJSON),
		Scroll: time.Minute,
	}

	res, err := req.Do(context.Background(), e.client)
	if err != nil {
		return nil, fmt.Errorf("执行搜索请求失败: %v", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("搜索请求返回错误: %s", res.String())
	}

	// 解析响应
	var searchResp ESSearchResponse
	if err := json.NewDecoder(res.Body).Decode(&searchResp); err != nil {
		return nil, fmt.Errorf("解析搜索响应失败: %v", err)
	}

	var allResults []map[string]interface{}

	// 处理第一批结果
	for _, hit := range searchResp.Hits.Hits {
		allResults = append(allResults, hit.Source)
	}

	fmt.Printf("第一批获取到 %d 条数据\n", len(searchResp.Hits.Hits))

	// 使用scroll API获取剩余所有结果
	scrollID := searchResp.ScrollID
	batchCount := 1

	for {
		if scrollID == "" {
			break
		}

		scrollReq := esapi.ScrollRequest{
			ScrollID: scrollID,
			Scroll:   time.Minute,
		}

		scrollRes, err := scrollReq.Do(context.Background(), e.client)
		if err != nil {
			fmt.Printf("执行scroll请求失败: %v\n", err)
			break
		}
		defer scrollRes.Body.Close()

		if scrollRes.IsError() {
			fmt.Printf("scroll请求返回错误: %s\n", scrollRes.String())
			break
		}

		var scrollResp ESSearchResponse
		if err := json.NewDecoder(scrollRes.Body).Decode(&scrollResp); err != nil {
			fmt.Printf("解析scroll响应失败: %v\n", err)
			break
		}

		if len(scrollResp.Hits.Hits) == 0 {
			break
		}

		batchCount++
		fmt.Printf("第%d批获取到 %d 条数据\n", batchCount, len(scrollResp.Hits.Hits))

		// 处理当前批次的结果
		for _, hit := range scrollResp.Hits.Hits {
			allResults = append(allResults, hit.Source)
		}

		scrollID = scrollResp.ScrollID
	}

	// 清除scroll上下文
	if scrollID != "" {
		clearReq := esapi.ClearScrollRequest{
			ScrollID: []string{scrollID},
		}
		clearRes, err := clearReq.Do(context.Background(), e.client)
		if err != nil {
			fmt.Printf("清除scroll上下文失败: %v\n", err)
		} else {
			clearRes.Body.Close()
		}
	}

	fmt.Printf("从ES总共获取到 %d 条告警数据\n", len(allResults))

	// 转换为ESAlarmRecord结构
	var records []ESAlarmRecord
	for _, result := range allResults {
		record := ESAlarmRecord{}

		if nodeCode, ok := result["nodeCode"].(string); ok {
			record.NodeCode = nodeCode
		}
		if sourceIP, ok := result["sourceIP"].(string); ok {
			record.SourceIP = sourceIP
		}
		if destinationIP, ok := result["destinationIP"].(string); ok {
			record.DestinationIP = destinationIP
		}
		if incidentTypeSub, ok := result["incidentTypeSub"]; ok {
			record.IncidentTypeSub = incidentTypeSub
		}
		if actionTimeShow, ok := result["actionTimeShow"].(string); ok {
			record.ActionTimeShow = actionTimeShow
		}
		if incidentName, ok := result["incidentName"].(string); ok {
			record.IncidentName = incidentName
		}
		if incidentLevel, ok := result["incidentLevel"]; ok {
			record.IncidentLevel = incidentLevel
		}
		if deviceBrand, ok := result["deviceBrand"].(string); ok {
			record.DeviceBrand = deviceBrand
		}
		if attackTime, ok := result["attackTime"].(string); ok {
			record.AttackTime = attackTime
		}
		if logID, ok := result["logId"].(string); ok {
			record.LogID = logID
		}

		records = append(records, record)
	}

	return records, nil
}



// processESAlarmData 处理ES告警数据
func (e *ESAlarmInterface) processESAlarmData(records []ESAlarmRecord, targetOrganizations []string) (int, error) {
	// 使用攻击流合并逻辑
	attackSummaries, internalCount, whitelistCount, filteredCount := e.mergeESAlarmDataWithDedup(records, targetOrganizations)

	fmt.Printf("数据处理统计 - 原始记录: %d, 内网流量: %d, 白名单: %d, 非目标组织: %d, 最终合并记录: %d\n",
		len(records), internalCount, whitelistCount, filteredCount, len(attackSummaries))

	if len(attackSummaries) == 0 {
		fmt.Println("没有有效的攻击数据需要保存")
		return 0, nil
	}

	// 转换为IOC源数据格式并保存
	var sourceDataList []models.IOCIntelligenceData
	var allProcessedLogIDs []string

	for _, summary := range attackSummaries {
		// 获取主要类别
		mainCategory := e.getMainCategory(summary.Categories)

		// 确定来源标签
		sourceLabel := e.getESSourceLabel(summary.NodeCode)

		sourceData := models.IOCIntelligenceData{
			UUID:            summary.UUID,
			AttackIP:        summary.SourceIP,
			VictimIP:        summary.DestinationIP,
			SourceLabel:     sourceLabel,
			Category:        e.getESAttackCategoryName(mainCategory),
			AttackCount:     summary.AttackCount,
			FirstAttackTime: fmt.Sprintf("%d", summary.FirstSeen),
			LastAttackTime:  fmt.Sprintf("%d", summary.LastSeen),
			ThreatScore:     e.calculateThreatScore(summary.Levels, summary.AttackCount),
		}
		sourceDataList = append(sourceDataList, sourceData)

		// 收集所有LogID用于标记为已处理
		allProcessedLogIDs = append(allProcessedLogIDs, summary.LogIDs...)
	}

	// 批量保存IOC源数据
	if err := e.BatchSaveIOCSourceData(sourceDataList); err != nil {
		return 0, fmt.Errorf("保存IOC源数据失败: %v", err)
	}

	// 批量保存所有已处理的LogID
	if err := e.BatchSaveProcessedUUIDs(allProcessedLogIDs, "es_alarm", e.dedupConfig.SaveBatchSize); err != nil {
		fmt.Printf("批量保存LogID失败: %v\n", err)
		// 如果批量保存失败，回退到单个保存模式
		savedCount := 0
		for _, logID := range allProcessedLogIDs {
			if logID != "" && !e.IsUUIDProcessed(logID, "es_alarm") {
				if err := e.SaveProcessedUUID(logID, "es_alarm"); err != nil {
					fmt.Printf("保存LogID失败: %s, 错误: %v\n", logID, err)
				} else {
					savedCount++
				}
			}
		}
		fmt.Printf("回退模式保存了 %d 个已处理的LogID\n", savedCount)
	}

	fmt.Printf("成功保存 %d 条IOC源数据\n", len(sourceDataList))
	return len(sourceDataList), nil
}

// mergeESAlarmDataWithDedup 使用LogID去重和攻击流合并处理ES告警数据
func (e *ESAlarmInterface) mergeESAlarmDataWithDedup(records []ESAlarmRecord, targetOrganizations []string) (map[string]*ESAlarmSummary, int, int, int) {
	internalCount := 0
	whitelistCount := 0
	filteredCount := 0

	// 根据数据量优化去重配置
	e.OptimizeDedupConfigForDataSize(len(records))
	fmt.Printf("数据量: %d, 使用配置 - 批次大小: %d, 工作协程: %d\n",
		len(records), e.dedupConfig.BatchSize, e.dedupConfig.WorkerCount)

	// 第一步：基础过滤，收集需要检查的LogID
	var candidateRecords []ESAlarmRecord
	var logIDsToCheck []string

	for _, record := range records {
		// 处理ES告警数据的UUID：如果LogID不为空就使用LogID，否则生成假UUID
		if record.LogID == "" {
			record.LogID = e.generateESAlarmUUID(record.SourceIP, record.DestinationIP, record.ActionTimeShow)
			fmt.Printf("为ES告警生成假UUID: %s (SourceIP=%s, DestinationIP=%s, Time=%s)\n",
				record.LogID, record.SourceIP, record.DestinationIP, record.ActionTimeShow)
		} else {
			fmt.Printf("使用ES告警原有LogID: %s (SourceIP=%s, DestinationIP=%s)\n",
				record.LogID, record.SourceIP, record.DestinationIP)
		}

		// 统计内网流量（包括海事局内网过滤）
		if e.isESInternalTraffic(record.SourceIP, record.DestinationIP, record.NodeCode) {
			internalCount++
			continue
		}

		// 检查是否涉及目标组织
		if !e.ShouldIncludeAttackFlow(record.SourceIP, record.DestinationIP, targetOrganizations) {
			filteredCount++
			continue
		}

		// 检查白名单
		if e.IsWhitelistedIP(record.SourceIP) || e.IsWhitelistedIP(record.DestinationIP) {
			whitelistCount++
			continue
		}

		// 通过基础过滤的记录，加入候选列表
		candidateRecords = append(candidateRecords, record)
		logIDsToCheck = append(logIDsToCheck, record.LogID)
	}

	fmt.Printf("基础过滤完成 - 候选记录: %d 条\n", len(candidateRecords))

	// 第二步：批量LogID去重检查
	var validRecords []ESAlarmRecord

	if len(logIDsToCheck) > 0 {
		// 使用优化的批量检查LogID是否已处理
		processedMap, err := e.OptimizedBatchCheckUUIDsProcessed(logIDsToCheck, "es_alarm")
		if err != nil {
			fmt.Printf("优化LogID去重检查失败，回退到单线程模式: %v\n", err)
			// 回退到原有的单线程模式
			for _, record := range candidateRecords {
				if !e.IsUUIDProcessed(record.LogID, "es_alarm") {
					validRecords = append(validRecords, record)
				}
			}
		} else {
			// 使用批量检查结果过滤
			for _, record := range candidateRecords {
				if !processedMap[record.LogID] {
					validRecords = append(validRecords, record)
				}
			}
		}
	}

	fmt.Printf("LogID去重后有效记录: %d 条\n", len(validRecords))

	// 第三步：攻击流合并 - 按攻击流（SourceIP->DestinationIP）分组
	attackFlowMap := make(map[string]*ESAlarmSummary)

	for _, record := range validRecords {
		// 生成攻击流键：攻击IP -> 受害IP
		flowKey := fmt.Sprintf("%s->%s", record.SourceIP, record.DestinationIP)

		// 解析时间戳
		timestamp := e.parseActionTime(record.ActionTimeShow)

		if existing, exists := attackFlowMap[flowKey]; exists {
			// 更新现有记录
			existing.AttackCount++
			existing.LogIDs = append(existing.LogIDs, record.LogID)

			// 更新时间范围
			if timestamp < existing.FirstSeen {
				existing.FirstSeen = timestamp
			}
			if timestamp > existing.LastSeen {
				existing.LastSeen = timestamp
			}

			// 统计类别
			if existing.Categories == nil {
				existing.Categories = make(map[string]int)
			}
			category := e.convertIncidentTypeSub(record.IncidentTypeSub)
			existing.Categories[category]++

			// 统计级别
			if existing.Levels == nil {
				existing.Levels = make(map[string]int)
			}
			level := e.convertIncidentLevel(record.IncidentLevel)
			existing.Levels[level]++
		} else {
			// 创建新记录
			category := e.convertIncidentTypeSub(record.IncidentTypeSub)
			level := e.convertIncidentLevel(record.IncidentLevel)

			attackFlowMap[flowKey] = &ESAlarmSummary{
				UUID:          record.LogID,
				SourceIP:      record.SourceIP,
				DestinationIP: record.DestinationIP,
				NodeCode:      record.NodeCode,
				SourceLabel:   e.getESSourceLabel(record.NodeCode),
				AttackCount:   1,
				Categories:    map[string]int{category: 1},
				Levels:        map[string]int{level: 1},
				FirstSeen:     timestamp,
				LastSeen:      timestamp,
				SampleRecord:  record,
				LogIDs:        []string{record.LogID},
			}
		}
	}

	fmt.Printf("攻击流合并后记录: %d 条\n", len(attackFlowMap))

	return attackFlowMap, internalCount, whitelistCount, filteredCount
}

// isESInternalTraffic 检查是否为ES告警的内网流量（包括海事局特殊过滤）
func (e *ESAlarmInterface) isESInternalTraffic(sourceIP, destinationIP, nodeCode string) bool {
	// 检查是否都是标准内网地址
	sourceIsInternal := e.IsInternalIP(sourceIP)
	destIsInternal := e.IsInternalIP(destinationIP)

	// 过滤内网到内网的流量
	if sourceIsInternal && destIsInternal {
		return true
	}

	// 特殊过滤：部海事局(节点代码2013)的内网IP地址 198.10-30.*.*
	if nodeCode == "2013" {
		sourceIsMaritimeInternal := e.isMaritimeInternalIP(sourceIP)
		destIsMaritimeInternal := e.isMaritimeInternalIP(destinationIP)

		if sourceIsMaritimeInternal && destIsMaritimeInternal {
			return true
		}
	}

	return false
}

// generateESAlarmUUID 为ES告警数据生成唯一UUID
func (e *ESAlarmInterface) generateESAlarmUUID(sourceIP, destinationIP, actionTimeShow string) string {
	// 使用MD5哈希生成确定性的UUID
	// 格式: "es_alarm_" + MD5(sourceIP + "|" + destinationIP + "|" + actionTimeShow)
	data := fmt.Sprintf("%s|%s|%s", sourceIP, destinationIP, actionTimeShow)
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("es_alarm_%x", hash)
}

// isMaritimeInternalIP 检查是否为海事局内网IP地址 (198.10-30.*.*)
func (e *ESAlarmInterface) isMaritimeInternalIP(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	// 检查海事局内网地址范围 198.10-30.0.0/16
	if ipv4 := parsedIP.To4(); ipv4 != nil {
		if ipv4[0] == 198 && ipv4[1] >= 10 && ipv4[1] <= 30 {
			return true
		}
	}

	return false
}

// getESSourceLabel 获取ES告警专用的来源标签（直接使用nodeCode）
func (e *ESAlarmInterface) getESSourceLabel(nodeCode string) string {
	// 节点代码映射（基于ES_18_Alarm.go中的GetNodeCodeMapping）
	nodeMapping := map[string]string{
		"2001": "交通运输部救助打捞局",
		"2002": "船级社",
		"2003": "通信中心",
		"2004": "资格中心",
		"2005": "交科院",
		"2006": "公路院",
		"2007": "水运院",
		"2008": "规划院",
		"2009": "天科院",
		"2010": "路网中心",
		"2011": "大连海大",
		"2012": "交干院",
		"2013": "部海事局",
		"2014": "长航局",
		"2015": "珠航局",
		"2016": "交通报社",
		"2017": "交通出版社",
		"2018": "国际中心",
	}

	if label, exists := nodeMapping[nodeCode]; exists {
		return label
	}

	return "未知组织"
}

// getESAttackCategoryName 获取ES告警专用的攻击类别名称
func (e *ESAlarmInterface) getESAttackCategoryName(categoryCode string) string {
	// ES告警完整攻击类别映射表
	categoryMapping := map[string]string{
		// 恶意代码类别
		"20101": "恶意代码",
		"20102": "木马",
		"20103": "网络蠕虫",
		"20104": "僵尸程序",
		"20105": "Webshell",
		"20106": "挖矿软件",
		"20107": "勒索软件",
		"20108": "流氓软件",
		"20109": "病毒感染",
		"20199": "其他有害程序",

		// 主机异常类别
		"20201": "主机异常",
		"20202": "流量异常",
		"20203": "终端行为异常",
		"20204": "网络行为异常",
		"20205": "账号异常",
		"20206": "非法外联",
		"20299": "其他异常行为",

		// 扫描类别
		"20301": "Web扫描",
		"20302": "漏洞扫描",
		"20303": "端口扫描",
		"20304": "主机存活扫描",
		"20305": "服务扫描",
		"20306": "扫描器指纹",
		"20399": "其他扫描行为",

		// 攻击类别
		"20401": "拒绝服务攻击",
		"20402": "Web攻击",
		"20403": "横向渗透",
		"20404": "漏洞利用攻击",
		"20405": "账号暴力破解",
		"20406": "权限许可和访问控制",
		"20407": "命令执行",
		"20408": "邮件攻击",
		"20409": "隐秘隧道",
		"20410": "系统后门",
		"20499": "其他网络攻击",

		// 信息破坏类别
		"20501": "信息泄露",
		"20502": "信息窃取",
		"20503": "信息篡改",
		"20599": "其他信息破坏",

		// 风险类别
		"20601": "漏洞风险",
		"20602": "配置风险",
		"20603": "端口风险",
		"20604": "未授权访问",
		"20605": "弱口令",
		"20606": "明文密码",
		"20699": "其他隐患风险",
	}

	if categoryName, exists := categoryMapping[categoryCode]; exists {
		return categoryName
	}

	// 如果没有映射，返回原始代码
	return categoryCode
}

// parseActionTime 解析actionTimeShow时间字符串为时间戳
func (e *ESAlarmInterface) parseActionTime(timeStr string) int64 {
	if timeStr == "" {
		return time.Now().Unix()
	}

	// 尝试多种时间格式
	formats := []string{
		"2006-01-02T15:04:05-0700",
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return t.Unix()
		}
	}

	// 如果都解析失败，返回当前时间
	fmt.Printf("无法解析时间字符串: %s\n", timeStr)
	return time.Now().Unix()
}

// convertIncidentTypeSub 转换事件类型子类
func (e *ESAlarmInterface) convertIncidentTypeSub(incidentTypeSub interface{}) string {
	if incidentTypeSub == nil {
		return "未知攻击类别"
	}

	switch v := incidentTypeSub.(type) {
	case string:
		return v
	case float64:
		return fmt.Sprintf("%.0f", v)
	case int:
		return fmt.Sprintf("%d", v)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// convertIncidentLevel 转换事件级别
func (e *ESAlarmInterface) convertIncidentLevel(incidentLevel interface{}) string {
	if incidentLevel == nil {
		return "未知级别"
	}

	switch v := incidentLevel.(type) {
	case string:
		return v
	case float64:
		level := int(v)
		switch level {
		case 1:
			return "低危"
		case 2:
			return "中危"
		case 3:
			return "高危"
		case 4:
			return "严重"
		default:
			return fmt.Sprintf("级别%d", level)
		}
	case int:
		switch v {
		case 1:
			return "低危"
		case 2:
			return "中危"
		case 3:
			return "高危"
		case 4:
			return "严重"
		default:
			return fmt.Sprintf("级别%d", v)
		}
	default:
		return fmt.Sprintf("%v", v)
	}
}

// getMainCategory 获取主要攻击类别
func (e *ESAlarmInterface) getMainCategory(categories map[string]int) string {
	if len(categories) == 0 {
		return ""
	}

	maxCount := 0
	mainCategory := ""
	for category, count := range categories {
		if count > maxCount {
			maxCount = count
			mainCategory = category
		}
	}
	return mainCategory
}

// calculateThreatScore 计算威胁评分
func (e *ESAlarmInterface) calculateThreatScore(levels map[string]int, attackCount int) float64 {
	// 基础分数：根据最高级别计算
	var baseScore float64 = 0
	for level := range levels {
		var score float64
		switch level {
		case "严重":
			score = 9.0
		case "高危":
			score = 7.0
		case "中危":
			score = 5.0
		case "低危":
			score = 3.0
		default:
			score = 2.0
		}
		if score > baseScore {
			baseScore = score
		}
	}

	// 频次系数
	var frequencyMultiplier float64 = 1.0
	if attackCount >= 100 {
		frequencyMultiplier = 1.8
	} else if attackCount >= 50 {
		frequencyMultiplier = 1.6
	} else if attackCount >= 20 {
		frequencyMultiplier = 1.4
	} else if attackCount >= 10 {
		frequencyMultiplier = 1.2
	} else if attackCount >= 5 {
		frequencyMultiplier = 1.1
	}

	// 计算最终评分
	finalScore := baseScore * frequencyMultiplier

	// 限制评分范围在1-10之间
	if finalScore > 10.0 {
		finalScore = 10.0
	} else if finalScore < 1.0 {
		finalScore = 1.0
	}

	return finalScore
}

// 以下是公开方法，用于测试和示例

// GetESSourceLabel 获取ES告警专用的来源标签（公开方法）
func (e *ESAlarmInterface) GetESSourceLabel(nodeCode string) string {
	return e.getESSourceLabel(nodeCode)
}

// IsMaritimeInternalIP 检查是否为海事局内网IP地址（公开方法）
func (e *ESAlarmInterface) IsMaritimeInternalIP(ip string) bool {
	return e.isMaritimeInternalIP(ip)
}

// ConvertIncidentTypeSub 转换事件类型子类（公开方法）
func (e *ESAlarmInterface) ConvertIncidentTypeSub(incidentTypeSub interface{}) string {
	return e.convertIncidentTypeSub(incidentTypeSub)
}

// ConvertIncidentLevel 转换事件级别（公开方法）
func (e *ESAlarmInterface) ConvertIncidentLevel(incidentLevel interface{}) string {
	return e.convertIncidentLevel(incidentLevel)
}

// CalculateThreatScore 计算威胁评分（公开方法）
func (e *ESAlarmInterface) CalculateThreatScore(levels map[string]int, attackCount int) float64 {
	return e.calculateThreatScore(levels, attackCount)
}
