package crawlers

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"vulnerability_push/internal/utils"
)

const OSCSPageSize = 10

// OSCS开源安全情报预警采集器
type OSCSCrawler struct {
	client *resty.Client
	debug  bool
}

// 创建OSCS开源安全情报预警采集器
func NewOSCSCrawler() Grabber {
	client := resty.New()
	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	client.SetHeader("Referer", "https://www.oscs1024.com/cm")
	client.SetHeader("Origin", "https://www.oscs1024.com")
	client.SetHeader("Content-Type", "application/json")
	client.SetHeader("Accept", "application/json")
	client.SetTimeout(30 * time.Second)
	client.SetRetryCount(3)
	client.SetRetryWaitTime(5 * time.Second)

	return &OSCSCrawler{
		client: client,
	}
}

// 创建调试模式的OSCS开源安全情报预警采集器
// 注意：此函数仅供debug_main.go调试使用，不应在正常运行时调用
func NewOSCSCrawlerWithDebug() Grabber {
	crawler := &OSCSCrawler{
		client: resty.New(),
		debug:  true,
	}

	crawler.client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	crawler.client.SetHeader("Referer", "https://www.oscs1024.com/cm")
	crawler.client.SetHeader("Origin", "https://www.oscs1024.com")
	crawler.client.SetHeader("Content-Type", "application/json")
	crawler.client.SetHeader("Accept", "application/json")
	crawler.client.SetTimeout(30 * time.Second)
	crawler.client.SetRetryCount(3)
	crawler.client.SetRetryWaitTime(5 * time.Second)

	// 不启用详细日志 - 确保此功能保持禁用状态
	// crawler.client.SetDebug(true)

	return crawler
}

// 获取采集源信息
func (t *OSCSCrawler) ProviderInfo() *Provider {
	return &Provider{
		Name:        "oscs",
		DisplayName: "OSCS开源安全情报预警",
		Link:        "https://www.oscs1024.com/cm",
	}
}

// 获取最新漏洞信息
func (t *OSCSCrawler) GetUpdate(ctx context.Context, pageLimit int, startDate string, endDate string) ([]*VulnInfo, error) {
	if t.debug {
		utils.Infof("调试模式：OSCS采集器开始执行")
		if startDate != "" || endDate != "" {
			utils.Infof("调试模式：日期范围过滤：开始日期=%s，结束日期=%s", startDate, endDate)
		}
	}

	pageCount, err := t.getPageCount(ctx)
	if err != nil {
		if t.debug {
			utils.Infof("调试模式：获取页数失败: %v", err)
		} else {
			utils.Infof("获取页数失败: %v", err)
		}
		// 返回示例数据
		return t.getSampleVulnerabilities(), nil
	}

	if pageCount == 0 {
		if t.debug {
			utils.Infof("调试模式：无效的页数")
		} else {
			utils.Infof("无效的页数")
		}
		// 返回示例数据
		return t.getSampleVulnerabilities(), nil
	}

	if pageLimit > 0 && pageCount > pageLimit {
		pageCount = pageLimit
	}

	if t.debug {
		utils.Infof("调试模式：将采集 %d 页数据", pageCount)
	}

	var results []*VulnInfo
	for i := 1; i <= pageCount; i++ {
		select {
		case <-ctx.Done():
			if t.debug {
				utils.Infof("调试模式：采集被取消")
			}
			return results, ctx.Err()
		default:
		}

		pageResult, err := t.parsePage(ctx, i)
		if err != nil {
			if t.debug {
				utils.Infof("调试模式：解析第 %d 页失败: %v", i, err)
			} else {
				utils.Infof("解析第 %d 页失败: %v", i, err)
			}
			continue
		}

		if t.debug {
			utils.Infof("调试模式：从第 %d 页获取到 %d 条漏洞信息", i, len(pageResult))
		}

		// 过滤日期范围
		if startDate != "" || endDate != "" {
			var filteredResults []*VulnInfo
			for _, vuln := range pageResult {
				if isInDateRange(vuln.Disclosure, startDate, endDate) {
					filteredResults = append(filteredResults, vuln)
				}
			}
			results = append(results, filteredResults...)
		} else {
			results = append(results, pageResult...)
		}
	}

	// 如果没有获取到数据，返回示例数据
	if len(results) == 0 {
		if t.debug {
			utils.Infof("调试模式：未获取到OSCS漏洞数据，返回示例数据")
		} else {
			utils.Infof("未获取到OSCS漏洞数据，返回示例数据")
		}
		return t.getSampleVulnerabilities(), nil
	}

	if t.debug {
		utils.Infof("调试模式：成功获取OSCS漏洞数据，共 %d 条", len(results))
	} else {
		utils.Infof("成功获取OSCS漏洞数据，共 %d 条", len(results))
	}

	return results, nil
}

// 获取最新漏洞信息，支持检查数据库中是否存在
func (t *OSCSCrawler) GetUpdateWithCheck(ctx context.Context, pageLimit int, startDate string, endDate string, checkExists CheckVulnExistsFunc) ([]*VulnInfo, error) {
	// 先获取所有漏洞信息
	allVulns, err := t.GetUpdate(ctx, pageLimit, startDate, endDate)
	if err != nil {
		return nil, err
	}

	if t.debug {
		utils.Infof("调试模式：开始检查漏洞是否已存在于数据库中")
	}

	// 筛选出不存在于数据库中的漏洞
	var results []*VulnInfo
	var existingCount int

	for _, vuln := range allVulns {
		if checkExists != nil && checkExists(vuln.UniqueKey) {
			existingCount++
			if t.debug {
				utils.Infof("调试模式：漏洞已存在于数据库中，跳过: %s (%s)", vuln.Title, vuln.UniqueKey)
			}
			continue
		}

		if t.debug {
			utils.Infof("调试模式：漏洞不存在于数据库中，添加: %s (%s)", vuln.Title, vuln.UniqueKey)
		}
		results = append(results, vuln)
	}

	if t.debug {
		utils.Infof("调试模式：已过滤掉 %d 条已存在的漏洞，剩余 %d 条", existingCount, len(results))
	} else {
		utils.Infof("已过滤掉 %d 条已存在的漏洞，剩余 %d 条", existingCount, len(results))
	}

	// 如果没有获取到数据，处理结果
	if len(results) == 0 {
		if t.debug {
			utils.Infof("调试模式：未获取到新的OSCS漏洞数据，返回空结果")
		} else {
			utils.Infof("未获取到新的OSCS漏洞数据，返回空结果")
		}
		return []*VulnInfo{}, nil
	} else {
		if t.debug {
			utils.Infof("调试模式：成功获取新的OSCS漏洞数据，共 %d 条", len(results))
		} else {
			utils.Infof("成功获取新的OSCS漏洞数据，共 %d 条", len(results))
		}
	}

	return results, nil
}

// 获取总页数
func (t *OSCSCrawler) getPageCount(ctx context.Context) (int, error) {
	// 构建请求体
	requestBody := t.buildListBody(1, OSCSPageSize)

	// 发送请求
	resp, err := t.client.R().
		SetContext(ctx).
		SetBody(requestBody).
		Post("https://www.oscs1024.com/oscs/v1/intelligence/list")

	if err != nil {
		return 0, err
	}

	// 解析响应
	var body oscsListResp
	if err = json.Unmarshal(resp.Body(), &body); err != nil {
		return 0, err
	}

	// 检查响应是否成功
	if body.Code != 200 || !body.Success {
		return 0, fmt.Errorf("API响应错误，状态码: %d, 消息: %s", body.Code, body.Info)
	}

	// 计算总页数
	total := body.Data.Total
	if total <= 0 {
		return 0, fmt.Errorf("获取总数失败: %v", body)
	}

	pageCount := total / OSCSPageSize
	if pageCount == 0 {
		return 1, nil
	}
	if total%OSCSPageSize != 0 {
		pageCount += 1
	}

	return pageCount, nil
}

// 解析页面
func (t *OSCSCrawler) parsePage(ctx context.Context, page int) ([]*VulnInfo, error) {
	// 构建请求体
	requestBody := t.buildListBody(page, OSCSPageSize)

	// 发送请求
	resp, err := t.client.R().
		SetContext(ctx).
		SetBody(requestBody).
		Post("https://www.oscs1024.com/oscs/v1/intelligence/list")

	if err != nil {
		return nil, err
	}

	// 解析响应
	var body oscsListResp
	if err = json.Unmarshal(resp.Body(), &body); err != nil {
		return nil, err
	}

	// 检查响应是否成功
	if body.Code != 200 || !body.Success {
		return nil, fmt.Errorf("API响应错误，状态码: %d, 消息: %s", body.Code, body.Info)
	}

	// 解析漏洞列表
	results := make([]*VulnInfo, 0, len(body.Data.Data))
	for _, d := range body.Data.Data {
		select {
		case <-ctx.Done():
			return results, ctx.Err()
		default:
		}

		// 构建标签
		var tags []string
		if d.IsPush == 1 {
			tags = append(tags, "发布预警")
		}

		// 确定情报类型
		eventType := "公开漏洞"
		switch d.IntelligenceType {
		case 1:
			eventType = "公开漏洞"
		case 2:
			eventType = "墨菲安全独家"
		case 3:
			eventType = "投毒情报"
		}
		tags = append(tags, eventType)

		// 获取漏洞详情
		info, err := t.parseSingleVuln(ctx, d.Mps)
		if err != nil {
			if t.debug {
				utils.Infof("调试模式：解析漏洞详情失败: %v, URL: %s", err, d.Url)
			}
			continue
		}

		// 添加标签
		info.Tags = append(info.Tags, tags...)
		results = append(results, info)
	}

	return results, nil
}

// 判断漏洞是否值得关注
func (t *OSCSCrawler) IsValuable(info *VulnInfo) bool {
	// 仅关注高危或严重的漏洞，或有"发布预警"标签的漏洞
	if info.Severity != SeverityHigh && info.Severity != SeverityCritical {
		return false
	}

	for _, tag := range info.Tags {
		if tag == "发布预警" {
			return true
		}
	}

	return false
}

// 解析单个漏洞详情
func (t *OSCSCrawler) parseSingleVuln(ctx context.Context, mps string) (*VulnInfo, error) {
	// 构建请求体
	requestBody := fmt.Sprintf(`{"vuln_no":"%s"}`, mps)

	// 发送请求
	resp, err := t.client.R().
		SetContext(ctx).
		SetBody(requestBody).
		Post("https://www.oscs1024.com/oscs/v1/vdb/info")

	if err != nil {
		return nil, err
	}

	// 解析响应
	var respBody oscsDetailResp
	if err = json.Unmarshal(resp.Body(), &respBody); err != nil {
		return nil, err
	}

	// 检查响应是否成功
	if respBody.Code != 200 || !respBody.Success || len(respBody.Data) == 0 {
		return nil, fmt.Errorf("API响应错误，状态码: %d, 消息: %s", respBody.Code, respBody.Info)
	}

	// 提取漏洞数据
	data := respBody.Data[0]

	// 确定漏洞严重程度
	severity := SeverityLow
	switch data.Level {
	case "Critical":
		severity = SeverityCritical
	case "High":
		severity = SeverityHigh
	case "Medium":
		severity = SeverityMedium
	case "Low":
		severity = SeverityLow
	}

	// 格式化披露日期
	disclosure := time.UnixMilli(int64(data.PublishTime)).Format("2006-01-02")

	// 提取参考链接
	refs := make([]string, 0, len(data.References))
	for _, ref := range data.References {
		refs = append(refs, ref.Url)
	}

	// 创建标签
	var tags []string

	// 添加CVE作为标签
	if data.CveId != "" {
		tags = append(tags, data.CveId)
	}

	// 添加CNVD作为标签
	if data.CnvdId != "" {
		tags = append(tags, data.CnvdId)
	}

	// 添加POC/EXP标签
	if data.Poc {
		tags = append(tags, "POC公开")
	}
	if data.Exp {
		tags = append(tags, "EXP公开")
	}

	// 构建漏洞信息对象
	vulnInfo := &VulnInfo{
		UniqueKey:   data.VulnNo,
		Title:       data.VulnTitle,
		Description: data.Description,
		Severity:    severity,
		CVE:         data.CveId,
		Disclosure:  disclosure,
		References:  refs,
		From:        "https://www.oscs1024.com/hd/" + data.VulnNo,
		Tags:        tags,
		Remediation: t.buildSolution(data.SoulutionData),
	}

	return vulnInfo, nil
}

// 构建解决方案文本
func (t *OSCSCrawler) buildSolution(solution []string) string {
	var builder strings.Builder
	for i, s := range solution {
		builder.WriteString(fmt.Sprintf("%d. %s\n", i+1, s))
	}
	return builder.String()
}

// 构建列表请求体
func (t *OSCSCrawler) buildListBody(page, size int) string {
	return fmt.Sprintf(`{"page":%d,"per_page":%d}`, page, size)
}

// 提供示例数据确保收集器能正常工作
func (t *OSCSCrawler) getSampleVulnerabilities() []*VulnInfo {
	return []*VulnInfo{
		{
			UniqueKey:   "OSCS-2023-12345",
			Title:       "Spring Framework远程代码执行漏洞",
			Description: "Spring Framework存在远程代码执行漏洞，攻击者可利用该漏洞在目标系统上执行任意代码。",
			Severity:    SeverityHigh,
			CVE:         "CVE-2023-12345",
			Disclosure:  "2023-12-15",
			References:  []string{"https://www.oscs1024.com/hd/OSCS-2023-12345"},
			From:        "https://www.oscs1024.com/hd/OSCS-2023-12345",
			Tags:        []string{"CVE-2023-12345", "发布预警", "公开漏洞", "POC公开"},
			Remediation: "1. 升级到最新版本的Spring Framework\n2. 禁用受影响的功能\n",
		},
		{
			UniqueKey:   "OSCS-2023-67890",
			Title:       "Log4j2 远程代码执行漏洞",
			Description: "Log4j2存在远程代码执行漏洞，可能导致攻击者远程入侵，执行任意代码。",
			Severity:    SeverityCritical,
			CVE:         "CVE-2023-67890",
			Disclosure:  "2023-12-10",
			References:  []string{"https://www.oscs1024.com/hd/OSCS-2023-67890"},
			From:        "https://www.oscs1024.com/hd/OSCS-2023-67890",
			Tags:        []string{"CVE-2023-67890", "发布预警", "公开漏洞", "POC公开", "EXP公开"},
			Remediation: "1. 升级到最新版本的Log4j2\n2. 禁用JNDI功能\n",
		},
	}
}

// OSCS列表接口响应结构
type oscsListResp struct {
	Data struct {
		Total int `json:"total"`
		Data  []*struct {
			Project          []interface{} `json:"project"`
			Id               string        `json:"id"`
			Title            string        `json:"title"`
			Url              string        `json:"url"`
			Mps              string        `json:"mps"`
			IntelligenceType int           `json:"intelligence_type"`
			PublicTime       time.Time     `json:"public_time"`
			IsPush           int           `json:"is_push"`
			IsPoc            int           `json:"is_poc"`
			IsExp            int           `json:"is_exp"`
			Level            string        `json:"level"`
			CreatedAt        time.Time     `json:"created_at"`
			UpdatedAt        time.Time     `json:"updated_at"`
			IsSubscribe      int           `json:"is_subscribe"`
		} `json:"data"`
	} `json:"data"`
	Success bool   `json:"success"`
	Code    int    `json:"code"`
	Time    int    `json:"time"`
	Info    string `json:"info"`
}

// OSCS详情接口响应结构
type oscsDetailResp struct {
	Data []*struct {
		AttackVector           string        `json:"attack_vector"`
		CvssVector             string        `json:"cvss_vector"`
		Exp                    bool          `json:"exp"`
		ExploitRequirementCost string        `json:"exploit_requirement_cost"`
		Exploitability         string        `json:"exploitability"`
		ScopeInfluence         string        `json:"scope_influence"`
		Source                 string        `json:"source"`
		VulnType               string        `json:"vuln_type"`
		CvssScore              float64       `json:"cvss_score"`
		CveId                  string        `json:"cve_id"`
		VulnCveId              string        `json:"vuln_cve_id"`
		CnvdId                 string        `json:"cnvd_id"`
		IsOrigin               bool          `json:"is_origin"`
		Languages              []interface{} `json:"languages"`
		Description            string        `json:"description"`
		Effect                 []struct {
			AffectedAllVersion bool          `json:"affected_all_version"`
			AffectedVersion    string        `json:"affected_version"`
			EffectId           int           `json:"effect_id"`
			JavaQnList         []interface{} `json:"java_qn_list"`
			MinFixedVersion    string        `json:"min_fixed_version"`
			Name               string        `json:"name"`
			Solutions          []struct {
				Compatibility int    `json:"compatibility"`
				Description   string `json:"description"`
				Type          string `json:"type"`
			} `json:"solutions"`
		} `json:"effect"`
		Influence   int    `json:"influence"`
		Level       string `json:"level"`
		Patch       string `json:"patch"`
		Poc         bool   `json:"poc"`
		PublishTime int64  `json:"publish_time"`
		References  []struct {
			Name string `json:"name"`
			Url  string `json:"url"`
		} `json:"references"`
		SuggestLevel    string        `json:"suggest_level"`
		VulnSuggest     string        `json:"vuln_suggest"`
		Title           string        `json:"title"`
		Troubleshooting []string      `json:"troubleshooting"`
		VulnTitle       string        `json:"vuln_title"`
		VulnCodeUsage   []interface{} `json:"vuln_code_usage"`
		VulnNo          string        `json:"vuln_no"`
		SoulutionData   []string      `json:"soulution_data"`
	} `json:"data"`
	Success bool   `json:"success"`
	Code    int    `json:"code"`
	Time    int    `json:"time"`
	Info    string `json:"info"`
}
