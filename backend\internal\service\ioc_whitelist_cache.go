package service

import (
	"fmt"
	"net"
	"strings"
	"sync"
	"time"

	"gorm.io/gorm"
	"vulnerability_push/internal/models"
	"vulnerability_push/internal/utils"
)

// IOCWhitelistCache IOC白名单内存缓存管理器
type IOCWhitelistCache struct {
	db       *gorm.DB
	mutex    sync.RWMutex
	ipCache  map[string]bool    // IP白名单缓存
	domCache map[string]bool    // 域名白名单缓存
	lastLoad time.Time          // 最后加载时间
}

// NewIOCWhitelistCache 创建IOC白名单缓存管理器
func NewIOCWhitelistCache(db *gorm.DB) *IOCWhitelistCache {
	cache := &IOCWhitelistCache{
		db:       db,
		ipCache:  make(map[string]bool),
		domCache: make(map[string]bool),
	}

	// 初始化时加载白名单
	if err := cache.LoadFromDatabase(); err != nil {
		utils.Errorf("初始化IOC白名单缓存失败: %v", err)
	}

	return cache
}

// LoadFromDatabase 从数据库加载白名单到内存
func (c *IOCWhitelistCache) LoadFromDatabase() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 清空现有缓存
	c.ipCache = make(map[string]bool)
	c.domCache = make(map[string]bool)

	// 从数据库查询所有白名单记录
	var whitelists []models.IOCWhitelist
	if err := c.db.Find(&whitelists).Error; err != nil {
		return fmt.Errorf("查询IOC白名单失败: %v", err)
	}

	// 分类加载到缓存
	ipCount := 0
	domainCount := 0
	
	for _, whitelist := range whitelists {
		switch strings.ToLower(whitelist.IOCType) {
		case "ip":
			c.ipCache[whitelist.IOC] = true
			ipCount++
		case "domain":
			c.domCache[strings.ToLower(whitelist.IOC)] = true
			domainCount++
		}
	}

	c.lastLoad = time.Now()
	utils.Infof("IOC白名单缓存加载完成: IP %d 个, 域名 %d 个", ipCount, domainCount)
	
	return nil
}

// IsIPWhitelisted 检查IP是否在白名单中
func (c *IOCWhitelistCache) IsIPWhitelisted(ip string) bool {
	if ip == "" {
		return false
	}

	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	return c.ipCache[ip]
}

// IsDomainWhitelisted 检查域名是否在白名单中
func (c *IOCWhitelistCache) IsDomainWhitelisted(domain string) bool {
	if domain == "" {
		return false
	}

	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	// 转换为小写进行匹配
	domain = strings.ToLower(domain)
	
	// 直接匹配
	if c.domCache[domain] {
		return true
	}
	
	// 检查通配符匹配（如果白名单中有 *.example.com 格式）
	for whitelistDomain := range c.domCache {
		if strings.HasPrefix(whitelistDomain, "*.") {
			// 移除 *. 前缀
			wildcardDomain := whitelistDomain[2:]
			if strings.HasSuffix(domain, "."+wildcardDomain) || domain == wildcardDomain {
				return true
			}
		}
	}
	
	return false
}

// IsIOCWhitelisted 检查IOC是否在白名单中（自动判断类型）
func (c *IOCWhitelistCache) IsIOCWhitelisted(ioc string) bool {
	if ioc == "" {
		return false
	}

	// 尝试解析为IP地址
	if net.ParseIP(ioc) != nil {
		return c.IsIPWhitelisted(ioc)
	}
	
	// 否则当作域名处理
	return c.IsDomainWhitelisted(ioc)
}

// AddIP 添加IP到白名单缓存
func (c *IOCWhitelistCache) AddIP(ip string) {
	if ip == "" {
		return
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	c.ipCache[ip] = true
	utils.Debugf("添加IP到白名单缓存: %s", ip)
}

// AddDomain 添加域名到白名单缓存
func (c *IOCWhitelistCache) AddDomain(domain string) {
	if domain == "" {
		return
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	c.domCache[strings.ToLower(domain)] = true
	utils.Debugf("添加域名到白名单缓存: %s", domain)
}

// RemoveIP 从白名单缓存中移除IP
func (c *IOCWhitelistCache) RemoveIP(ip string) {
	if ip == "" {
		return
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	delete(c.ipCache, ip)
	utils.Debugf("从白名单缓存移除IP: %s", ip)
}

// RemoveDomain 从白名单缓存中移除域名
func (c *IOCWhitelistCache) RemoveDomain(domain string) {
	if domain == "" {
		return
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	delete(c.domCache, strings.ToLower(domain))
	utils.Debugf("从白名单缓存移除域名: %s", domain)
}

// RefreshCache 刷新缓存（重新从数据库加载）
func (c *IOCWhitelistCache) RefreshCache() error {
	utils.Infof("刷新IOC白名单缓存...")
	return c.LoadFromDatabase()
}

// GetCacheStats 获取缓存统计信息
func (c *IOCWhitelistCache) GetCacheStats() map[string]interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	return map[string]interface{}{
		"ip_count":     len(c.ipCache),
		"domain_count": len(c.domCache),
		"last_load":    c.lastLoad,
	}
}

// IsExpired 检查缓存是否过期（可选的自动刷新机制）
func (c *IOCWhitelistCache) IsExpired(maxAge time.Duration) bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	return time.Since(c.lastLoad) > maxAge
}
