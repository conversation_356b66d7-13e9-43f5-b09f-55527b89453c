<template>
  <div class="push-management">
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="推送通道" name="channels">
        <div class="tab-header">
          <h3>推送通道管理</h3>
          <el-button type="primary" @click="showCreateChannelDialog">
            <el-icon><Plus /></el-icon> 添加通道
          </el-button>
        </div>

        <el-table 
          :data="channels" 
          border 
          style="width: 100%" 
          v-loading="loadingChannels"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="通道名称" />
          <el-table-column label="通道类型" width="120">
            <template #default="scope">
              <el-tag>{{ getChannelTypeLabel(scope.row.type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status ? 'success' : 'info'">
                {{ scope.row.status ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280">
            <template #default="scope">
              <div>
                <el-button size="small" type="primary" @click="handleEditChannel(scope.row)">编辑</el-button>
                <el-button 
                  size="small" 
                  type="success" 
                  @click="handleTestChannel(scope.row)" 
                  :disabled="scope.row.status === false"
                >
                  测试
                </el-button>
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="handleDeleteChannel(scope.row)"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="empty-data" v-if="channels.length === 0 && !loadingChannels">
          <el-empty description="暂无推送通道" />
        </div>
      </el-tab-pane>

      <el-tab-pane label="推送策略" name="policies">
        <div class="tab-header">
          <h3>推送策略管理</h3>
          <el-button type="primary" @click="showCreatePolicyDialog">
            <el-icon><Plus /></el-icon> 添加策略
          </el-button>
        </div>

        <el-table 
          :data="policies" 
          border 
          style="width: 100%" 
          v-loading="loadingPolicies"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="策略名称" />
          <el-table-column prop="description" label="描述" />
          <el-table-column label="关联通道" min-width="200">
            <template #default="scope">
              <div v-if="scope.row.channelInfo && scope.row.channelInfo.length > 0">
                <el-tag 
                  v-for="channel in scope.row.channelInfo" 
                  :key="channel.id"
                  size="small"
                  class="channel-tag"
                >
                  {{ channel.name }}
                </el-tag>
              </div>
              <span v-else class="text-secondary">无关联通道</span>
            </template>
          </el-table-column>
          <el-table-column label="默认策略" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.isDefault ? 'success' : 'info'">
                {{ scope.row.isDefault ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <div>
                <el-button size="small" type="primary" @click="handleEditPolicy(scope.row)">编辑</el-button>
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="handleDeletePolicy(scope.row)"
                  :disabled="scope.row.isDefault"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="empty-data" v-if="policies.length === 0 && !loadingPolicies">
          <el-empty description="暂无推送策略" />
        </div>
      </el-tab-pane>

      <el-tab-pane label="推送白名单" name="whitelist">
        <div class="tab-header">
          <h3>推送白名单管理</h3>
          <el-button type="primary" @click="saveWhitelist" :loading="savingWhitelist">
            <el-icon><Check /></el-icon> 保存设置
          </el-button>
        </div>

        <el-card shadow="hover" class="whitelist-card">
          <el-form :model="whitelistForm" label-width="120px">
            <el-form-item label="自动推送">
              <el-switch v-model="whitelistForm.autoPush" />
              <div class="form-tip">启用后，新增的漏洞如果命中白名单关键词将自动推送</div>
            </el-form-item>
            
            <el-form-item label="使用推送策略">
              <el-select v-model="whitelistForm.policyId" style="width: 100%">
                <el-option
                  v-for="policy in policies"
                  :key="policy.id"
                  :label="policy.name"
                  :value="policy.id"
                />
              </el-select>
              <div class="form-tip">选择自动推送时使用的策略</div>
            </el-form-item>
            
            <el-form-item label="白名单描述">
              <el-input v-model="whitelistForm.description" placeholder="可选描述" />
            </el-form-item>
            
            <el-form-item label="白名单关键词">
              <el-input
                v-model="whitelistForm.keywords"
                type="textarea"
                :rows="10"
                placeholder="每行输入一个关键词，漏洞标题、标签或描述包含关键词时将匹配"
              />
              <div class="form-tip">关键词匹配不区分大小写，每行输入一个关键词</div>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="RSS订阅" name="rss">
        <div class="tab-header">
          <h3>RSS订阅配置</h3>
          <div>
            <el-button type="primary" @click="saveRssConfig" :loading="savingRssConfig">
              <el-icon><Check /></el-icon> 保存设置
            </el-button>
            <el-button type="success" @click="copyRssUrl">
              <el-icon><CopyDocument /></el-icon> 复制订阅链接
            </el-button>
          </div>
        </div>

        <el-card shadow="hover" class="rss-card">
          <el-form :model="rssForm" label-width="120px">
            <el-form-item label="启用RSS">
              <el-switch v-model="rssForm.enabled" />
              <div class="form-tip">启用或禁用RSS订阅功能</div>
            </el-form-item>
            
            <el-form-item label="需要认证">
              <el-switch v-model="rssForm.requireAuth" />
              <div class="form-tip">启用后，访问RSS需要提供API密钥参数</div>
            </el-form-item>
            
            <el-form-item label="RSS标题">
              <el-input v-model="rssForm.title" placeholder="RSS订阅标题" />
            </el-form-item>
            
            <el-form-item label="RSS描述">
              <el-input v-model="rssForm.description" placeholder="RSS订阅描述" />
            </el-form-item>
            
            <el-form-item label="条目数量">
              <el-input-number v-model="rssForm.itemCount" :min="10" :max="200" />
              <div class="form-tip">RSS订阅中显示的最大漏洞条目数量</div>
            </el-form-item>
            
            <el-form-item label="包含严重程度">
              <el-select v-model="selectedSeverities" multiple style="width: 100%">
                <el-option label="严重" value="严重" />
                <el-option label="高危" value="高危" />
                <el-option label="中危" value="中危" />
                <el-option label="低危" value="低危" />
                <el-option label="信息" value="信息" />
              </el-select>
              <div class="form-tip">选择要包含在RSS中的漏洞严重程度</div>
            </el-form-item>
            
            <el-form-item label="排除标签">
              <el-input 
                v-model="rssForm.excludeTags" 
                placeholder="输入要排除的标签，用逗号分隔"
              />
              <div class="form-tip">包含这些标签的漏洞将不会出现在RSS中</div>
            </el-form-item>
            
            <el-divider />
            
            <el-form-item label="订阅链接">
              <div class="rss-url-container">
                <el-input 
                  v-model="rssUrl" 
                  readonly
                  class="rss-url-input"
                >
                  <template #append>
                    <el-button @click="copyRssUrl">复制</el-button>
                  </template>
                </el-input>
              </div>
              <div class="form-tip">将此链接添加到您的RSS阅读器中</div>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="漏洞推送记录" name="records">
        <div class="tab-header">
          <h3>漏洞推送记录</h3>
          <div class="filter-container">
            <el-input
              v-model="filterKeyword"
              placeholder="按漏洞名称/编号或通道名称搜索"
              clearable
              @keyup.enter="fetchPushRecords"
              style="width: 250px"
            />
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              style="width: 300px"
            />
            <el-button @click="fetchPushRecords">查询</el-button>
            <el-button @click="resetFilters">重置</el-button>
          </div>
        </div>

        <el-table 
          :data="records" 
          border 
          style="width: 100%" 
          v-loading="loadingRecords"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column label="漏洞信息" min-width="200">
            <template #default="scope">
              <div v-if="scope.row.vulnerabilityName">
                <el-link 
                  type="primary" 
                  @click="showVulnerabilityDetail(scope.row.vulnerabilityID)"
                  :underline="false"
                >
                  {{ scope.row.vulnerabilityName }}
                </el-link>
                <div class="text-secondary">{{ scope.row.vulnerabilityVulnId }}</div>
              </div>
              <div v-else>ID: {{ scope.row.vulnerabilityID }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="channelName" label="推送通道" width="150">
            <template #default="scope">
              {{ scope.row.channelName || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="channelType" label="通道类型" width="120">
            <template #default="scope">
              <el-tag v-if="scope.row.channelType">{{ getChannelTypeLabel(scope.row.channelType) }}</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
                {{ scope.row.status === 'success' ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="推送时间" width="180">
            <template #default="scope">
              {{ scope.row.pushedAt ? formatTimestamp(scope.row.pushedAt) : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="错误信息" min-width="150">
            <template #default="scope">
              <span v-if="scope.row.errorMessage" class="text-danger">{{ scope.row.errorMessage }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button 
                size="small" 
                type="danger" 
                @click="handleDeleteRecord(scope.row)"
                :disabled="!scope.row.id"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="empty-data" v-if="records.length === 0 && !loadingRecords">
          <el-empty description="暂无推送记录" />
        </div>
      </el-tab-pane>

      <el-tab-pane label="IOC推送记录" name="ioc-records">
        <div class="tab-header">
          <h3>IOC推送记录</h3>
          <div class="filter-container">
            <el-input
              v-model="iocFilterKeyword"
              placeholder="按IOC值或通道名称搜索"
              clearable
              @keyup.enter="fetchIOCPushRecords"
              style="width: 250px"
            />
            <el-select
              v-model="iocFilterStatus"
              placeholder="推送状态"
              clearable
              style="width: 120px"
            >
              <el-option label="成功" value="success" />
              <el-option label="失败" value="failed" />
            </el-select>
            <el-select
              v-model="iocFilterChannelType"
              placeholder="通道类型"
              clearable
              style="width: 120px"
            >
              <el-option label="企业微信" value="wechat_bot" />
              <el-option label="钉钉" value="dingding" />
              <el-option label="飞书" value="lark" />
              <el-option label="Webhook" value="webhook" />
            </el-select>
            <el-date-picker
              v-model="iocDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              style="width: 300px"
            />
            <el-button @click="fetchIOCPushRecords">查询</el-button>
            <el-button @click="resetIOCFilters">重置</el-button>
          </div>
        </div>

        <el-table
          :data="iocRecords"
          border
          style="width: 100%"
          v-loading="loadingIOCRecords"
        >
          <el-table-column type="index" label="序号" width="80" :index="getIOCTableIndex" />
          <el-table-column label="IOC信息" min-width="200">
            <template #default="scope">
              <div v-if="scope.row.iocIntelligence">
                <div class="ioc-value">{{ scope.row.iocIntelligence.ioc }}</div>
                <div class="text-secondary">
                  <el-tag size="small" :type="getIOCTypeTagType(scope.row.iocIntelligence.iocType)">
                    {{ scope.row.iocIntelligence.iocType }}
                  </el-tag>
                  <el-tag size="small" :type="getRiskLevelTagType(scope.row.iocIntelligence.riskLevel)" style="margin-left: 5px">
                    {{ scope.row.iocIntelligence.riskLevel }}
                  </el-tag>
                </div>
              </div>
              <div v-else>IOC ID: {{ scope.row.iocIntelligenceId }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="channelName" label="推送通道" width="150">
            <template #default="scope">
              {{ scope.row.channelName || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="channelType" label="通道类型" width="120">
            <template #default="scope">
              <el-tag v-if="scope.row.channelType">{{ getChannelTypeLabel(scope.row.channelType) }}</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
                {{ scope.row.status === 'success' ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="推送时间" width="180">
            <template #default="scope">
              {{ scope.row.pushedAt ? formatTimestamp(scope.row.pushedAt) : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="错误信息" min-width="150">
            <template #default="scope">
              <span v-if="scope.row.errorMessage" class="text-danger">{{ scope.row.errorMessage }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button
                size="small"
                type="primary"
                @click="showIOCRecordDetail(scope.row)"
              >
                详情
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDeleteIOCRecord(scope.row)"
                :disabled="!scope.row.id"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container" v-if="iocRecords.length > 0">
          <el-pagination
            v-model:current-page="iocCurrentPage"
            v-model:page-size="iocPageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="iocTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="fetchIOCPushRecords"
            @current-change="fetchIOCPushRecords"
          />
        </div>

        <div class="empty-data" v-if="iocRecords.length === 0 && !loadingIOCRecords">
          <el-empty description="暂无IOC推送记录" />
        </div>
      </el-tab-pane>

      <el-tab-pane label="漏洞导出" name="export">
        <div class="tab-header">
          <h3>漏洞数据导出</h3>
        </div>
        
        <el-card shadow="hover" class="export-card">
          <el-form :model="exportForm" label-width="120px">
            <el-form-item label="导出方式">
              <el-radio-group v-model="exportForm.exportType">
                <el-radio label="manual">手动导出</el-radio>
                <el-radio label="auto">定期导出</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <!-- 手动导出选项 -->
            <template v-if="exportForm.exportType === 'manual'">
              <el-form-item label="时间范围">
                <el-date-picker
                  v-model="exportForm.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                  style="width: 350px"
                />
              </el-form-item>
              
              <el-form-item label="严重程度">
                <el-select v-model="exportForm.severities" multiple style="width: 100%">
                  <el-option label="严重" value="严重" />
                  <el-option label="高危" value="高危" />
                  <el-option label="中危" value="中危" />
                  <el-option label="低危" value="低危" />
                  <el-option label="信息" value="信息" />
                </el-select>
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="handleManualExport" :loading="exportingData">
                  <el-icon><Download /></el-icon> 导出数据
                </el-button>
              </el-form-item>
            </template>
            
            <!-- 定期导出选项 -->
            <template v-if="exportForm.exportType === 'auto'">
              <el-form-item label="导出频率">
                <el-select v-model="exportForm.frequency" style="width: 100%">
                  <el-option label="每周" value="weekly" />
                  <el-option label="每月" value="monthly" />
                </el-select>
              </el-form-item>
              
              <!-- 周几选择（当频率为每周时显示） -->
              <el-form-item label="每周几导出" v-if="exportForm.frequency === 'weekly'">
                <el-select v-model="exportForm.weekDay" style="width: 100%">
                  <el-option label="周日" :value="0" />
                  <el-option label="周一" :value="1" />
                  <el-option label="周二" :value="2" />
                  <el-option label="周三" :value="3" />
                  <el-option label="周四" :value="4" />
                  <el-option label="周五" :value="5" />
                  <el-option label="周六" :value="6" />
                </el-select>
                <div class="form-tip">系统将在指定的周几导出近一周入库的漏洞</div>
              </el-form-item>
              
              <!-- 月几号选择（当频率为每月时显示） -->
              <el-form-item label="每月几号导出" v-if="exportForm.frequency === 'monthly'">
                <el-select v-model="exportForm.monthDay" style="width: 100%">
                  <el-option v-for="day in 31" :key="day" :label="`${day}号`" :value="day" />
                </el-select>
                <div class="form-tip">系统将在指定的日期导出近一月入库的漏洞。如果当月没有对应日期（如2月没有30日），则在月末执行导出。</div>
              </el-form-item>
              
              <el-form-item label="严重程度">
                <el-select v-model="exportForm.severities" multiple style="width: 100%">
                  <el-option label="严重" value="严重" />
                  <el-option label="高危" value="高危" />
                  <el-option label="中危" value="中危" />
                  <el-option label="低危" value="低危" />
                  <el-option label="信息" value="信息" />
                </el-select>
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="saveExportConfig" :loading="savingExportConfig">
                  <el-icon><Check /></el-icon> 保存配置
                </el-button>
              </el-form-item>
            </template>
          </el-form>
        </el-card>
        
        <!-- 导出文件列表 -->
        <el-card shadow="hover" class="export-files-card" style="margin-top: 20px;">
          <div slot="header" class="clearfix">
            <span>导出文件列表</span>
            <el-button 
              style="float: right" 
              size="small" 
              type="primary" 
              @click="fetchExportFiles"
              :loading="loadingExportFiles"
            >
              <el-icon><Refresh /></el-icon> 刷新
            </el-button>
          </div>
          
          <el-table 
            :data="exportFiles"
            border
            style="width: 100%"
            v-loading="loadingExportFiles"
          >
            <el-table-column prop="filename" label="文件名" min-width="250" />
            <el-table-column label="创建时间" width="180">
              <template #default="scope">
                {{ formatTimestamp(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="文件大小" width="120">
              <template #default="scope">
                {{ formatFileSize(scope.row.fileSize) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button 
                  size="small" 
                  type="primary"
                  @click="downloadExportFile(scope.row)"
                >
                  <el-icon><Download /></el-icon> 下载
                </el-button>
                <el-button 
                  size="small" 
                  type="danger"
                  @click="handleDeleteExportFile(scope.row)"
                >
                  <el-icon><Delete /></el-icon> 删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="empty-data" v-if="exportFiles.length === 0 && !loadingExportFiles">
            <el-empty description="暂无导出文件" />
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑通道对话框 -->
    <el-dialog 
      v-model="channelDialogVisible" 
      :title="isEditMode ? '编辑推送通道' : '创建推送通道'" 
      width="500px"
      destroy-on-close
    >
      <el-form :model="channelForm" :rules="channelRules" ref="channelFormRef" label-width="100px" status-icon>
        <el-form-item label="通道名称" prop="name">
          <el-input v-model="channelForm.name" />
        </el-form-item>
        <el-form-item label="通道类型" prop="type">
          <el-select v-model="channelForm.type" style="width: 100%">
            <el-option label="企业微信机器人" value="wechat_bot" />
            <el-option label="钉钉机器人" value="dingding" />
            <el-option label="自定义Webhook" value="webhook" />
            <el-option label="飞书机器人" value="lark" />
          </el-select>
        </el-form-item>
        <el-form-item label="Webhook URL" prop="webhookURL" v-if="channelForm.type === 'wechat_bot'">
          <el-input v-model="channelForm.webhookURL" />
        </el-form-item>
        <el-form-item label="访问令牌" prop="accessToken" v-if="channelForm.type === 'dingding'">
          <el-input v-model="channelForm.accessToken" />
        </el-form-item>
        <el-form-item label="签名密钥" prop="secret" v-if="channelForm.type === 'dingding'">
          <el-input v-model="channelForm.secret" placeholder="可选" />
        </el-form-item>
        <el-form-item label="Webhook URL" prop="webhookURL" v-if="channelForm.type === 'webhook'">
          <el-input v-model="channelForm.webhookURL" placeholder="请输入完整的URL，如 http://example.com/webhook" />
        </el-form-item>
        <el-form-item label="访问令牌" prop="accessToken" v-if="channelForm.type === 'lark'">
          <el-input v-model="channelForm.accessToken" placeholder="请输入飞书机器人的访问令牌" />
        </el-form-item>
        <el-form-item label="签名密钥" prop="secret" v-if="channelForm.type === 'lark'">
          <el-input v-model="channelForm.secret" placeholder="可选" />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch
            v-model="channelForm.status"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="channelDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveChannel" :loading="savingChannel">确定</el-button>
          <el-button 
            v-if="isEditMode && channelForm.status" 
            type="success" 
            @click="testCurrentChannel" 
            :loading="testingChannel"
          >
            测试有效性
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 创建/编辑策略对话框 -->
    <el-dialog 
      v-model="policyDialogVisible" 
      :title="isEditPolicyMode ? '编辑推送策略' : '创建推送策略'" 
      width="500px"
      destroy-on-close
    >
      <el-form :model="policyForm" :rules="policyRules" ref="policyFormRef" label-width="100px" status-icon>
        <el-form-item v-if="isEditPolicyMode">
          <div class="debug-info">
            策略ID: {{ policyForm.id }}
          </div>
        </el-form-item>
        <el-form-item label="策略名称" prop="name">
          <el-input v-model="policyForm.name" />
        </el-form-item>
        <el-form-item label="策略描述" prop="description">
          <el-input v-model="policyForm.description" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="关联通道" prop="channelIDs">
          <el-select 
            v-model="selectedChannels" 
            multiple 
            style="width: 100%"
            placeholder="请选择关联的推送通道"
          >
            <el-option 
              v-for="channel in channels" 
              :key="channel.id" 
              :label="channel.name" 
              :value="channel.id"
              :disabled="!channel.status"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="默认策略">
          <el-switch
            v-model="policyForm.isDefault"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="policyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePolicy" :loading="savingPolicy">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 漏洞详情对话框 -->
    <el-dialog 
      v-model="vulnerabilityDetailDialogVisible" 
      :title="`漏洞详情: ${vulnerabilityDetail.name || ''}`"
      width="700px"
      destroy-on-close
    >
      <div v-loading="loadingVulnerabilityDetail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="漏洞名称">{{ vulnerabilityDetail.name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="漏洞编号">{{ vulnerabilityDetail.vulnId || '-' }}</el-descriptions-item>
          <el-descriptions-item label="严重程度">
            <el-tag :type="getSeverityType(vulnerabilityDetail.severity)" effect="dark">
              {{ vulnerabilityDetail.severity || '-' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="披露日期">{{ vulnerabilityDetail.disclosureDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="信息来源">{{ vulnerabilityDetail.source || '-' }}</el-descriptions-item>
          <el-descriptions-item label="漏洞描述">
            <div class="vuln-description">{{ vulnerabilityDetail.description || '-' }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="修复建议">
            <div class="vuln-description">{{ vulnerabilityDetail.remediation || '-' }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="标签">
            <el-tag 
              v-for="tag in vulnerabilityDetail.tags" 
              :key="tag" 
              size="small" 
              class="tag-item"
            >
              {{ tag }}
            </el-tag>
            <span v-if="!vulnerabilityDetail.tags || vulnerabilityDetail.tags.length === 0">-</span>
          </el-descriptions-item>
          <el-descriptions-item label="参考链接">
            <div v-if="vulnerabilityDetail.references && vulnerabilityDetail.references.length > 0">
              <div v-for="(ref, index) in vulnerabilityDetail.references" :key="index" class="reference-item">
                <el-link :href="ref" target="_blank" type="primary">{{ ref }}</el-link>
              </div>
            </div>
            <span v-else>-</span>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="dialog-footer">
          <el-button @click="vulnerabilityDetailDialogVisible = false">关闭</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- IOC推送记录详情对话框 -->
    <el-dialog
      v-model="iocRecordDetailVisible"
      :title="`IOC推送记录详情`"
      width="700px"
      destroy-on-close
    >
      <div v-if="currentIOCRecord">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="记录ID">{{ currentIOCRecord.id || '-' }}</el-descriptions-item>
          <el-descriptions-item label="IOC值">
            {{ currentIOCRecord.iocIntelligence?.ioc || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="IOC类型">
            <el-tag v-if="currentIOCRecord.iocIntelligence?.iocType"
                    :type="getIOCTypeTagType(currentIOCRecord.iocIntelligence.iocType)">
              {{ currentIOCRecord.iocIntelligence.iocType }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="风险等级">
            <el-tag v-if="currentIOCRecord.iocIntelligence?.riskLevel"
                    :type="getRiskLevelTagType(currentIOCRecord.iocIntelligence.riskLevel)">
              {{ currentIOCRecord.iocIntelligence.riskLevel }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="地理位置" v-if="currentIOCRecord.iocIntelligence?.location">
            {{ currentIOCRecord.iocIntelligence.location }}
          </el-descriptions-item>
          <el-descriptions-item label="威胁描述">
            <div class="ioc-description">{{ currentIOCRecord.iocIntelligence?.description || '-' }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="被攻击单位" v-if="currentIOCRecord.iocIntelligence?.targetOrg">
            {{ currentIOCRecord.iocIntelligence.targetOrg }}
          </el-descriptions-item>
          <el-descriptions-item label="情报来源">{{ currentIOCRecord.iocIntelligence?.source || '-' }}</el-descriptions-item>
          <el-descriptions-item label="发现日期">{{ currentIOCRecord.iocIntelligence?.discoveryDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="推送原因" v-if="currentIOCRecord.iocIntelligence?.pushReason">
            <div class="push-reason">{{ currentIOCRecord.iocIntelligence.pushReason }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="推送通道">{{ currentIOCRecord.channelName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="通道类型">
            <el-tag v-if="currentIOCRecord.channelType">{{ getChannelTypeLabel(currentIOCRecord.channelType) }}</el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="推送状态">
            <el-tag :type="currentIOCRecord.status === 'success' ? 'success' : 'danger'">
              {{ currentIOCRecord.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="推送时间">
            {{ currentIOCRecord.pushedAt ? formatTimestamp(currentIOCRecord.pushedAt) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="错误信息" v-if="currentIOCRecord.errorMessage">
            <div class="error-message">{{ currentIOCRecord.errorMessage }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <div class="dialog-footer">
          <el-button @click="iocRecordDetailVisible = false">关闭</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, View, ArrowDown, Check, CopyDocument, Connection, Download, Refresh, Delete } from '@element-plus/icons-vue'
import api from './api'

const activeTab = ref('channels')
const channels = ref([])
const records = ref([])
const policies = ref([])
const loadingChannels = ref(false)
const loadingRecords = ref(false)
const loadingPolicies = ref(false)
const loadingExportFiles = ref(false)
const channelDialogVisible = ref(false)
const policyDialogVisible = ref(false)
const isEditMode = ref(false)
const isEditPolicyMode = ref(false)
const savingChannel = ref(false)
const savingPolicy = ref(false)
const savingWhitelist = ref(false)
const savingRssConfig = ref(false)
const savingExportConfig = ref(false)
const exportingData = ref(false)
const testingChannel = ref(false)
const filterKeyword = ref('')
const dateRange = ref(null)
const vulnerabilityOptions = ref([])
const searchingVulnerabilities = ref(false)
const exportFiles = ref([])

// IOC推送记录相关数据
const iocRecords = ref([])
const loadingIOCRecords = ref(false)
const iocFilterKeyword = ref('')
const iocFilterStatus = ref('')
const iocFilterChannelType = ref('')
const iocDateRange = ref(null)
const iocCurrentPage = ref(1)
const iocPageSize = ref(10)
const iocTotal = ref(0)
const iocRecordDetailVisible = ref(false)
const currentIOCRecord = ref(null)

// 通道表单
const channelFormRef = ref(null)
const channelForm = ref({
  id: null,
  name: '',
  type: 'wechat_bot',
  description: '',
  webhookURL: '',
  accessToken: '',
  secret: '',
  status: true
})

// 策略表单
const policyFormRef = ref(null)
const selectedChannels = ref([])
const policyForm = ref({
  id: null,
  name: '',
  description: '',
  channelIDs: '',
  isDefault: false
})

// 白名单表单
const whitelistForm = ref({
  id: 0,
  keywords: '',
  autoPush: false,
  policyId: 0,
  description: ''
})

// RSS配置表单
const rssForm = ref({
  id: 0,
  enabled: true,
  requireAuth: false,
  title: '威胁情报管理平台 - 漏洞订阅',
  description: '最新安全漏洞信息订阅',
  itemCount: 50,
  includeSeverity: '高,中,低',
  excludeTags: ''
})

// 选中的严重程度
const selectedSeverities = ref(['严重', '高危', '中危', '低危', '信息', 'Critical', 'High', 'Medium', 'Low', 'Info'])

// 监听选中的严重程度变化
watch(selectedSeverities, (newVal) => {
  rssForm.value.includeSeverity = newVal.join(',')
})

// 计算RSS订阅URL
const rssUrl = computed(() => {
  if (!rssForm.value.enabled) {
    return '(RSS订阅功能已禁用)'
  }
  
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}')
  return api.getRssUrl(rssForm.value.requireAuth ? currentUser.apiKey : undefined)
})

// 表单验证规则
const channelRules = {
  name: [
    { required: true, message: '请输入通道名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择通道类型', trigger: 'change' }
  ],
  webhookURL: [
    { required: true, message: '请输入URL', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  accessToken: [
    { required: true, message: '请输入访问令牌', trigger: 'blur' }
  ]
}

const policyRules = {
  name: [
    { required: true, message: '请输入策略名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 监听选中的通道变化，更新channelIDs字段
watch(selectedChannels, (newVal) => {
  policyForm.value.channelIDs = newVal.join(',')
})

// 获取通道类型标签
const getChannelTypeLabel = (type) => {
  if (!type) return '未知类型'
  
  const types = {
    'wechat_bot': '企业微信机器人',
    'dingding': '钉钉机器人',
    'webhook': '自定义Webhook',
    'lark': '飞书机器人'
  }
  return types[type] || type
}

// 格式化时间戳
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp * 1000)
  return date.toLocaleString()
}

// 获取推送通道列表
const fetchPushChannels = async () => {
  loadingChannels.value = true
  try {
    const res = await api.getPushChannels({ page: 1, pageSize: 100 })
    console.log('原始通道数据:', res.data.data)
    
    // 确保每个通道对象都有正确的属性
    const channelList = res.data.data?.list || res.data.data || []
    channels.value = channelList.map(channel => {
      const processedChannel = {
        id: Number(channel.ID || channel.id || 0),
        name: channel.Name || channel.name || '',
        type: channel.Type || channel.type || 'wechat_bot',
        webhookURL: channel.WebhookURL || channel.webhookURL || '',
        accessToken: channel.AccessToken || channel.accessToken || '',
        secret: channel.Secret || channel.secret || '',
        status: typeof channel.Status === 'boolean' ? channel.Status : 
               (typeof channel.status === 'boolean' ? channel.status : true)
      }
      console.log('处理后的通道:', processedChannel)
      return processedChannel
    })
  } catch (error) {
    console.error('获取推送通道失败', error)
  } finally {
    loadingChannels.value = false
  }
}

// 获取推送策略列表
const fetchPushPolicies = async () => {
  loadingPolicies.value = true
  try {
    const res = await api.getPushPolicies({ page: 1, pageSize: 100 })
    console.log('原始策略数据:', res.data.data)
    
    // 确保每个策略对象都有正确的属性
    const policyList = res.data.data?.list || res.data.data || []
    policies.value = policyList.map(policy => {
      const processedPolicy = {
        id: Number(policy.ID || policy.id || 0),
        name: policy.Name || policy.name || '',
        description: policy.Description || policy.description || '',
        channelIDs: policy.ChannelIDs || policy.channelIDs || '',
        isDefault: Boolean(policy.IsDefault || policy.isDefault),
        channelInfo: policy.ChannelInfo || policy.channelInfo || []
      }
      console.log('处理后的策略:', processedPolicy)
      return processedPolicy
    })
  } catch (error) {
    console.error('获取推送策略失败', error)
  } finally {
    loadingPolicies.value = false
  }
}

// 获取推送记录
const fetchPushRecords = async () => {
  loadingRecords.value = true
  try {
    console.log('获取推送记录，关键词:', filterKeyword.value)
    const res = await api.getPushRecords({
      keyword: filterKeyword.value,
      start_time: dateRange.value ? dateRange.value[0] : undefined,
      end_time: dateRange.value ? dateRange.value[1] : undefined,
      page: 1,
      pageSize: 100
    })
    console.log('推送记录响应:', res.data)
    
    // 确保记录数据有效
    if (res.data && res.data.data) {
      // 处理分页响应格式
      let recordList = []
      if (res.data.data.list) {
        // 新的分页格式
        recordList = res.data.data.list
      } else if (Array.isArray(res.data.data)) {
        // 直接数组格式
        recordList = res.data.data
      } else {
        console.warn('未知的数据格式:', res.data.data)
        recordList = []
      }

      // 确保recordList是数组
      if (!Array.isArray(recordList)) {
        console.error('recordList不是数组:', recordList)
        recordList = []
      }

      records.value = recordList.map(record => {
        // 确保ID是数字类型并展平嵌套对象
        const processedRecord = {
          ...record,
          id: Number(record.id || 0),
          // 展平漏洞信息
          vulnerabilityID: record.vulnerability?.id || record.vulnerabilityId,
          vulnerabilityName: record.vulnerability?.name || record.vulnerabilityName,
          vulnerabilityVulnId: record.vulnerability?.vulnId || record.vulnerabilityVulnId,
          vulnerabilitySeverity: record.vulnerability?.severity || record.vulnerabilitySeverity,
          vulnerabilitySource: record.vulnerability?.source || record.vulnerabilitySource,
          // 展平通道信息
          channelName: record.channel?.name || record.channelName,
          channelType: record.channel?.type || record.channelType
        }
        console.log('处理后的记录ID:', processedRecord.id, '类型:', typeof processedRecord.id)
        console.log('漏洞信息:', {
          id: processedRecord.vulnerabilityID,
          name: processedRecord.vulnerabilityName,
          vulnId: processedRecord.vulnerabilityVulnId
        })
        return processedRecord
      }).filter(record => record.id > 0); // 过滤掉无效ID的记录

      console.log('有效记录数量:', records.value.length)
    } else {
      records.value = []
    }
    
    console.log('最终记录数据:', records.value)
  } catch (error) {
    console.error('获取推送记录失败', error)
    console.error('错误详情:', error.response?.data)
    ElMessage.error('获取推送记录失败：' + (error.response?.data?.msg || '未知错误'))
  } finally {
    loadingRecords.value = false
  }
}

// 显示创建通道对话框
const showCreateChannelDialog = () => {
  isEditMode.value = false
  channelForm.value = {
    id: null,
    name: '',
    type: 'wechat_bot',
    description: '',
    webhookURL: '',
    accessToken: '',
    secret: '',
    status: true
  }
  console.log('创建通道初始表单:', channelForm.value)
  channelDialogVisible.value = true
}

// 显示创建策略对话框
const showCreatePolicyDialog = () => {
  isEditPolicyMode.value = false
  policyForm.value = {
    id: null,
    name: '',
    description: '',
    channelIDs: '',
    isDefault: false
  }
  selectedChannels.value = []
  policyDialogVisible.value = true
}

// 编辑通道
const handleEditChannel = async (channel) => {
  isEditMode.value = true
  
  try {
    // 获取最新的通道详情
    const channelId = Number(channel.id || 0)
    console.log('获取通道详情，ID:', channelId)
    
    const response = await api.getPushChannel(channelId)
    console.log('获取通道详情响应:', response.data)
    
    const channelData = response.data.data
    
    // 提取配置信息
    const config = channelData.config || {}
    
    channelForm.value = { 
      id: channelId,
      name: channelData.name || '',
      type: channelData.type || 'wechat_bot',
      description: channelData.description || '',
      webhookURL: channelData.type === 'webhook' ? (config.url || '') : (config.webhook_url || ''),
      accessToken: config.access_token || '',
      secret: (channelData.type === 'dingding' || channelData.type === 'lark') ? (config.sign_secret || '') : (config.secret || ''),
      status: typeof channelData.status === 'boolean' ? channelData.status : true
    }
    
    console.log('编辑通道表单数据:', channelForm.value)
    channelDialogVisible.value = true
  } catch (error) {
    console.error('获取通道详情失败', error)
    console.error('错误详情:', error.response?.data)
    ElMessage.error('获取通道详情失败：' + (error.response?.data?.msg || '未知错误'))
  }
}

// 编辑策略
const handleEditPolicy = (policy) => {
  isEditPolicyMode.value = true
  
  // 确保ID是数字类型
  const id = Number(policy.id || policy.ID || 0)
  console.log('编辑策略，原始ID:', policy.id, '类型:', typeof policy.id)
  console.log('转换后ID:', id, '类型:', typeof id)
  
  policyForm.value = {
    id: id,
    name: policy.name || policy.Name || '',
    description: policy.description || policy.Description || '',
    channelIDs: policy.channelIDs || policy.ChannelIDs || policy.channelIds || '',
    isDefault: Boolean(policy.isDefault || policy.IsDefault)
  }
  
  console.log('编辑策略表单数据:', policyForm.value)
  
  selectedChannels.value = policyForm.value.channelIDs ? 
    policyForm.value.channelIDs.split(',').map(Number).filter(Boolean) : []
  
  policyDialogVisible.value = true
}

// 测试当前编辑中的通道
const testCurrentChannel = async () => {
  if (!channelFormRef.value) return
  
  channelFormRef.value.validate(async (valid) => {
    if (valid) {
      testingChannel.value = true
      try {
        // 构建通道配置对象
        const config = {}
        
        // 根据通道类型设置不同的配置字段
        if (channelForm.value.type === 'wechat_bot') {
          config.webhook_url = channelForm.value.webhookURL
        } else if (channelForm.value.type === 'dingding') {
          config.access_token = channelForm.value.accessToken
          if (channelForm.value.secret) {
            config.sign_secret = channelForm.value.secret
          }
        } else if (channelForm.value.type === 'webhook') {
          config.url = channelForm.value.webhookURL
        } else if (channelForm.value.type === 'lark') {
          config.access_token = channelForm.value.accessToken
          if (channelForm.value.secret) {
            config.sign_secret = channelForm.value.secret
          }
        }
        
        const formData = {
          name: channelForm.value.name,
          type: channelForm.value.type,
          description: channelForm.value.description || '',
          status: channelForm.value.status,
          config: config
        }
        
        console.log('测试通道数据:', JSON.stringify(formData))
        
        // 先保存当前编辑的数据
        if (isEditMode.value && channelForm.value.id) {
          const response = await api.updatePushChannel(Number(channelForm.value.id), formData)
          console.log('更新通道响应:', response.data)
          
          // 然后测试通道
          const testResponse = await api.testPushChannel(Number(channelForm.value.id))
          console.log('测试通道响应:', testResponse.data)
          ElMessage.success('测试消息已成功发送')
        } else {
          ElMessage.warning('请先保存通道再测试')
        }
      } catch (error) {
        console.error('测试推送失败', error)
        console.error('错误详情:', error.response?.data)
        ElMessage.error('测试失败：' + (error.response?.data?.msg || error.response?.data?.error || '未知错误'))
      } finally {
        testingChannel.value = false
      }
    }
  })
}

// 测试通道
const handleTestChannel = async (channel) => {
  if (!channel.status) {
    ElMessage.warning('通道已禁用，无法测试')
    return
  }
  
  try {
    console.log('测试通道:', channel)
    await api.testPushChannel(Number(channel.id))
    ElMessage.success('测试消息已成功发送')
  } catch (error) {
    ElMessage.error('测试失败：' + (error.response?.data?.error || '未知错误'))
    console.error('测试推送失败', error)
  }
}

// 保存通道
const saveChannel = async () => {
  if (!channelFormRef.value) return
  
  channelFormRef.value.validate(async (valid) => {
    if (valid) {
      savingChannel.value = true
      try {
        // 构建通道配置对象
        const config = {}
        
        // 根据通道类型设置不同的配置字段
        if (channelForm.value.type === 'wechat_bot') {
          config.webhook_url = channelForm.value.webhookURL
        } else if (channelForm.value.type === 'dingding') {
          config.access_token = channelForm.value.accessToken
          if (channelForm.value.secret) {
            config.sign_secret = channelForm.value.secret
          }
        } else if (channelForm.value.type === 'webhook') {
          config.url = channelForm.value.webhookURL
        } else if (channelForm.value.type === 'lark') {
          config.access_token = channelForm.value.accessToken
          if (channelForm.value.secret) {
            config.sign_secret = channelForm.value.secret
          }
        }
        
        const formData = {
          name: channelForm.value.name,
          type: channelForm.value.type,
          description: channelForm.value.description || '',
          status: channelForm.value.status,
          config: config
        }
        
        console.log('保存通道数据:', JSON.stringify(formData))
        
        if (isEditMode.value) {
          console.log('更新通道, ID:', Number(channelForm.value.id))
          const response = await api.updatePushChannel(Number(channelForm.value.id), formData)
          console.log('更新通道响应:', response.data)
          ElMessage.success('更新推送通道成功')
        } else {
          console.log('创建通道')
          const response = await api.createPushChannel(formData)
          console.log('创建通道响应:', response.data)
          ElMessage.success('创建推送通道成功')
        }
        channelDialogVisible.value = false
        fetchPushChannels()
      } catch (error) {
        console.error('保存推送通道失败', error)
        console.error('错误详情:', error.response?.data)
        ElMessage.error('保存失败：' + (error.response?.data?.msg || '未知错误'))
      } finally {
        savingChannel.value = false
      }
    }
  })
}

// 保存策略
const savePolicy = async () => {
  if (!policyFormRef.value) return
  
  policyFormRef.value.validate(async (valid) => {
    if (valid) {
      savingPolicy.value = true
      try {
        // 创建一个新的对象，避免直接修改表单数据
        const formData = {
          name: policyForm.value.name,
          description: policyForm.value.description || '',
          channelIDs: selectedChannels.value.join(','),
          isDefault: policyForm.value.isDefault
        }
        
        console.log('保存策略，模式:', isEditPolicyMode.value ? '编辑' : '创建')
        
        if (isEditPolicyMode.value) {
          // 确保ID是有效的数字
          const policyId = Number(policyForm.value.id)
          if (!policyId || isNaN(policyId)) {
            throw new Error('无效的策略ID')
          }
          console.log('更新策略，ID:', policyId, '数据:', JSON.stringify(formData))
          try {
            const res = await api.updatePushPolicy(policyId, formData)
            console.log('更新策略响应:', res)
            ElMessage.success('更新推送策略成功')
          } catch (err) {
            console.error('更新策略API错误:', err)
            console.error('错误详情:', err.response?.data)
            throw err
          }
        } else {
          // 创建模式不需要传ID
          console.log('创建新策略，数据:', JSON.stringify(formData))
          try {
            const res = await api.createPushPolicy(formData)
            console.log('创建策略响应:', res)
            ElMessage.success('创建推送策略成功')
          } catch (err) {
            console.error('创建策略API错误:', err)
            console.error('错误详情:', err.response?.data)
            throw err
          }
        }
        
        policyDialogVisible.value = false
        fetchPushPolicies()
      } catch (error) {
        console.error('保存推送策略失败', error)
        ElMessage.error('保存失败：' + (error.response?.data?.msg || '未知错误'))
      } finally {
        savingPolicy.value = false
      }
    }
  })
}

// 删除通道
const handleDeleteChannel = (channel) => {
  // 确保ID是数字类型
  const channelId = Number(channel.id || 0)
  console.log('删除通道，原始ID:', channel.id, '类型:', typeof channel.id)
  console.log('转换后ID:', channelId, '类型:', typeof channelId)
  
  ElMessageBox.confirm(`确定要删除推送通道 ${channel.name} 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      console.log('正在删除通道，ID:', channelId)
      const res = await api.deletePushChannel(channelId)
      console.log('删除通道响应:', res)
      ElMessage.success('删除推送通道成功')
      fetchPushChannels()
    } catch (error) {
      console.error('删除推送通道失败', error)
      console.error('错误详情:', error.response?.data)
      ElMessage.error('删除失败：' + (error.response?.data?.msg || '未知错误'))
    }
  }).catch(() => {})
}

// 删除策略
const handleDeletePolicy = (policy) => {
  // 不允许删除默认策略
  if (policy.isDefault) {
    ElMessage.warning('不能删除默认策略')
    return
  }
  
  ElMessageBox.confirm(`确定要删除推送策略 ${policy.name} 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await api.deletePushPolicy(Number(policy.id))
      ElMessage.success('删除推送策略成功')
      fetchPushPolicies()
    } catch (error) {
      console.error('删除推送策略失败', error)
      ElMessage.error('删除失败：' + (error.response?.data?.msg || '未知错误'))
    }
  }).catch(() => {})
}

// 删除推送记录
const handleDeleteRecord = (record) => {
  // 确保ID是数字类型且不为0
  const recordId = Number(record.id || 0)
  console.log('删除推送记录，原始记录:', record)
  console.log('删除推送记录，原始ID:', record.id, '类型:', typeof record.id)
  console.log('转换后ID:', recordId, '类型:', typeof recordId)
  
  if (!recordId) {
    ElMessage.error('无效的记录ID')
    return
  }
  
  ElMessageBox.confirm(`确定要删除推送记录吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      console.log('正在删除推送记录，ID:', recordId)
      const res = await api.deletePushRecord(recordId)
      console.log('删除推送记录响应:', res)
      ElMessage.success('删除推送记录成功')
      fetchPushRecords()
    } catch (error) {
      console.error('删除推送记录失败', error)
      console.error('错误详情:', error.response?.data)
      ElMessage.error('删除失败：' + (error.response?.data?.msg || '未知错误'))
    }
  }).catch(() => {})
}

// 重置过滤器
const resetFilters = () => {
  filterKeyword.value = ''
  dateRange.value = null
  fetchPushRecords()
}

// 添加漏洞详情对话框
const vulnerabilityDetailDialogVisible = ref(false)
const vulnerabilityDetail = ref({})
const loadingVulnerabilityDetail = ref(false)

// 显示漏洞详情对话框
const showVulnerabilityDetail = async (vulnerabilityID) => {
  if (!vulnerabilityID) {
    ElMessage.warning('无效的漏洞ID')
    return
  }
  
  console.log('显示漏洞详情，ID:', vulnerabilityID)
  try {
    loadingVulnerabilityDetail.value = true
    const res = await api.getVulnerabilityDetail(vulnerabilityID)
    console.log('漏洞详情响应:', res.data)
    
    if (res.data && res.data.data) {
      vulnerabilityDetail.value = res.data.data
      vulnerabilityDetailDialogVisible.value = true
    } else {
      ElMessage.warning('获取漏洞详情失败')
    }
  } catch (error) {
    console.error('获取漏洞详情失败', error)
    ElMessage.error('获取漏洞详情失败：' + (error.response?.data?.msg || '未知错误'))
  } finally {
    loadingVulnerabilityDetail.value = false
  }
}

// 获取漏洞严重程度类型
const getSeverityType = (severity) => {
  if (!severity) return 'info'
  
  const severityTypes = {
    '严重': 'error',
    '高危': 'danger',
    '中危': 'warning',
    '低危': 'success',
    '信息': 'info'
  }
  return severityTypes[severity] || 'info'
}

// 获取RSS配置
const fetchRssConfig = async () => {
  try {
    const res = await api.getRssConfig()
    console.log('RSS配置数据:', res.data)
    
    if (res.data && res.data.data) {
      const data = res.data.data
      rssForm.value = {
        id: data.id || 0,
        enabled: data.enabled !== undefined ? data.enabled : true,
        requireAuth: data.requireAuth !== undefined ? data.requireAuth : false,
        title: data.title || '威胁情报管理平台 - 漏洞订阅',
        description: data.description || '最新安全漏洞信息订阅',
        itemCount: data.itemCount || 50,
        includeSeverity: data.includeSeverity || '高,中,低',
        excludeTags: data.excludeTags || ''
      }
      
      // 更新选中的严重程度
      if (data.includeSeverity) {
        selectedSeverities.value = data.includeSeverity.split(',')
      }
    }
  } catch (error) {
    console.error('获取RSS配置失败', error)
    ElMessage.error('获取RSS配置失败：' + (error.response?.data?.msg || '未知错误'))
  }
}

// 保存RSS配置
const saveRssConfig = async () => {
  savingRssConfig.value = true
  try {
    await api.updateRssConfig({
      enabled: rssForm.value.enabled,
      requireAuth: rssForm.value.requireAuth,
      title: rssForm.value.title,
      description: rssForm.value.description,
      itemCount: rssForm.value.itemCount,
      includeSeverity: rssForm.value.includeSeverity,
      excludeTags: rssForm.value.excludeTags
    })
    ElMessage.success('保存RSS配置成功')
  } catch (error) {
    console.error('保存RSS配置失败', error)
    ElMessage.error('保存失败：' + (error.response?.data?.msg || '未知错误'))
  } finally {
    savingRssConfig.value = false
  }
}

// 复制RSS订阅链接
const copyRssUrl = () => {
  if (!rssForm.value.enabled) {
    ElMessage.warning('RSS订阅功能已禁用，无法复制链接')
    return
  }
  
  // 创建一个临时的textarea元素
  const textarea = document.createElement('textarea')
  textarea.value = rssUrl.value
  textarea.style.position = 'fixed'  // 避免滚动到底部
  textarea.style.opacity = '0'
  document.body.appendChild(textarea)
  
  try {
    // 选择文本并复制
    textarea.select()
    const successful = document.execCommand('copy')
    if (successful) {
      ElMessage.success('RSS订阅链接已复制到剪贴板')
    } else {
      ElMessage.error('复制失败，请手动复制')
    }
  } catch (err) {
    console.error('复制失败:', err)
    ElMessage.error('复制失败，请手动复制')
  } finally {
    // 移除临时元素
    document.body.removeChild(textarea)
  }
}

// 获取推送白名单
const fetchPushWhitelist = async () => {
  try {
    const res = await api.getPushWhitelist()
    console.log('推送白名单数据:', res.data)
    
    if (res.data && res.data.data) {
      const data = res.data.data
      // 将后端返回的逗号分隔关键词转换为换行符分隔，以便在前端显示为多行
      const keywords = data.keywords ? data.keywords.split(',').join('\n') : '';
      
      whitelistForm.value = {
        id: data.id || 0,
        keywords: keywords,
        autoPush: data.autoPush || false,
        policyId: data.policyId || 0,
        description: data.description || ''
      }
    }
  } catch (error) {
    console.error('获取推送白名单失败', error)
    ElMessage.error('获取推送白名单失败：' + (error.response?.data?.msg || '未知错误'))
  }
}

// 保存推送白名单
const saveWhitelist = async () => {
  savingWhitelist.value = true
  try {
    // 将换行符分隔的关键词转换为逗号分隔，以便在后端存储
    const keywords = whitelistForm.value.keywords ? whitelistForm.value.keywords.split('\n').map(k => k.trim()).filter(k => k).join(',') : '';
    
    await api.updatePushWhitelist({
      keywords: keywords,
      autoPush: whitelistForm.value.autoPush,
      policyId: whitelistForm.value.policyId,
      description: whitelistForm.value.description
    })
    ElMessage.success('保存推送白名单成功')
  } catch (error) {
    console.error('保存推送白名单失败', error)
    ElMessage.error('保存失败：' + (error.response?.data?.msg || '未知错误'))
  } finally {
    savingWhitelist.value = false
  }
}

// 导出数据
const handleManualExport = async () => {
  if (!exportForm.value.dateRange || !exportForm.value.dateRange[0] || !exportForm.value.dateRange[1]) {
    ElMessage.warning('请选择时间范围')
    return
  }

  exportingData.value = true
  try {
    // 创建导出参数
    const params = {
      startDate: exportForm.value.dateRange[0],
      endDate: exportForm.value.dateRange[1],
      severities: exportForm.value.severities
    }
    
    console.log('导出参数:', params)
    
    // 调用API导出数据
    await api.exportVulnerabilitiesByDate(params)
    
    ElMessage.success('导出成功')
    
    // 导出成功后自动刷新文件列表
    fetchExportFiles()
  } catch (error) {
    console.error('导出漏洞失败', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    exportingData.value = false
  }
}

// 保存导出配置
const saveExportConfig = async () => {
  savingExportConfig.value = true
  try {
    // 创建配置参数
    const config = {
      frequency: exportForm.value.frequency,
      severities: exportForm.value.severities,
      weekDay: exportForm.value.weekDay,
      monthDay: exportForm.value.monthDay
    }
    
    console.log('保存导出配置:', config)
    
    // 调用API保存配置
    await api.saveExportConfig(config)
    ElMessage.success('保存导出配置成功')
    
    // 重新获取导出文件列表
    fetchExportFiles()
  } catch (error) {
    console.error('保存导出配置失败', error)
    ElMessage.error('保存失败：' + (error.response?.data?.msg || '未知错误'))
  } finally {
    savingExportConfig.value = false
  }
}

// 获取导出文件列表
const fetchExportFiles = async () => {
  loadingExportFiles.value = true
  try {
    const response = await api.getExportFiles()
    
    if (response.data && response.data.data) {
      exportFiles.value = response.data.data
    } else {
      exportFiles.value = []
    }
  } catch (error) {
    console.error('获取导出文件列表失败', error)
    ElMessage.error('获取导出文件列表失败')
    exportFiles.value = []
  } finally {
    loadingExportFiles.value = false
  }
}

// 获取导出配置
const fetchExportConfig = async () => {
  try {
    const response = await api.getExportConfig()
    
    if (response.data && response.data.data) {
      const config = response.data.data
      exportForm.value.exportType = 'auto' // 默认显示自动导出
      exportForm.value.frequency = config.frequency || 'weekly'
      exportForm.value.weekDay = config.weekDay !== undefined ? config.weekDay : 1
      exportForm.value.monthDay = config.monthDay !== undefined ? config.monthDay : 1
      exportForm.value.severities = config.severities || ['严重', '高危', '中危', '低危', '信息']
    }
  } catch (error) {
    console.error('获取导出配置失败', error)
  }
}

// 下载导出文件
const downloadExportFile = async (file) => {
  try {
    const response = await api.downloadExportFile(file.filename)
    
    // 创建下载链接
    const blob = new Blob([response.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    
    // 创建下载链接并触发下载
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', file.filename)
    document.body.appendChild(link)
    link.click()
    
    // 清理
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载导出文件失败', error)
    ElMessage.error('下载失败')
  }
}

// 删除导出文件
const handleDeleteExportFile = (file) => {
  ElMessageBox.confirm(`确定要删除文件 ${file.filename} 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await api.deleteExportFile(file.filename)
      ElMessage.success('文件删除成功')
      // 刷新文件列表
      fetchExportFiles()
    } catch (error) {
      console.error('删除导出文件失败', error)
      ElMessage.error('删除失败: ' + (error.response?.data?.msg || '未知错误'))
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 导出表单
const exportForm = ref({
  exportType: 'manual', // 默认为手动导出
  dateRange: [
    new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().split('T')[0], // 一个月前
    new Date().toISOString().split('T')[0] // 今天
  ],
  frequency: 'weekly',
  weekDay: 1, // 默认周一
  monthDay: 1, // 默认每月1号
  severities: ['严重', '高危', '中危', '低危', '信息']
})

// IOC推送记录相关方法
const fetchIOCPushRecords = async () => {
  loadingIOCRecords.value = true
  try {
    const params = {
      page: iocCurrentPage.value,
      page_size: iocPageSize.value,
      keyword: iocFilterKeyword.value,
      status: iocFilterStatus.value,
      channel_type: iocFilterChannelType.value,
      start_time: iocDateRange.value ? iocDateRange.value[0] : undefined,
      end_time: iocDateRange.value ? iocDateRange.value[1] : undefined
    }

    const res = await api.getIOCIntelligencePushRecords(params)

    if (res.data && res.data.data) {
      iocRecords.value = res.data.data.list || []
      iocTotal.value = res.data.data.pagination?.total || 0
    } else {
      iocRecords.value = []
      iocTotal.value = 0
    }
  } catch (error) {
    console.error('获取IOC推送记录失败', error)
    ElMessage.error('获取IOC推送记录失败：' + (error.response?.data?.msg || '未知错误'))
  } finally {
    loadingIOCRecords.value = false
  }
}

const resetIOCFilters = () => {
  iocFilterKeyword.value = ''
  iocFilterStatus.value = ''
  iocFilterChannelType.value = ''
  iocDateRange.value = null
  iocCurrentPage.value = 1
  fetchIOCPushRecords()
}

const showIOCRecordDetail = (record) => {
  currentIOCRecord.value = record
  iocRecordDetailVisible.value = true
}

// 计算IOC表格序号
const getIOCTableIndex = (index) => {
  return (iocCurrentPage.value - 1) * iocPageSize.value + index + 1
}

const handleDeleteIOCRecord = async (record) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条IOC推送记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await api.deleteIOCIntelligencePushRecord(record.id)
    ElMessage.success('删除成功')
    fetchIOCPushRecords()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// IOC类型标签样式
const getIOCTypeTagType = (iocType) => {
  const typeMap = {
    'IP': 'primary',
    'Domain': 'success',
    'URL': 'warning',
    'Hash': 'info',
    'Email': 'danger'
  }
  return typeMap[iocType] || 'info'
}

// 风险等级标签样式
const getRiskLevelTagType = (riskLevel) => {
  const levelMap = {
    '高': 'danger',
    '中': 'warning',
    '低': 'success',
    '信息': 'info'
  }
  return levelMap[riskLevel] || 'info'
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

onMounted(() => {
  console.log("PushManagement组件已挂载");

  // 获取通道和策略列表
  fetchPushChannels();
  fetchPushPolicies();

  // 获取推送记录
  fetchPushRecords();

  // 获取IOC推送记录
  fetchIOCPushRecords();

  // 获取白名单设置
  fetchPushWhitelist();

  // 获取RSS设置
  fetchRssConfig();
  
  // 获取导出配置和文件列表
  try {
    console.log("正在获取导出配置...");
    fetchExportConfig();
  } catch (error) {
    console.error("获取导出配置失败:", error);
  }
  
  try {
    console.log("正在获取导出文件列表...");
    fetchExportFiles();
  } catch (error) {
    console.error("获取导出文件列表失败:", error);
  }
  
  // 切换到推送通道选项卡
  activeTab.value = 'channels';
  console.log("当前选中的标签页:", activeTab.value);
})
</script>

<style scoped>
.push-management {
  padding: 0;
  height: 100%;
}

/* 自定义严重级别的标签样式 */
:deep(.el-tag--error) {
  background-color: #8b0000;
  border-color: #8b0000;
}

/* 自定义高危级别的标签样式 */
:deep(.el-tag--danger) {
  background-color: #ff4949;
  border-color: #ff4949;
  color: white; /* 添加白色文字颜色，确保与红色背景有足够对比度 */
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-header h3 {
  margin: 0;
}

.filter-container {
  display: flex;
  gap: 10px;
}

.empty-data {
  padding: 30px 0;
}

.text-secondary {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.text-danger {
  color: #f56c6c;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  width: 100%;
}

.debug-info {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.channel-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.vulnerability-detail {
  padding: 20px;
}

.vuln-description {
  white-space: pre-wrap;
}

.tag-item {
  margin-right: 5px;
  margin-bottom: 5px;
}

.reference-item {
  margin-bottom: 5px;
}

.rss-card {
  margin-bottom: 20px;
}

.rss-url-container {
  display: flex;
  align-items: center;
}

.rss-url-input {
  flex: 1;
}

.whitelist-card {
  margin-bottom: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  margin-top: 5px;
}

.export-card {
  margin-bottom: 20px;
}

.export-files-card {
  margin-top: 20px;
}

.push-reason {
  white-space: pre-wrap;
}

.ioc-description {
  white-space: pre-wrap;
}
</style>