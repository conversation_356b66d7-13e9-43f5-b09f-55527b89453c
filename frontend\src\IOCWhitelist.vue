<template>
  <div class="ioc-whitelist-container">
    <!-- 筛选条件 -->
    <el-card class="filter-container">
      <el-form :model="filterForm" inline>
        <el-form-item label="IOC值">
          <el-input
            v-model="filterForm.ioc"
            placeholder="请输入IOC值"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="IOC类型">
          <el-select
            v-model="filterForm.iocType"
            placeholder="请选择IOC类型"
            clearable
            style="width: 120px"
          >
            <el-option label="IP地址" value="ip" />
            <el-option label="域名" value="domain" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建人">
          <el-input
            v-model="filterForm.createdBy"
            placeholder="请输入创建人"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="关键词">
          <el-input
            v-model="filterForm.keyword"
            placeholder="搜索IOC值、备注或创建人"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="fetchWhitelist" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilter">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮和表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>IOC白名单列表</span>
          <div>
            <el-button type="primary" @click="handleCreate">
              <el-icon><Plus /></el-icon>
              新增白名单
            </el-button>
            <el-button type="success" @click="handleImport">
              <el-icon><Upload /></el-icon>
              批量导入
            </el-button>
            <el-button 
              type="danger" 
              @click="handleBatchDelete"
              :disabled="selectedRows.length === 0"
            >
              <el-icon><Delete /></el-icon>
              批量删除
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="whitelistData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="80" :index="getTableIndex" />
        <el-table-column prop="ioc" label="IOC值" min-width="200" />
        <el-table-column prop="iocType" label="IOC类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.iocType === 'ip' ? 'primary' : 'success'">
              {{ row.iocType === 'ip' ? 'IP地址' : '域名' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createdBy" label="创建人" width="120" />
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchWhitelist"
          @current-change="fetchWhitelist"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="isEditing ? '编辑IOC白名单' : '新增IOC白名单'"
      v-model="formDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="whitelistForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="IOC值" prop="ioc">
          <el-input
            v-model="whitelistForm.ioc"
            placeholder="支持IPv4/IPv6、CIDR、IP范围或域名"
            clearable
          />
          <div class="form-tip">
            <small style="color: #909399;">
              支持格式：***********、2001:db8::1、***********/24、2001:db8::/32、***********-100、***********-*************
            </small>
          </div>
        </el-form-item>
        <el-form-item label="IOC类型" prop="iocType">
          <el-select
            v-model="whitelistForm.iocType"
            placeholder="请选择IOC类型"
            style="width: 100%"
          >
            <el-option label="IP地址" value="ip" />
            <el-option label="域名" value="domain" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="whitelistForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="formDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            {{ isEditing ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      title="批量导入IOC白名单"
      v-model="importDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="import-content">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px"
        >
          <template #default>
            <p>1. 请上传CSV格式文件</p>
            <p>2. 文件格式：IOC值,IOC类型,备注</p>
            <p>3. IOC类型只支持：ip 或 domain</p>
            <p>4. IP地址支持IPv4和IPv6格式：</p>
            <p style="margin-left: 20px;">• 单个IPv4：***********</p>
            <p style="margin-left: 20px;">• 单个IPv6：2001:db8::1</p>
            <p style="margin-left: 20px;">• IPv4 CIDR：***********/24</p>
            <p style="margin-left: 20px;">• IPv6 CIDR：2001:db8::/32</p>
            <p style="margin-left: 20px;">• IPv4数字范围：***********-100</p>
            <p style="margin-left: 20px;">• IPv4完整范围：***********-*************</p>
            <p style="margin-left: 20px;">• IPv6范围：2001:db8::1-2001:db8::10</p>
            <p>5. 示例：***********/24,ip,内网段</p>
            <p>6. 注意：IPv6范围最多支持1024个地址，IPv4范围最多支持65536个地址</p>
          </template>
        </el-alert>

        <div style="margin-bottom: 20px;">
          <el-button type="info" @click="downloadTemplate" :loading="downloadingTemplate">
            <el-icon><Download /></el-icon>
            下载导入模板
          </el-button>
        </div>

        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :limit="1"
          accept=".csv"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :file-list="fileList"
          :before-upload="() => false"
        >
          <el-button type="primary">
            <el-icon><Upload /></el-icon>
            选择文件
          </el-button>
          <template #tip>
            <div class="el-upload__tip">只能上传CSV文件，且不超过10MB</div>
          </template>
        </el-upload>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="submitImport" 
            :loading="importing"
            :disabled="!selectedFile"
          >
            开始导入
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Upload, Delete, Edit, Download } from '@element-plus/icons-vue'
import api, { type IOCWhitelist } from './api'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const whitelistData = ref<IOCWhitelist[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const selectedRows = ref<IOCWhitelist[]>([])

// 筛选表单
const filterForm = reactive({
  ioc: '',
  iocType: '',
  createdBy: '',
  keyword: ''
})

// 表单相关
const formDialogVisible = ref(false)
const isEditing = ref(false)
const submitting = ref(false)
const formRef = ref(null)

// IOC白名单表单数据
const whitelistForm = ref({
  id: undefined,
  ioc: '',
  iocType: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  ioc: [
    { required: true, message: '请输入IOC值', trigger: 'blur' }
  ],
  iocType: [
    { required: true, message: '请选择IOC类型', trigger: 'change' }
  ]
}

// 导入相关
const importDialogVisible = ref(false)
const importing = ref(false)
const uploadRef = ref(null)
const fileList = ref([])
const selectedFile = ref<File | null>(null)
const downloadingTemplate = ref(false)

// 格式化时间
const formatTime = (timestamp: number) => {
  return dayjs(timestamp * 1000).format('YYYY-MM-DD HH:mm:ss')
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.ioc = ''
  filterForm.iocType = ''
  filterForm.createdBy = ''
  filterForm.keyword = ''
  currentPage.value = 1
  fetchWhitelist()
}

// 处理选择变化
const handleSelectionChange = (selection: IOCWhitelist[]) => {
  selectedRows.value = selection
}

// 计算表格序号
const getTableIndex = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1
}

// 获取IOC白名单数据
const fetchWhitelist = async () => {
  loading.value = true

  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ioc: filterForm.ioc,
      ioc_type: filterForm.iocType,
      created_by: filterForm.createdBy,
      keyword: filterForm.keyword || '',
      order_by: 'created_at',
      order_dir: 'desc'
    }

    // 过滤空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await api.getIOCWhitelist(params)

    if (response.data && response.data.data) {
      whitelistData.value = response.data.data.list || []
      total.value = response.data.data.pagination?.total || 0
    } else {
      whitelistData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取IOC白名单列表失败:', error)
    ElMessage.error('获取IOC白名单列表失败')
    whitelistData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理创建
const handleCreate = () => {
  isEditing.value = false
  whitelistForm.value = {
    id: undefined,
    ioc: '',
    iocType: '',
    remark: ''
  }
  formDialogVisible.value = true
}

// 处理编辑
const handleEdit = (row: IOCWhitelist) => {
  isEditing.value = true
  whitelistForm.value = {
    id: row.id,
    ioc: row.ioc,
    iocType: row.iocType,
    remark: row.remark || ''
  }
  formDialogVisible.value = true
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const formData = { ...whitelistForm.value }

    if (isEditing.value) {
      // 更新IOC白名单
      await api.updateIOCWhitelist(formData.id, formData)
      ElMessage.success('更新成功')
    } else {
      // 创建IOC白名单
      await api.createIOCWhitelist(formData)
      ElMessage.success('创建成功')
    }

    formDialogVisible.value = false
    fetchWhitelist()
  } catch (error) {
    if (error.response && error.response.data && error.response.data.msg) {
      ElMessage.error(error.response.data.msg)
    } else {
      ElMessage.error(isEditing.value ? '更新失败' : '创建失败')
    }
    console.error('提交表单失败:', error)
  } finally {
    submitting.value = false
  }
}

// 处理删除
const handleDelete = async (row: IOCWhitelist) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除IOC白名单 "${row.ioc}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await api.deleteIOCWhitelist(row.id!)
    ElMessage.success('删除成功')
    fetchWhitelist()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除IOC白名单失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 处理批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条IOC白名单记录吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const ids = selectedRows.value.map(row => row.id!)
    await api.batchDeleteIOCWhitelist(ids)
    ElMessage.success('批量删除成功')
    fetchWhitelist()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除IOC白名单失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 处理导入
const handleImport = () => {
  importDialogVisible.value = true
  fileList.value = []
  selectedFile.value = null
}

// 处理文件变化
const handleFileChange = (file: any, fileList: any[]) => {
  console.log('文件选择:', file, fileList)
  if (file && file.raw) {
    selectedFile.value = file.raw
    console.log('选择的文件:', selectedFile.value)
  }
}

// 处理文件移除
const handleFileRemove = () => {
  selectedFile.value = null
}

// 下载导入模板
const downloadTemplate = async () => {
  try {
    downloadingTemplate.value = true
    const response = await api.downloadIOCWhitelistTemplate()

    // 创建下载链接
    const blob = new Blob([response.data], { type: 'text/csv;charset=utf-8' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'ioc_whitelist_template.csv'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败')
  } finally {
    downloadingTemplate.value = false
  }
}

// 提交导入
const submitImport = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  console.log('开始导入文件:', selectedFile.value.name, selectedFile.value.size)

  try {
    importing.value = true

    // 检查文件大小（提高到50MB）
    if (selectedFile.value.size > 50 * 1024 * 1024) {
      ElMessage.error('文件大小不能超过50MB')
      return
    }

    // 检查文件类型
    if (!selectedFile.value.name.toLowerCase().endsWith('.csv')) {
      ElMessage.error('只支持CSV格式文件')
      return
    }

    // 显示处理提示
    const loadingMessage = ElMessage({
      message: '正在处理文件，请耐心等待...',
      type: 'info',
      duration: 0, // 不自动关闭
      showClose: false
    })

    console.log('正在调用API...')
    const response = await api.batchImportIOCWhitelist(selectedFile.value)
    console.log('API响应:', response)

    // 关闭加载提示
    loadingMessage.close()

    if (response.data && response.data.data) {
      const data = response.data.data
      const {
        processed_count = 0,
        skipped_count = 0,
        error_count = 0,
        warning_count = 0,
        errors = [],
        warnings = [],
        message = ''
      } = data

      // 构建结果消息
      let resultMessage = message || '导入完成'
      let resultDetails = []

      if (processed_count > 0) {
        resultDetails.push(`成功处理: ${processed_count} 条`)
      }
      if (skipped_count > 0) {
        resultDetails.push(`跳过重复: ${skipped_count} 条`)
      }
      if (error_count > 0) {
        resultDetails.push(`错误: ${error_count} 条`)
      }
      if (warning_count > 0) {
        resultDetails.push(`警告: ${warning_count} 条`)
      }

      if (resultDetails.length > 0) {
        resultMessage += `\n\n统计信息:\n${resultDetails.join('\n')}`
      }

      // 添加错误详情
      if (errors.length > 0) {
        resultMessage += `\n\n错误详情:\n${errors.slice(0, 10).join('\n')}`
        if (errors.length > 10) {
          resultMessage += '\n...还有更多错误'
        }
      }

      // 添加警告详情
      if (warnings.length > 0) {
        resultMessage += `\n\n警告信息:\n${warnings.slice(0, 5).join('\n')}`
        if (warnings.length > 5) {
          resultMessage += '\n...还有更多警告'
        }
      }

      // 根据结果类型显示不同的消息
      if (error_count > 0 && processed_count === 0) {
        // 完全失败
        ElMessageBox.alert(resultMessage, '导入失败', {
          type: 'error',
          customStyle: { 'white-space': 'pre-line' }
        })
      } else if (error_count > 0 || warning_count > 0) {
        // 部分成功
        ElMessageBox.alert(resultMessage, '导入完成（有问题）', {
          type: 'warning',
          customStyle: { 'white-space': 'pre-line' }
        })
      } else {
        // 完全成功
        ElMessage.success(resultMessage)
      }
    } else {
      ElMessage.success('导入完成')
    }

    importDialogVisible.value = false
    fetchWhitelist()
  } catch (error) {
    console.error('批量导入失败:', error)

    // 更详细的错误处理
    if (error.response) {
      console.error('响应错误:', error.response.status, error.response.data)

      let errorMessage = '导入失败'

      if (error.response.status === 413) {
        errorMessage = '文件过大，请减小文件大小后重试'
      } else if (error.response.status === 408) {
        errorMessage = '请求超时，文件可能过大或网络较慢，请稍后重试'
      } else if (error.response.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message
        } else if (error.response.data.msg) {
          errorMessage = error.response.data.msg
        } else if (error.response.data.errors) {
          const errors = Array.isArray(error.response.data.errors)
            ? error.response.data.errors
            : [error.response.data.errors]
          errorMessage = `导入失败：\n${errors.slice(0, 5).join('\n')}`
          if (errors.length > 5) {
            errorMessage += '\n...还有更多错误'
          }
        }
      }

      ElMessageBox.alert(errorMessage, '导入失败', {
        type: 'error',
        customStyle: { 'white-space': 'pre-line' }
      })
    } else if (error.request) {
      console.error('网络错误:', error.request)
      ElMessage.error('网络连接失败或请求超时，请检查网络设置后重试')
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，文件可能过大，请稍后重试')
    } else {
      console.error('其他错误:', error.message)
      ElMessage.error('批量导入失败：' + error.message)
    }
  } finally {
    importing.value = false
  }
}

onMounted(() => {
  fetchWhitelist()
})
</script>

<style scoped>
.ioc-whitelist-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}

.import-content {
  padding: 20px 0;
}

.form-tip {
  margin-top: 5px;
}
</style>
