package service

import (
	"context"
	"vulnerability_push/internal/models"
)

// UserServiceInterface 用户服务接口
type UserServiceInterface interface {
	// 用户认证
	Login(ctx context.Context, username, password string) (*LoginResult, error)
	ValidateAPIKey(ctx context.Context, apiKey string) (*User, error)
	
	// 用户管理
	GetUsers(ctx context.Context, req *GetUsersRequest) (*GetUsersResponse, error)
	GetUserByID(ctx context.Context, id uint) (*User, error)
	CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error)
	UpdateUser(ctx context.Context, id uint, req *UpdateUserRequest) error
	DeleteUser(ctx context.Context, id uint) error
	
	// 密码管理
	ChangePassword(ctx context.Context, userID uint, oldPassword, newPassword string) error
	AdminChangePassword(ctx context.Context, userID uint, newPassword string) error
	Reset<PERSON><PERSON>Key(ctx context.Context, userID uint) (string, error)
}

// VulnerabilityServiceInterface 漏洞服务接口
type VulnerabilityServiceInterface interface {
	// 漏洞查询
	GetVulnerabilities(ctx context.Context, req *GetVulnerabilitiesRequest) (*GetVulnerabilitiesResponse, error)
	GetVulnerabilityByID(ctx context.Context, id uint) (*Vulnerability, error)
	GetVulnerabilityStats(ctx context.Context) (*VulnerabilityStats, error)
	
	// 漏洞管理
	CreateVulnerability(ctx context.Context, req *CreateVulnerabilityRequest) (*Vulnerability, error)
	UpdateVulnerability(ctx context.Context, id uint, req *UpdateVulnerabilityRequest) error
	DeleteVulnerability(ctx context.Context, id uint) error
	
	// 漏洞导出
	ExportVulnerabilities(ctx context.Context, req *ExportVulnerabilitiesRequest) (string, error)
}

// CrawlerServiceInterface 采集器服务接口
type CrawlerServiceInterface interface {
	// 采集器管理
	GetCrawlers(ctx context.Context, req *GetCrawlersRequest) (*GetCrawlersResponse, error)
	GetCrawlerByID(ctx context.Context, id uint) (*Crawler, error)
	CreateCrawler(ctx context.Context, req *CreateCrawlerRequest) (*Crawler, error)
	UpdateCrawler(ctx context.Context, id uint, req *UpdateCrawlerRequest) error
	DeleteCrawler(ctx context.Context, id uint) error
	
	// 采集器执行
	RunCrawler(ctx context.Context, id uint) (*CrawlerRunResult, error)
	GetCrawlerLogs(ctx context.Context, req *GetCrawlerLogsRequest) (*GetCrawlerLogsResponse, error)
}

// PushServiceInterface 推送服务接口
type PushServiceInterface interface {
	// 推送通道管理
	GetPushChannels(ctx context.Context) ([]*PushChannel, error)
	GetPushChannelByID(ctx context.Context, id uint) (*PushChannel, error)
	CreatePushChannel(ctx context.Context, req *CreatePushChannelRequest) (*PushChannel, error)
	UpdatePushChannel(ctx context.Context, id uint, req *UpdatePushChannelRequest) error
	DeletePushChannel(ctx context.Context, id uint) error
	TestPushChannel(ctx context.Context, id uint) error
	
	// 推送策略管理
	GetPushPolicies(ctx context.Context) ([]*PushPolicy, error)
	CreatePushPolicy(ctx context.Context, req *CreatePushPolicyRequest) (*PushPolicy, error)
	UpdatePushPolicy(ctx context.Context, id uint, req *UpdatePushPolicyRequest) error
	DeletePushPolicy(ctx context.Context, id uint) error
	
	// 推送执行
	PushVulnerability(ctx context.Context, vulnID uint) error
	GetPushRecords(ctx context.Context, req *GetPushRecordsRequest) (*GetPushRecordsResponse, error)
}

// IOCServiceInterface IOC情报服务接口
type IOCServiceInterface interface {
	// IOC情报管理
	GetIOCIntelligence(ctx context.Context, req *GetIOCIntelligenceRequest) (*GetIOCIntelligenceResponse, error)
	GetIOCIntelligenceByID(ctx context.Context, id uint) (*models.IOCIntelligence, error)
	CreateIOCIntelligence(ctx context.Context, req *CreateIOCIntelligenceRequest) (*models.IOCIntelligence, error)
	UpdateIOCIntelligence(ctx context.Context, id uint, req *UpdateIOCIntelligenceRequest) error
	DeleteIOCIntelligence(ctx context.Context, id uint) error
	
	// IOC白名单管理
	GetIOCWhitelist(ctx context.Context, req *GetIOCWhitelistRequest) (*GetIOCWhitelistResponse, error)
	CreateIOCWhitelist(ctx context.Context, req *CreateIOCWhitelistRequest) (*models.IOCWhitelist, error)
	UpdateIOCWhitelist(ctx context.Context, id uint, req *UpdateIOCWhitelistRequest) error
	DeleteIOCWhitelist(ctx context.Context, id uint) error
	
	// IOC情报推送
	PushIOCIntelligence(ctx context.Context, iocID uint) error
}

// DataInterfaceServiceInterface 数据接口服务接口
type DataInterfaceServiceInterface interface {
	// 数据接口管理
	GetDataInterfaces(ctx context.Context, req *GetDataInterfacesRequest) (*GetDataInterfacesResponse, error)
	GetDataInterfaceByID(ctx context.Context, id uint) (*DataInterface, error)
	CreateDataInterface(ctx context.Context, req *CreateDataInterfaceRequest) (*DataInterface, error)
	UpdateDataInterface(ctx context.Context, id uint, req *UpdateDataInterfaceRequest) error
	DeleteDataInterface(ctx context.Context, id uint) error
	
	// 数据接口执行
	RunDataInterface(ctx context.Context, id uint) error
	GetDataInterfaceLogs(ctx context.Context, req *GetDataInterfaceLogsRequest) (*GetDataInterfaceLogsResponse, error)
}
