package push

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

// 飞书推送类型常量
const TypeLark = "lark"

// LarkBotConfig 飞书机器人配置
type LarkBotConfig struct {
	AccessToken string `yaml:"access_token" json:"access_token"`
}

// LarkCardMessage 飞书卡片消息结构
type LarkCardMessage struct {
	MsgType string `json:"msg_type"`
	Card    struct {
		Config struct {
			WideScreenMode bool   `json:"wide_screen_mode"`
			EnableForward  bool   `json:"enable_forward"`
			UpdateMulti    bool   `json:"update_multi"`
			HeaderIcon     string `json:"header_icon,omitempty"`
		} `json:"config"`
		Header struct {
			Title struct {
				Tag     string `json:"tag"`
				Content string `json:"content"`
				Lines   int    `json:"lines,omitempty"`
			} `json:"title"`
			Template string `json:"template"`
		} `json:"header"`
		Elements []map[string]interface{} `json:"elements"`
	} `json:"card"`
}

// 推送到飞书机器人
func PushToLark(vuln *Vulnerability, channel *PushChannel, record *PushRecord) error {
	// 获取通道配置
	config := channel.GetConfig()
	if config == nil {
		return fmt.Errorf("通道配置为空")
	}
	
	// 检查access_token是否存在
	accessToken, ok := config["access_token"].(string)
	if !ok || accessToken == "" {
		return fmt.Errorf("Access Token未配置或格式不正确")
	}
	
	// 构建飞书webhook URL
	webhookURL := fmt.Sprintf("https://open.feishu.cn/open-apis/bot/v2/hook/%s", accessToken)

	// 获取严重程度对应的颜色
	var headerColor string
	switch vuln.Severity {
	case "严重":
		headerColor = "red"
	case "高危":
		headerColor = "orange"
	case "中危":
		headerColor = "yellow"
	case "低危":
		headerColor = "blue"
	case "信息":
		headerColor = "grey"
	default:
		headerColor = "blue"
	}

	// 构建卡片消息
	message := LarkCardMessage{
		MsgType: "interactive",
	}
	message.Card.Config.WideScreenMode = true
	message.Card.Config.EnableForward = true
	message.Card.Config.UpdateMulti = false

	// 设置标题
	message.Card.Header.Title.Tag = "plain_text"
	message.Card.Header.Title.Content = fmt.Sprintf("%s漏洞告警: %s", vuln.Severity, vuln.Name)
	message.Card.Header.Template = headerColor

	// 添加基本信息
	message.Card.Elements = []map[string]interface{}{
		{
			"tag": "div",
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": fmt.Sprintf("**漏洞编号**: %s", vuln.VulnID),
			},
		},
		{
			"tag": "div",
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": fmt.Sprintf("**披露日期**: %s", vuln.DisclosureDate),
			},
		},
	}

	// 添加来源信息
	if vuln.Source != "" {
		// 如果源URL过长，进行截断
		source := vuln.Source
		if len([]rune(source)) > 100 {
			runeSource := []rune(source)
			source = string(runeSource[:100]) + "..."
		}
		message.Card.Elements = append(message.Card.Elements, map[string]interface{}{
			"tag": "div",
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": fmt.Sprintf("**信息来源**: %s", source),
			},
		})
	}

	// 添加描述
	if vuln.Description != "" {
		desc := vuln.Description
		if len([]rune(desc)) > 200 {
			runeDesc := []rune(desc)
			desc = string(runeDesc[:200]) + "..."
		}
		message.Card.Elements = append(message.Card.Elements, map[string]interface{}{
			"tag": "div",
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": fmt.Sprintf("**漏洞描述**:\n%s", desc),
			},
		})
	}

	// 添加推送原因
	if vuln.PushReason != "" {
		reason := vuln.PushReason
		if len([]rune(reason)) > 100 {
			runeReason := []rune(reason)
			reason = string(runeReason[:100]) + "..."
		}
		message.Card.Elements = append(message.Card.Elements, map[string]interface{}{
			"tag": "div",
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": fmt.Sprintf("**推送原因**:\n%s", reason),
			},
		})
	}

	// 添加修复建议
	if vuln.Remediation != "" {
		remediation := vuln.Remediation
		if len([]rune(remediation)) > 200 {
			runeRemediation := []rune(remediation)
			remediation = string(runeRemediation[:200]) + "..."
		}
		message.Card.Elements = append(message.Card.Elements, map[string]interface{}{
			"tag": "div",
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": fmt.Sprintf("**修复建议**:\n%s", remediation),
			},
		})
	}

	// 添加参考链接
	references := vuln.GetReferences()
	if len(references) > 0 {
		// 最多只显示3个参考链接，避免消息过长
		maxLinks := 3
		if len(references) > maxLinks {
			references = references[:maxLinks]
		}

		linksContent := "**参考链接**:\n"
		for _, ref := range references {
			// 处理过长的链接
			linkText := ref
			if len([]rune(linkText)) > 80 {
				runeLink := []rune(linkText)
				linkText = string(runeLink[:80]) + "..."
			}
			linksContent += linkText + "\n"
		}

		// 如果还有更多链接，添加提示
		if len(vuln.GetReferences()) > maxLinks {
			linksContent += "(更多链接省略...)"
		}

		message.Card.Elements = append(message.Card.Elements, map[string]interface{}{
			"tag": "div",
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": linksContent,
			},
		})
	}

	// 添加时间戳
	message.Card.Elements = append(message.Card.Elements, map[string]interface{}{
		"tag": "note",
		"elements": []map[string]interface{}{
			{
				"tag":     "plain_text",
				"content": fmt.Sprintf("推送时间: %s", time.Now().Format("2006-01-02 15:04:05")),
			},
		},
	})

	// 将消息转换为JSON
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("构建消息失败: %v", err)
	}

	// 发送请求
	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("推送失败，HTTP状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var result struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	if result.Code != 0 {
		return fmt.Errorf("推送失败: %s (错误码: %d)", result.Msg, result.Code)
	}

	return nil
}

// 推送IOC情报到飞书机器人
func PushIOCIntelligenceToLark(ioc *IOCIntelligence, channel *PushChannel, record *IOCIntelligencePushRecord) error {
	// 获取通道配置
	config := channel.GetConfig()
	if config == nil {
		return fmt.Errorf("通道配置为空")
	}

	// 检查access_token是否存在
	accessToken, ok := config["access_token"].(string)
	if !ok || accessToken == "" {
		return fmt.Errorf("AccessToken未配置或格式不正确")
	}

	// 构建webhook URL
	webhookURL := fmt.Sprintf("https://open.feishu.cn/open-apis/bot/v2/hook/%s", accessToken)

	// 构建卡片消息
	message := LarkCardMessage{
		MsgType: "interactive",
	}

	// 设置卡片配置
	message.Card.Config.WideScreenMode = true
	message.Card.Config.EnableForward = true
	message.Card.Config.UpdateMulti = false

	// 设置标题和模板颜色
	riskTemplate := "blue"
	switch ioc.RiskLevel {
	case "严重":
		riskTemplate = "red"
	case "高危":
		riskTemplate = "orange"
	case "中危":
		riskTemplate = "yellow"
	case "低危":
		riskTemplate = "green"
	case "信息":
		riskTemplate = "blue"
	}

	message.Card.Header.Title.Tag = "plain_text"
	message.Card.Header.Title.Content = fmt.Sprintf("%s IOC情报告警", ioc.RiskLevel)
	message.Card.Header.Template = riskTemplate

	// 构建卡片元素
	elements := []map[string]interface{}{}

	// IOC值
	elements = append(elements, map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"is_short": true,
				"text": map[string]interface{}{
					"tag":     "lark_md",
					"content": fmt.Sprintf("**IOC值**\n%s", ioc.IOC),
				},
			},
			{
				"is_short": true,
				"text": map[string]interface{}{
					"tag":     "lark_md",
					"content": fmt.Sprintf("**IOC类型**\n%s", ioc.IOCType),
				},
			},
		},
	})

	// 地理位置和威胁类型
	if ioc.Location != "" || ioc.Type != "" {
		fields := []map[string]interface{}{}
		if ioc.Location != "" {
			fields = append(fields, map[string]interface{}{
				"is_short": true,
				"text": map[string]interface{}{
					"tag":     "lark_md",
					"content": fmt.Sprintf("**地理位置**\n%s", ioc.Location),
				},
			})
		}
		if ioc.Type != "" {
			fields = append(fields, map[string]interface{}{
				"is_short": true,
				"text": map[string]interface{}{
					"tag":     "lark_md",
					"content": fmt.Sprintf("**威胁类型**\n%s", ioc.Type),
				},
			})
		}
		elements = append(elements, map[string]interface{}{
			"tag":    "div",
			"fields": fields,
		})
	}

	// 命中次数
	if ioc.HitCount > 0 {
		fields := []map[string]interface{}{}
		fields = append(fields, map[string]interface{}{
			"is_short": true,
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": fmt.Sprintf("**命中次数**\n%d", ioc.HitCount),
			},
		})
		elements = append(elements, map[string]interface{}{
			"tag":    "div",
			"fields": fields,
		})
	}

	// 威胁描述
	if ioc.Description != "" {
		desc := ioc.Description
		if len([]rune(desc)) > 200 {
			runeDesc := []rune(desc)
			desc = string(runeDesc[:200]) + "..."
		}
		elements = append(elements, map[string]interface{}{
			"tag": "div",
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": fmt.Sprintf("**威胁描述**\n%s", desc),
			},
		})
	}

	// 情报来源
	if ioc.Source != "" {
		source := ioc.Source
		if len([]rune(source)) > 100 {
			runeSource := []rune(source)
			source = string(runeSource[:100]) + "..."
		}
		elements = append(elements, map[string]interface{}{
			"tag": "div",
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": fmt.Sprintf("**情报来源**\n%s", source),
			},
		})
	}

	// 分隔线
	elements = append(elements, map[string]interface{}{
		"tag": "hr",
	})

	// 时间信息
	timeFields := []map[string]interface{}{}
	if ioc.DiscoveryDate != "" {
		timeFields = append(timeFields, map[string]interface{}{
			"is_short": true,
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": fmt.Sprintf("**发现日期**\n%s", ioc.DiscoveryDate),
			},
		})
	}
	timeFields = append(timeFields, map[string]interface{}{
		"is_short": true,
		"text": map[string]interface{}{
			"tag":     "lark_md",
			"content": fmt.Sprintf("**推送时间**\n%s", time.Now().Format("2006-01-02 15:04:05")),
		},
	})

	elements = append(elements, map[string]interface{}{
		"tag":    "div",
		"fields": timeFields,
	})

	message.Card.Elements = elements

	// 序列化消息
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("构建消息失败: %v", err)
	}

	// 发送请求
	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("推送失败，HTTP状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var result struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	if result.Code != 0 {
		return fmt.Errorf("推送失败: %s (错误码: %d)", result.Msg, result.Code)
	}

	return nil
}

// 批量推送IOC情报到飞书机器人
func BatchPushIOCIntelligenceToLark(iocIntels []*IOCIntelligence, channel *PushChannel) error {
	// 获取通道配置
	config := channel.GetConfig()
	if config == nil {
		return fmt.Errorf("通道配置为空")
	}

	// 检查access_token是否存在
	accessToken, ok := config["access_token"].(string)
	if !ok || accessToken == "" {
		return fmt.Errorf("AccessToken未配置或格式不正确")
	}

	// 构建webhook URL
	webhookURL := fmt.Sprintf("https://open.feishu.cn/open-apis/bot/v2/hook/%s", accessToken)

	// 构建批量推送内容
	content := buildBatchIOCPushContent(iocIntels)

	// 构建简单的文本消息（批量推送使用文本格式更简洁）
	message := map[string]interface{}{
		"msg_type": "text",
		"content": map[string]interface{}{
			"text": content,
		},
	}

	// 序列化消息
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("构建消息失败: %v", err)
	}

	// 发送请求
	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("推送失败，HTTP状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var result struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	if result.Code != 0 {
		return fmt.Errorf("推送失败: %s (错误码: %d)", result.Msg, result.Code)
	}

	return nil
}