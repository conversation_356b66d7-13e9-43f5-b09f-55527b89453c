package push

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"
	"vulnerability_push/internal/utils"
)

// 为了保持兼容性，定义日志函数别名
var (
	// Infof 信息级别日志
	Infof = utils.Infof

	// Debugf 调试级别日志
	Debugf = utils.Debugf

	// Warnf 警告级别日志
	Warnf = utils.Warnf

	// Errorf 错误级别日志
	Errorf = utils.Errorf

	// Fatalf 致命错误日志
	Fatalf = utils.Fatalf
)

// splitLines 按行分割字符串
func splitLines(s string) []string {
	if s == "" {
		return []string{}
	}
	
	// 处理不同的换行符
	s = strings.ReplaceAll(s, "\r\n", "\n")
	s = strings.ReplaceAll(s, "\r", "\n")
	
	lines := strings.Split(s, "\n")
	var result []string
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			result = append(result, line)
		}
	}
	
	return result
}

// splitByComma 按逗号分割字符串
func splitByComma(s string) []string {
	if s == "" {
		return []string{}
	}
	
	parts := strings.Split(s, ",")
	var result []string
	
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part != "" {
			result = append(result, part)
		}
	}
	
	return result
}

// SetConfig 设置通道配置
func (c *PushChannel) SetConfig(config map[string]interface{}) error {
	if config == nil {
		c.Config = ""
		return nil
	}
	
	configJSON, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}
	
	c.Config = string(configJSON)
	return nil
}

// buildBatchIOCPushContent 构建批量IOC推送内容
func buildBatchIOCPushContent(iocIntels []*IOCIntelligence) string {
	if len(iocIntels) == 0 {
		return ""
	}

	// 按IOC类型分组
	ipIOCs := []string{}
	domainIOCs := []string{}

	// 统计信息
	totalHitCount := 0
	typeSet := make(map[string]bool)
	highestRiskLevel := ""
	highestRiskScore := 0
	targetOrgSet := make(map[string]bool)
	sourceSet := make(map[string]bool)

	// 风险等级评分映射
	riskScores := map[string]int{
		"严重": 5,
		"高危": 4,
		"中危": 3,
		"低危": 2,
		"信息": 1,
	}

	// 遍历所有IOC情报，收集统计信息
	for _, iocIntel := range iocIntels {
		// 按类型分组
		if strings.ToLower(iocIntel.IOCType) == "ip" {
			ipIOCs = append(ipIOCs, iocIntel.IOC)
		} else {
			domainIOCs = append(domainIOCs, iocIntel.IOC)
		}

		// 统计命中次数
		totalHitCount += iocIntel.HitCount

		// 收集威胁类型
		if iocIntel.Type != "" {
			typeSet[iocIntel.Type] = true
		}

		// 找出最高风险等级
		if score, exists := riskScores[iocIntel.RiskLevel]; exists {
			if score > highestRiskScore {
				highestRiskScore = score
				highestRiskLevel = iocIntel.RiskLevel
			}
		}

		// 移除目标组织收集，已替换为推送状态

		// 收集情报来源
		if iocIntel.Source != "" {
			sourceSet[iocIntel.Source] = true
		}
	}

	// 构建推送内容
	riskEmoji := "⚠️ 警告级"
	switch highestRiskLevel {
	case "严重":
		riskEmoji = "🚨 严重级"
	case "高危":
		riskEmoji = "🔥 高危级"
	case "中危":
		riskEmoji = "⚠️ 中危级"
	case "低危":
		riskEmoji = "💡 低危级"
	case "信息":
		riskEmoji = "ℹ️ 信息级"
	}

	content := fmt.Sprintf("## %s IOC情报批量预警 (%d条)\n\n", riskEmoji, len(iocIntels))

	// 显示IOC列表
	if len(ipIOCs) > 0 {
		content += "> **恶意IP:**\n"
		for _, ip := range ipIOCs {
			content += fmt.Sprintf("> • %s\n", ip)
		}
		content += "\n"
	}

	if len(domainIOCs) > 0 {
		content += "> **恶意域名:**\n"
		for _, domain := range domainIOCs {
			content += fmt.Sprintf("> • %s\n", domain)
		}
		content += "\n"
	}

	// 显示统计信息
	// 风险等级（显示最高等级）
	if highestRiskLevel != "" {
		content += fmt.Sprintf("> **风险等级**: %s\n\n", highestRiskLevel)
	}

	// 情报类型（去重显示）
	if len(typeSet) > 0 {
		types := make([]string, 0, len(typeSet))
		for t := range typeSet {
			types = append(types, t)
		}
		content += fmt.Sprintf("> **情报类型**: %s\n\n", strings.Join(types, ", "))
	}

	// 总命中次数
	if totalHitCount > 0 {
		content += fmt.Sprintf("> **总命中次数**: %d\n\n", totalHitCount)
	}

	// 目标组织（去重显示，最多显示5个）
	if len(targetOrgSet) > 0 {
		targetOrgs := make([]string, 0, len(targetOrgSet))
		for org := range targetOrgSet {
			targetOrgs = append(targetOrgs, org)
		}
		if len(targetOrgs) > 5 {
			targetOrgs = targetOrgs[:5]
			content += fmt.Sprintf("> **目标组织**: %s 等\n\n", strings.Join(targetOrgs, ", "))
		} else {
			content += fmt.Sprintf("> **目标组织**: %s\n\n", strings.Join(targetOrgs, ", "))
		}
	}

	// 情报来源（去重显示，最多显示3个）
	if len(sourceSet) > 0 {
		sources := make([]string, 0, len(sourceSet))
		for source := range sourceSet {
			sources = append(sources, source)
		}
		if len(sources) > 3 {
			sources = sources[:3]
			content += fmt.Sprintf("> **情报来源**: %s 等\n\n", strings.Join(sources, ", "))
		} else {
			content += fmt.Sprintf("> **情报来源**: %s\n\n", strings.Join(sources, ", "))
		}
	}

	// 添加时间戳
	content += fmt.Sprintf("\n> 推送时间: %s", time.Now().Format("2006-01-02 15:04:05"))

	return content
}