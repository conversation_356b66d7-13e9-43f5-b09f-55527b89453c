package utils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"
)

// StringUtils 字符串工具函数

// SanitizeString 清理字符串，确保UTF-8编码正确
func SanitizeString(s string) string {
	if s == "" {
		return s
	}

	// 将无效的UTF-8序列替换为空格
	result := strings.ToValidUTF8(s, " ")
	
	// 移除零宽度空格和其他可能导致问题的不可见字符
	result = strings.ReplaceAll(result, "\u200B", "") // 零宽度空格
	result = strings.ReplaceAll(result, "\u200C", "") // 零宽度非连接符
	result = strings.ReplaceAll(result, "\u200D", "") // 零宽度连接符
	result = strings.ReplaceAll(result, "\u200E", "") // 从左到右标记
	result = strings.ReplaceAll(result, "\u200F", "") // 从右到左标记
	result = strings.ReplaceAll(result, "\uFEFF", "") // 零宽度不换行空格
	
	// 替换其他特殊控制字符
	for i := 0; i < len(result); i++ {
		if result[i] < 32 && result[i] != '\n' && result[i] != '\r' && result[i] != '\t' {
			if i == 0 {
				result = " " + result[1:]
			} else if i == len(result)-1 {
				result = result[:i] + " "
			} else {
				result = result[:i] + " " + result[i+1:]
			}
		}
	}
	
	return result
}

// TruncateString 截断字符串到指定长度
func TruncateString(s string, maxLen int) string {
	if utf8.RuneCountInString(s) <= maxLen {
		return s
	}
	
	runes := []rune(s)
	if len(runes) <= maxLen-3 {
		return s
	}
	
	return string(runes[:maxLen-3]) + "..."
}

// JoinStrings 将字符串数组转换为逗号分隔的字符串
func JoinStrings(arr []string) string {
	return strings.Join(arr, ",")
}

// SplitString 将逗号分隔的字符串转换为字符串数组
func SplitString(s string) []string {
	if s == "" {
		return []string{}
	}
	
	parts := strings.Split(s, ",")
	result := make([]string, 0, len(parts))
	for _, part := range parts {
		if trimmed := strings.TrimSpace(part); trimmed != "" {
			result = append(result, trimmed)
		}
	}
	return result
}

// ContainsString 检查字符串数组是否包含指定字符串
func ContainsString(arr []string, target string) bool {
	for _, item := range arr {
		if item == target {
			return true
		}
	}
	return false
}

// ValidationUtils 验证工具函数

// IsValidEmail 验证邮箱格式
func IsValidEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// IsValidIP 验证IP地址格式
func IsValidIP(ip string) bool {
	ipRegex := regexp.MustCompile(`^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`)
	return ipRegex.MatchString(ip)
}

// IsValidDomain 验证域名格式
func IsValidDomain(domain string) bool {
	domainRegex := regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$`)
	return domainRegex.MatchString(domain)
}

// IsValidCVE 验证CVE编号格式
func IsValidCVE(cve string) bool {
	cveRegex := regexp.MustCompile(`^CVE-\d{4}-\d{4,}$`)
	return cveRegex.MatchString(cve)
}

// CryptoUtils 加密工具函数

// GenerateRandomString 生成指定长度的随机字符串
func GenerateRandomString(length int) (string, error) {
	bytes := make([]byte, length/2)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes)[:length], nil
}

// GenerateAPIKey 生成API密钥
func GenerateAPIKey() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// TimeUtils 时间工具函数

// FormatUnixTime 格式化Unix时间戳
func FormatUnixTime(timestamp int64) string {
	if timestamp == 0 {
		return ""
	}
	return time.Unix(timestamp, 0).Format("2006-01-02 15:04:05")
}

// FormatUnixTimeToDate 格式化Unix时间戳为日期
func FormatUnixTimeToDate(timestamp int64) string {
	if timestamp == 0 {
		return ""
	}
	return time.Unix(timestamp, 0).Format("2006-01-02")
}

// ParseDateString 解析日期字符串
func ParseDateString(dateStr string) (time.Time, error) {
	layouts := []string{
		"2006-01-02",
		"2006-01-02 15:04:05",
		"2006/01/02",
		"2006/01/02 15:04:05",
		"01/02/2006",
		"01/02/2006 15:04:05",
	}
	
	for _, layout := range layouts {
		if t, err := time.Parse(layout, dateStr); err == nil {
			return t, nil
		}
	}
	
	return time.Time{}, fmt.Errorf("无法解析日期字符串: %s", dateStr)
}

// GetStartOfDay 获取指定日期的开始时间
func GetStartOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// GetEndOfDay 获取指定日期的结束时间
func GetEndOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 999999999, t.Location())
}

// ConversionUtils 转换工具函数

// StringToInt 字符串转整数，失败返回默认值
func StringToInt(s string, defaultValue int) int {
	if i, err := strconv.Atoi(s); err == nil {
		return i
	}
	return defaultValue
}

// StringToInt64 字符串转int64，失败返回默认值
func StringToInt64(s string, defaultValue int64) int64 {
	if i, err := strconv.ParseInt(s, 10, 64); err == nil {
		return i
	}
	return defaultValue
}

// StringToBool 字符串转布尔值，失败返回默认值
func StringToBool(s string, defaultValue bool) bool {
	if b, err := strconv.ParseBool(s); err == nil {
		return b
	}
	return defaultValue
}

// IntToString 整数转字符串
func IntToString(i int) string {
	return strconv.Itoa(i)
}

// Int64ToString int64转字符串
func Int64ToString(i int64) string {
	return strconv.FormatInt(i, 10)
}

// BoolToString 布尔值转字符串
func BoolToString(b bool) string {
	return strconv.FormatBool(b)
}

// SliceUtils 切片工具函数

// RemoveDuplicateStrings 去除字符串切片中的重复项
func RemoveDuplicateStrings(slice []string) []string {
	keys := make(map[string]bool)
	result := make([]string, 0, len(slice))
	
	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}
	
	return result
}

// FilterEmptyStrings 过滤空字符串
func FilterEmptyStrings(slice []string) []string {
	result := make([]string, 0, len(slice))
	for _, item := range slice {
		if strings.TrimSpace(item) != "" {
			result = append(result, strings.TrimSpace(item))
		}
	}
	return result
}

// ChunkSlice 将切片分块
func ChunkSlice(slice []string, chunkSize int) [][]string {
	if chunkSize <= 0 {
		return [][]string{slice}
	}
	
	var chunks [][]string
	for i := 0; i < len(slice); i += chunkSize {
		end := i + chunkSize
		if end > len(slice) {
			end = len(slice)
		}
		chunks = append(chunks, slice[i:end])
	}
	
	return chunks
}
