package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"gopkg.in/yaml.v3"
)

// Config 应用配置
type Config struct {
	Server   ServerConfig   `yaml:"server"`
	Database DatabaseConfig `yaml:"database"`
	Log      LogConfig      `yaml:"log"`
	Security SecurityConfig `yaml:"security"`
	Push     PushConfig     `yaml:"push"`
	Crawler  CrawlerConfig  `yaml:"crawler"`
	RSS      RSSConfig      `yaml:"rss"`
	TJUN     TJUNConfig     `yaml:"tjun"`
	Weibu    WeibuConfig    `yaml:"weibu"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string `yaml:"host"`
	Port         int    `yaml:"port"`
	Mode         string `yaml:"mode"`         // debug, release, test
	ReadTimeout  int    `yaml:"readTimeout"` // 秒
	WriteTimeout int    `yaml:"writeTimeout"` // 秒
	MaxBodySize  int64  `yaml:"maxBodySize"`  // 字节
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type   string      `yaml:"type"`   // mysql, sqlite
	MySQL  MySQLConfig `yaml:"mysql"`
	SQLite SQLiteConfig `yaml:"sqlite"`
	Migration MigrationConfig `yaml:"migration"`
}

// MySQLConfig MySQL配置
type MySQLConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	Database string `yaml:"database"`
	Charset  string `yaml:"charset"`
	Loc      string `yaml:"loc"`
}

// SQLiteConfig SQLite配置
type SQLiteConfig struct {
	File   string `yaml:"file"`
	Memory bool   `yaml:"memory"`
}

// MigrationConfig 迁移配置
type MigrationConfig struct {
	AutoBackupTables bool `yaml:"autoBackupTables"`
	DropLegacyTables bool `yaml:"dropLegacyTables"`
	DetailedLogs     bool `yaml:"detailedLogs"`
	MigrationTimeout int  `yaml:"migrationTimeout"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level    string `yaml:"level"`    // debug, info, warn, error, fatal
	File     string `yaml:"file"`     // 日志文件路径
	Console  bool   `yaml:"console"`  // 是否输出到控制台
	ShowFile bool   `yaml:"showFile"` // 是否显示文件名和行号
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	JWTSecret     string `yaml:"jwtSecret"`
	JWTExpireHour int64  `yaml:"jwtExpireHour"`
	APIKeyLength  int    `yaml:"apiKeyLength"`
	PasswordSalt  string `yaml:"passwordSalt"`
}

// PushConfig 推送配置
type PushConfig struct {
	Enabled     bool              `yaml:"enabled"`
	Channels    []PushChannelConfig `yaml:"channels"`
	DefaultPolicy string          `yaml:"defaultPolicy"`
}

// PushChannelConfig 推送通道配置
type PushChannelConfig struct {
	Name   string                 `yaml:"name"`
	Type   string                 `yaml:"type"`
	Config map[string]interface{} `yaml:"config"`
}

// CrawlerConfig 采集器配置
type CrawlerConfig struct {
	Enabled         bool `yaml:"enabled"`
	DefaultInterval int  `yaml:"defaultInterval"` // 默认采集间隔（秒）
	MaxConcurrent   int  `yaml:"maxConcurrent"`   // 最大并发数
	Timeout         int  `yaml:"timeout"`         // 超时时间（秒）
}

// RSSConfig RSS配置
type RSSConfig struct {
	Enabled     bool   `yaml:"enabled"`
	Title       string `yaml:"title"`
	Description string `yaml:"description"`
	ItemCount   int    `yaml:"itemCount"`
}

// TJUNConfig 天际友盟配置
type TJUNConfig struct {
	Enabled      bool   `yaml:"enabled"`      // 是否启用天际友盟IOC查询
	Host         string `yaml:"host"`         // API域名
	AppKey       string `yaml:"appKey"`       // APP KEY
	AppSecret    string `yaml:"appSecret"`    // APP密钥
	Token        string `yaml:"token"`        // 用户身份标识
	Timeout      int    `yaml:"timeout"`      // 请求超时时间（秒）
	MaxBatchSize int    `yaml:"maxBatchSize"` // 批量查询最大数量
	RetryCount   int    `yaml:"retryCount"`   // 重试次数
}

// WeibuConfig 微步威胁情报配置
type WeibuConfig struct {
	Enabled      bool   `yaml:"enabled"`      // 是否启用微步威胁情报查询
	Host         string `yaml:"host"`         // API域名
	ApiKey       string `yaml:"apiKey"`       // API密钥
	Timeout      int    `yaml:"timeout"`      // 请求超时时间（秒）
	MaxBatchSize int    `yaml:"maxBatchSize"` // 批量查询最大数量
	RetryCount   int    `yaml:"retryCount"`   // 重试次数
	Lang         string `yaml:"lang"`         // 返回语言
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		if err := createDefaultConfig(configPath); err != nil {
			return nil, fmt.Errorf("创建默认配置文件失败: %w", err)
		}
	}
	
	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}
	
	// 解析配置
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}
	
	// 应用环境变量覆盖
	applyEnvOverrides(&config)
	
	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}
	
	// 设置默认值
	setDefaults(&config)
	
	return &config, nil
}

// createDefaultConfig 创建默认配置文件
func createDefaultConfig(configPath string) error {
	config := getDefaultConfig()
	
	// 确保配置目录存在
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}
	
	// 序列化配置
	data, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化默认配置失败: %w", err)
	}
	
	// 写入文件
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}
	
	return nil
}

// getDefaultConfig 获取默认配置
func getDefaultConfig() *Config {
	return &Config{
		Server: ServerConfig{
			Host:         "0.0.0.0",
			Port:         8080,
			Mode:         "release",
			ReadTimeout:  30,
			WriteTimeout: 30,
			MaxBodySize:  32 << 20, // 32MB
		},
		Database: DatabaseConfig{
			Type: "sqlite",
			MySQL: MySQLConfig{
				Host:     "localhost",
				Port:     3306,
				User:     "root",
				Password: "",
				Database: "vuln_push",
				Charset:  "utf8mb4",
				Loc:      "Local",
			},
			SQLite: SQLiteConfig{
				File:   "./data/database.db",
				Memory: false,
			},
			Migration: MigrationConfig{
				AutoBackupTables: true,
				DropLegacyTables: false,
				DetailedLogs:     true,
				MigrationTimeout: 300,
			},
		},
		Log: LogConfig{
			Level:    "info",
			File:     "./logs/app.log",
			Console:  true,
			ShowFile: true,
		},
		Security: SecurityConfig{
			JWTSecret:     "your-jwt-secret-key",
			JWTExpireHour: 24,
			APIKeyLength:  32,
			PasswordSalt:  "your-password-salt",
		},
		Push: PushConfig{
			Enabled:       true,
			Channels:      []PushChannelConfig{},
			DefaultPolicy: "default",
		},
		Crawler: CrawlerConfig{
			Enabled:         true,
			DefaultInterval: 3600,
			MaxConcurrent:   5,
			Timeout:         30,
		},
		RSS: RSSConfig{
			Enabled:     true,
			Title:       "漏洞情报管理平台",
			Description: "最新漏洞信息订阅",
			ItemCount:   50,
		},
	}
}

// applyEnvOverrides 应用环境变量覆盖
func applyEnvOverrides(config *Config) {
	// 服务器配置
	if host := os.Getenv("SERVER_HOST"); host != "" {
		config.Server.Host = host
	}
	if port := os.Getenv("SERVER_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Server.Port = p
		}
	}
	if mode := os.Getenv("SERVER_MODE"); mode != "" {
		config.Server.Mode = mode
	}
	
	// 数据库配置
	if dbType := os.Getenv("DB_TYPE"); dbType != "" {
		config.Database.Type = dbType
	}
	if dbHost := os.Getenv("DB_HOST"); dbHost != "" {
		config.Database.MySQL.Host = dbHost
	}
	if dbPort := os.Getenv("DB_PORT"); dbPort != "" {
		if p, err := strconv.Atoi(dbPort); err == nil {
			config.Database.MySQL.Port = p
		}
	}
	if dbUser := os.Getenv("DB_USER"); dbUser != "" {
		config.Database.MySQL.User = dbUser
	}
	if dbPassword := os.Getenv("DB_PASSWORD"); dbPassword != "" {
		config.Database.MySQL.Password = dbPassword
	}
	if dbName := os.Getenv("DB_NAME"); dbName != "" {
		config.Database.MySQL.Database = dbName
	}
	if dbFile := os.Getenv("DB_FILE"); dbFile != "" {
		config.Database.SQLite.File = dbFile
	}
	
	// 日志配置
	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		config.Log.Level = logLevel
	}
	if logFile := os.Getenv("LOG_FILE"); logFile != "" {
		config.Log.File = logFile
	}
	
	// 安全配置
	if jwtSecret := os.Getenv("JWT_SECRET"); jwtSecret != "" {
		config.Security.JWTSecret = jwtSecret
	}
	if jwtExpire := os.Getenv("JWT_EXPIRE_HOUR"); jwtExpire != "" {
		if h, err := strconv.ParseInt(jwtExpire, 10, 64); err == nil {
			config.Security.JWTExpireHour = h
		}
	}
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	// 验证服务器配置
	if config.Server.Port < 1 || config.Server.Port > 65535 {
		return fmt.Errorf("服务器端口必须在1-65535之间")
	}
	
	if config.Server.Mode != "debug" && config.Server.Mode != "release" && config.Server.Mode != "test" {
		return fmt.Errorf("服务器模式必须是debug、release或test")
	}
	
	// 验证数据库配置
	if config.Database.Type != "mysql" && config.Database.Type != "sqlite" {
		return fmt.Errorf("数据库类型必须是mysql或sqlite")
	}
	
	if config.Database.Type == "mysql" {
		if config.Database.MySQL.Host == "" {
			return fmt.Errorf("MySQL主机地址不能为空")
		}
		if config.Database.MySQL.User == "" {
			return fmt.Errorf("MySQL用户名不能为空")
		}
		if config.Database.MySQL.Database == "" {
			return fmt.Errorf("MySQL数据库名不能为空")
		}
	}
	
	if config.Database.Type == "sqlite" && !config.Database.SQLite.Memory {
		if config.Database.SQLite.File == "" {
			return fmt.Errorf("SQLite数据库文件路径不能为空")
		}
	}
	
	// 验证日志配置
	validLogLevels := []string{"debug", "info", "warn", "error", "fatal"}
	if !contains(validLogLevels, strings.ToLower(config.Log.Level)) {
		return fmt.Errorf("日志级别必须是debug、info、warn、error或fatal")
	}
	
	// 验证安全配置
	if config.Security.JWTSecret == "" || config.Security.JWTSecret == "your-jwt-secret-key" {
		return fmt.Errorf("JWT密钥不能为空或使用默认值")
	}
	
	if config.Security.JWTExpireHour < 1 {
		return fmt.Errorf("JWT过期时间必须大于0小时")
	}
	
	return nil
}

// setDefaults 设置默认值
func setDefaults(config *Config) {
	// 设置服务器默认值
	if config.Server.ReadTimeout == 0 {
		config.Server.ReadTimeout = 30
	}
	if config.Server.WriteTimeout == 0 {
		config.Server.WriteTimeout = 30
	}
	if config.Server.MaxBodySize == 0 {
		config.Server.MaxBodySize = 32 << 20 // 32MB
	}
	
	// 设置数据库默认值
	if config.Database.MySQL.Port == 0 {
		config.Database.MySQL.Port = 3306
	}
	if config.Database.MySQL.Charset == "" {
		config.Database.MySQL.Charset = "utf8mb4"
	}
	if config.Database.MySQL.Loc == "" {
		config.Database.MySQL.Loc = "Local"
	}
	
	// 设置迁移默认值
	if config.Database.Migration.MigrationTimeout == 0 {
		config.Database.Migration.MigrationTimeout = 300
	}
	
	// 设置安全默认值
	if config.Security.APIKeyLength == 0 {
		config.Security.APIKeyLength = 32
	}
	
	// 设置采集器默认值
	if config.Crawler.DefaultInterval == 0 {
		config.Crawler.DefaultInterval = 3600
	}
	if config.Crawler.MaxConcurrent == 0 {
		config.Crawler.MaxConcurrent = 5
	}
	if config.Crawler.Timeout == 0 {
		config.Crawler.Timeout = 30
	}
	
	// 设置RSS默认值
	if config.RSS.ItemCount == 0 {
		config.RSS.ItemCount = 50
	}
}

// contains 检查切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// GetAddress 获取服务器地址
func (c *Config) GetAddress() string {
	return fmt.Sprintf("%s:%d", c.Server.Host, c.Server.Port)
}

// IsDebugMode 检查是否为调试模式
func (c *Config) IsDebugMode() bool {
	return c.Server.Mode == "debug"
}

// IsReleaseMode 检查是否为发布模式
func (c *Config) IsReleaseMode() bool {
	return c.Server.Mode == "release"
}

// IsTestMode 检查是否为测试模式
func (c *Config) IsTestMode() bool {
	return c.Server.Mode == "test"
}
