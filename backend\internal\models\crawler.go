package models

import (
	"encoding/json"
	"time"
)

// 采集间隔类型常量
const (
	IntervalCustom  = "custom"  // 自定义间隔
	IntervalHourly  = "hourly"  // 每小时
	IntervalDaily   = "daily"   // 每天
	IntervalWeekly  = "weekly"  // 每周
	IntervalMonthly = "monthly" // 每月
	IntervalManual  = "manual"  // 手动
)

// Crawler 采集器模型
type Crawler struct {
	ID          uint   `gorm:"primaryKey" json:"id"`
	Name        string `gorm:"size:100;not null" json:"name"`
	Type        string `gorm:"size:50;not null;index" json:"type"`
	Description string `gorm:"size:500" json:"description"`
	Config      string `gorm:"type:text" json:"-"` // JSON格式配置
	Interval    string `gorm:"size:20;not null;default:'manual'" json:"interval"` // 采集间隔类型
	Status      bool   `gorm:"default:true" json:"status"`
	LastRunAt   int64  `gorm:"default:0" json:"lastRunAt"`
	NextRunAt   int64  `gorm:"default:0" json:"nextRunAt"` // 下次运行时间
	CreatedAt   int64  `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt   int64  `gorm:"autoUpdateTime" json:"updatedAt"`

	// 关联关系
	Logs []CrawlerLog `gorm:"foreignKey:CrawlerID;constraint:OnDelete:CASCADE" json:"logs,omitempty"`
}

// TableName 指定表名
func (Crawler) TableName() string {
	return "crawlers"
}

// GetConfig 获取配置
func (c *Crawler) GetConfig() map[string]interface{} {
	var config map[string]interface{}
	if c.Config == "" {
		return make(map[string]interface{})
	}
	
	if err := json.Unmarshal([]byte(c.Config), &config); err != nil {
		return make(map[string]interface{})
	}
	
	return config
}

// SetConfig 设置配置
func (c *Crawler) SetConfig(config map[string]interface{}) error {
	data, err := json.Marshal(config)
	if err != nil {
		return err
	}
	c.Config = string(data)
	return nil
}

// IsActive 检查是否激活
func (c *Crawler) IsActive() bool {
	return c.Status
}

// GetLastRunTime 获取最后运行时间
func (c *Crawler) GetLastRunTime() time.Time {
	if c.LastRunAt == 0 {
		return time.Time{}
	}
	return time.Unix(c.LastRunAt, 0)
}

// UpdateLastRunTime 更新最后运行时间
func (c *Crawler) UpdateLastRunTime() {
	c.LastRunAt = time.Now().Unix()
}

// UpdateNextRunTime 更新下次运行时间
func (c *Crawler) UpdateNextRunTime() {
	now := time.Now()

	switch c.Interval {
	case IntervalCustom:
		// 自定义间隔，从配置中获取
		config := c.GetConfig()
		if customInterval, ok := config["customInterval"].(float64); ok && customInterval > 0 {
			c.NextRunAt = now.Add(time.Duration(customInterval) * time.Minute).Unix()
		} else {
			// 默认1小时
			c.NextRunAt = now.Add(1 * time.Hour).Unix()
		}
	case IntervalHourly:
		c.NextRunAt = now.Add(1 * time.Hour).Unix()
	case IntervalDaily:
		c.NextRunAt = time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location()).
			Add(24 * time.Hour).Unix()
	case IntervalWeekly:
		c.NextRunAt = time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location()).
			Add(7 * 24 * time.Hour).Unix()
	case IntervalMonthly:
		// 计算下个月同一天
		nextMonth := now.Month() + 1
		year := now.Year()
		if nextMonth > 12 {
			nextMonth = 1
			year++
		}
		c.NextRunAt = time.Date(year, nextMonth, now.Day(), now.Hour(), 0, 0, 0, now.Location()).Unix()
	case IntervalManual:
		// 手动模式不设置下次运行时间
		c.NextRunAt = 0
	default:
		// 默认每小时
		c.NextRunAt = now.Add(1 * time.Hour).Unix()
	}
}

// GetNextRunTime 获取下次运行时间
func (c *Crawler) GetNextRunTime() time.Time {
	if c.NextRunAt == 0 {
		return time.Time{}
	}
	return time.Unix(c.NextRunAt, 0)
}

// GetCreatedTime 获取创建时间
func (c *Crawler) GetCreatedTime() time.Time {
	return time.Unix(c.CreatedAt, 0)
}

// GetUpdatedTime 获取更新时间
func (c *Crawler) GetUpdatedTime() time.Time {
	return time.Unix(c.UpdatedAt, 0)
}

// CrawlerLog 采集器日志模型
type CrawlerLog struct {
	ID        uint   `gorm:"primaryKey" json:"id"`
	CrawlerID uint   `gorm:"not null;index" json:"crawlerId"`
	Status    string `gorm:"size:20;not null;index" json:"status"` // success, failed, running
	Count     int    `gorm:"default:0" json:"count"`
	Message   string `gorm:"type:text" json:"message"`
	StartedAt int64  `gorm:"default:0" json:"startedAt"`
	EndedAt   int64  `gorm:"default:0" json:"endedAt"`
	Duration  int    `gorm:"default:0" json:"duration"` // 执行时长（秒）
	CreatedAt int64  `gorm:"autoCreateTime" json:"createdAt"`
	
	// 关联关系
	Crawler Crawler `gorm:"foreignKey:CrawlerID" json:"crawler,omitempty"`
}

// TableName 指定表名
func (CrawlerLog) TableName() string {
	return "crawler_logs"
}

// IsSuccess 检查是否成功
func (cl *CrawlerLog) IsSuccess() bool {
	return cl.Status == "success"
}

// IsFailed 检查是否失败
func (cl *CrawlerLog) IsFailed() bool {
	return cl.Status == "failed"
}

// IsRunning 检查是否正在运行
func (cl *CrawlerLog) IsRunning() bool {
	return cl.Status == "running"
}

// GetStartedTime 获取开始时间
func (cl *CrawlerLog) GetStartedTime() time.Time {
	return time.Unix(cl.StartedAt, 0)
}

// GetEndedTime 获取结束时间
func (cl *CrawlerLog) GetEndedTime() time.Time {
	if cl.EndedAt == 0 {
		return time.Time{}
	}
	return time.Unix(cl.EndedAt, 0)
}

// MarkAsStarted 标记为开始
func (cl *CrawlerLog) MarkAsStarted() {
	cl.Status = "running"
	cl.StartedAt = time.Now().Unix()
}

// MarkAsSuccess 标记为成功
func (cl *CrawlerLog) MarkAsSuccess(count int, message string) {
	now := time.Now().Unix()
	cl.Status = "success"
	cl.Count = count
	cl.Message = message
	cl.EndedAt = now
	if cl.StartedAt > 0 {
		cl.Duration = int(now - cl.StartedAt)
	}
}

// MarkAsFailed 标记为失败
func (cl *CrawlerLog) MarkAsFailed(message string) {
	now := time.Now().Unix()
	cl.Status = "failed"
	cl.Message = message
	cl.EndedAt = now
	if cl.StartedAt > 0 {
		cl.Duration = int(now - cl.StartedAt)
	}
}

// GetCreatedTime 获取创建时间
func (cl *CrawlerLog) GetCreatedTime() time.Time {
	return time.Unix(cl.CreatedAt, 0)
}

// CrawlerType 采集器类型常量
const (
	CrawlerTypeChaitin    = "chaitin"
	CrawlerTypeQianxin    = "qianxin"
	CrawlerTypeAliyun     = "aliyun"
	CrawlerTypeThreatbook = "threatbook"
	CrawlerTypeSeebug     = "seebug"
	CrawlerTypeVenustech  = "venustech"
	CrawlerTypeOSCS       = "oscs"
	CrawlerTypeNVD        = "nvd"
)

// GetAllCrawlerTypes 获取所有采集器类型
func GetAllCrawlerTypes() []string {
	return []string{
		CrawlerTypeChaitin,
		CrawlerTypeQianxin,
		CrawlerTypeAliyun,
		CrawlerTypeThreatbook,
		CrawlerTypeSeebug,
		CrawlerTypeVenustech,
		CrawlerTypeOSCS,
		CrawlerTypeNVD,
	}
}

// IsValidCrawlerType 检查采集器类型是否有效
func IsValidCrawlerType(crawlerType string) bool {
	validTypes := GetAllCrawlerTypes()
	for _, t := range validTypes {
		if t == crawlerType {
			return true
		}
	}
	return false
}

// CrawlerStatus 采集器状态常量
const (
	CrawlerStatusSuccess = "success"
	CrawlerStatusFailed  = "failed"
	CrawlerStatusRunning = "running"
)

// GetAllCrawlerStatuses 获取所有采集器状态
func GetAllCrawlerStatuses() []string {
	return []string{
		CrawlerStatusSuccess,
		CrawlerStatusFailed,
		CrawlerStatusRunning,
	}
}

// IsValidCrawlerStatus 检查采集器状态是否有效
func IsValidCrawlerStatus(status string) bool {
	validStatuses := GetAllCrawlerStatuses()
	for _, s := range validStatuses {
		if s == status {
			return true
		}
	}
	return false
}
