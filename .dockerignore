# Git
.git/
.gitignore
**/*.md
LICENSE

# Node.js
frontend/node_modules/
**/.npm/
**/.config/
**/.cache/
**/node_modules/
**/bower_components/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-store/
**/.pnp/
**/.pnp.js
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# 前端构建缓存
frontend/.vite/
frontend/.cache/
frontend/.temp/
frontend/.eslintcache
frontend/coverage/
frontend/.parcel-cache/
frontend/dist/
frontend/.build/
**/*.tsbuildinfo
**/.next/
**/.nuxt/
**/.output/
**/storybook-static/

# Go
backend/vulnerability_push_server
backend/*.db
backend/*.db-journal
backend/tmp/
backend/.build/
backend/bin/
backend/vendor/
**/.go/
**/go-build/
**/.glide/

# 数据文件
*.db
*.db-journal
*.sqlite
*.sqlite3
data/
.buildcache/
**/__pycache__/
**/*.py[cod]
**/*$py.class

# 日志文件
*.log
logs/
**/log/

# 测试文件
**/coverage/
**/test/
**/*.test.*
**/.nyc_output
**/__tests__/
**/__snapshots__/
**/*.spec.*

# Docker
.docker/
.dockerignore

# 系统文件
.DS_Store
**/.DS_Store
Thumbs.db
**/Thumbs.db
**/*.swp
**/*.swo
*~
**/*~

# IDE配置
**/.idea/
**/.vscode/
**/.vs/
**/.eclipse/
**/.settings/
**/.project
**/.classpath
**/.factorypath
**/.editorconfig

# 临时文件和构建产物
**/dist/
**/build/
**/out/
**/.output/
**/tmp/
**/temp/
**/.temp/
**/.tmp/ 