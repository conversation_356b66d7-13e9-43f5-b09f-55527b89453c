<template>
  <div class="test-page">
    <h1>测试页面</h1>
    <p>这是一个测试页面，用于验证路由系统是否正常工作</p>
    <p>时间戳: {{ timestamp }}</p>
    <el-button @click="goHome" type="primary">返回首页</el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const timestamp = ref(new Date().getTime())

const goHome = () => {
  router.push('/home')
}

onMounted(() => {
  console.log('TestPage组件已挂载', timestamp.value)
})
</script>

<style scoped>
.test-page {
  padding: 20px;
  text-align: center;
}
</style> 