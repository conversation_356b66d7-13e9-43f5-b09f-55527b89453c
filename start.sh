#!/bin/bash

# 威胁情报管理平台启动脚本
echo "==== 威胁情报管理平台启动脚本 ===="

# 检查是否安装了Go
if ! command -v go &> /dev/null; then
    echo "错误: 未安装Go语言环境。请安装Go 1.16或更高版本。"
    exit 1
fi

# 检查Go版本
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
echo "检测到Go版本: $GO_VERSION"

# 确保当前目录是项目根目录
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo "错误: 请在项目根目录运行此脚本。"
    exit 1
fi

# 检查并安装后端依赖
echo "正在检查并安装后端依赖..."
cd backend
go mod tidy
if [ $? -ne 0 ]; then
    echo "错误: 安装后端依赖失败。"
    exit 1
fi

# 构建后端
echo "正在构建后端..."
go build -o vulnerability_push_server .
if [ $? -ne 0 ]; then
    echo "错误: 构建后端失败。"
    exit 1
fi

# 检查前端依赖安装
echo "正在检查前端环境..."
cd ../frontend
if [ ! -d "node_modules" ]; then
    echo "正在安装前端依赖(这可能需要一些时间)..."
    if command -v npm &> /dev/null; then
        npm install
    elif command -v yarn &> /dev/null; then
        yarn install
    else
        echo "警告: 未找到npm或yarn，无法安装前端依赖。请手动安装前端依赖。"
    fi
fi

# 启动后端服务
echo "正在启动后端服务..."
cd ../backend
./vulnerability_push_server &
BACKEND_PID=$!
echo "后端服务已启动，PID: $BACKEND_PID"

# 启动前端开发服务器(如果需要)
echo "前端部署说明:"
echo "1. 开发模式: 在frontend目录中运行 'npm run serve' 或 'yarn serve'"
echo "2. 生产模式: 在frontend目录中运行 'npm run build' 或 'yarn build'，然后将dist目录部署到Web服务器"

echo "系统初始化完成!"
echo "- 后端API地址: http://localhost:8080"
echo "- 默认管理员用户名: admin (首次启动时会在控制台显示初始密码)"
echo "- 请查看后端日志获取更多信息"

# 输出日志
echo "按Ctrl+C停止服务..."
wait $BACKEND_PID 