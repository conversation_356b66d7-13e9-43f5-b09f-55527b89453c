package push

import (
	"fmt"
	"github.com/CatchZeng/dingtalk/pkg/dingtalk"
	"strings"
	"time"
)

// 钉钉推送类型常量
const TypeDingDing = "dingding"

// DingDingConfig 钉钉机器人配置
type DingDingConfig struct {
	AccessToken string `yaml:"access_token" json:"access_token"`
	SignSecret  string `yaml:"sign_secret" json:"sign_secret"`
}

// 推送到钉钉机器人
func PushToDingDing(vuln *Vulnerability, channel *PushChannel, record *PushRecord) error {
	// 获取通道配置
	config := channel.GetConfig()
	if config == nil {
		return fmt.Errorf("通道配置为空")
	}
	
	// 检查access_token和sign_secret是否存在
	accessToken, ok := config["access_token"].(string)
	if !ok || accessToken == "" {
		return fmt.Errorf("AccessToken未配置或格式不正确")
	}
	
	signSecret, _ := config["sign_secret"].(string)
	
	// 创建钉钉客户端
	client := dingtalk.NewClient(accessToken, signSecret)

	// 构建markdown消息
	severityEmoji := "⚠️ 警告级"
	switch vuln.Severity {
	case "严重":
		severityEmoji = "🚨 严重级"
	case "高危":
		severityEmoji = "🔥 高危级"
	case "中危":
		severityEmoji = "⚠️ 中危级"
	case "低危":
		severityEmoji = "💡 低危级"
	case "信息":
		severityEmoji = "ℹ️ 信息级"
	}

	title := fmt.Sprintf("%s漏洞告警: %s", severityEmoji, vuln.Name)

	content := fmt.Sprintf("## %s漏洞告警\n", severityEmoji)
	content += fmt.Sprintf("### 漏洞名称: %s\n", vuln.Name)
	content += fmt.Sprintf("### 漏洞编号: %s\n", vuln.VulnID)
	content += fmt.Sprintf("### 披露日期: %s\n", vuln.DisclosureDate)

	// 安全处理源URL，避免特殊字符问题
	if vuln.Source != "" {
		source := vuln.Source
		// 如果源URL过长，进行截断
		if len([]rune(source)) > 100 {
			runeSource := []rune(source)
			source = string(runeSource[:100]) + "..."
		}
		content += fmt.Sprintf("### 信息来源: %s\n", source)
	}

	if vuln.Description != "" {
		if len(vuln.Description) > 200 {
			// 处理长描述，避免乱码
			desc := []rune(vuln.Description)
			if len(desc) > 200 {
				desc = desc[:200]
			}
			content += fmt.Sprintf("### 漏洞描述\n%s...\n", string(desc))
		} else {
			content += fmt.Sprintf("### 漏洞描述\n%s\n", vuln.Description)
		}
	}

	if vuln.PushReason != "" {
		// 安全处理推送原因
		reason := vuln.PushReason
		if len([]rune(reason)) > 100 {
			runeReason := []rune(reason)
			reason = string(runeReason[:100]) + "..."
		}
		content += fmt.Sprintf("### 推送原因\n%s\n", reason)
	}

	if vuln.Remediation != "" {
		// 安全处理修复建议
		remediation := vuln.Remediation
		if len([]rune(remediation)) > 200 {
			runeRemediation := []rune(remediation)
			remediation = string(runeRemediation[:200]) + "..."
		}
		content += fmt.Sprintf("### 修复建议\n%s\n", remediation)
	}

	// 添加参考链接
	references := vuln.GetReferences()
	if len(references) > 0 {
		content += "### 参考链接\n"
		// 最多只显示3个参考链接，避免消息过长
		maxLinks := 3
		if len(references) > maxLinks {
			references = references[:maxLinks]
		}

		for _, ref := range references {
			// 处理过长的链接
			linkText := ref
			if len([]rune(linkText)) > 80 {
				runeLink := []rune(linkText)
				linkText = string(runeLink[:80]) + "..."
			}
			content += fmt.Sprintf("%s\n", linkText)
		}

		// 如果还有更多链接，添加提示
		if len(vuln.GetReferences()) > maxLinks {
			content += "(更多链接省略...)\n"
		}
	}

	// 添加时间戳
	content += fmt.Sprintf("\n> 推送时间: %s", time.Now().Format("2006-01-02 15:04:05"))

	// 特殊处理一下空行
	content = strings.ReplaceAll(content, "\n\n", "\n\n&nbsp;\n")
	
	// 创建并发送markdown消息
	msg := dingtalk.NewMarkdownMessage().SetMarkdown(title, content)
	_, resp, err := client.Send(msg)
	if err != nil {
		return fmt.Errorf("failed to send dingding markdown, %s %d %s", resp.ErrMsg, resp.ErrCode, err)
	}
	
	// 检查响应
	if resp.ErrCode != 0 {
		return fmt.Errorf("钉钉推送失败: %s (错误码: %d)", resp.ErrMsg, resp.ErrCode)
	}

	return nil
}

// 推送IOC情报到钉钉机器人
func PushIOCIntelligenceToDingDing(ioc *IOCIntelligence, channel *PushChannel, record *IOCIntelligencePushRecord) error {
	// 获取通道配置
	config := channel.GetConfig()
	if config == nil {
		return fmt.Errorf("通道配置为空")
	}

	// 检查access_token和sign_secret是否存在
	accessToken, ok := config["access_token"].(string)
	if !ok || accessToken == "" {
		return fmt.Errorf("AccessToken未配置或格式不正确")
	}

	signSecret, _ := config["sign_secret"].(string)

	// 创建钉钉客户端
	client := dingtalk.NewClient(accessToken, signSecret)

	// 构建markdown消息
	riskEmoji := "⚠️ 警告级"
	switch ioc.RiskLevel {
	case "严重":
		riskEmoji = "🚨 严重级"
	case "高危":
		riskEmoji = "🔥 高危级"
	case "中危":
		riskEmoji = "⚠️ 中危级"
	case "低危":
		riskEmoji = "💡 低危级"
	case "信息":
		riskEmoji = "ℹ️ 信息级"
	}

	title := fmt.Sprintf("%s IOC情报告警", riskEmoji)

	content := fmt.Sprintf("## %s\n\n", title)
	content += fmt.Sprintf("**IOC值**: %s\n\n", ioc.IOC)
	content += fmt.Sprintf("**IOC类型**: %s\n\n", strings.ToUpper(ioc.IOCType))

	if ioc.Location != "" {
		content += fmt.Sprintf("**地理位置**: %s\n\n", ioc.Location)
	}

	if ioc.Type != "" {
		content += fmt.Sprintf("**威胁类型**: %s\n\n", ioc.Type)
	}

	if ioc.HitCount > 0 {
		content += fmt.Sprintf("**命中次数**: %d\n\n", ioc.HitCount)
	}

	// 移除目标组织字段，已替换为推送状态

	if ioc.Description != "" {
		desc := ioc.Description
		if len([]rune(desc)) > 200 {
			runeDesc := []rune(desc)
			desc = string(runeDesc[:200]) + "..."
		}
		content += fmt.Sprintf("**威胁描述**: %s\n\n", desc)
	}

	if ioc.Source != "" {
		source := ioc.Source
		if len([]rune(source)) > 100 {
			runeSource := []rune(source)
			source = string(runeSource[:100]) + "..."
		}
		content += fmt.Sprintf("**情报来源**: %s\n\n", source)
	}

	if len(ioc.Tags) > 0 {
		content += fmt.Sprintf("**标签**: %s\n\n", strings.Join(ioc.Tags, ", "))
	}

	if ioc.PushReason != "" {
		reason := ioc.PushReason
		if len([]rune(reason)) > 100 {
			runeReason := []rune(reason)
			reason = string(runeReason[:100]) + "..."
		}
		content += fmt.Sprintf("**推送原因**: %s\n\n", reason)
	}

	// 添加发现日期
	if ioc.DiscoveryDate != "" {
		content += fmt.Sprintf("**发现日期**: %s\n\n", ioc.DiscoveryDate)
	}

	// 添加时间戳
	content += fmt.Sprintf("**推送时间**: %s\n\n", time.Now().Format("2006-01-02 15:04:05"))

	// 特殊处理一下空行
	content = strings.ReplaceAll(content, "\n\n", "\n\n&nbsp;\n")

	// 创建并发送markdown消息
	msg := dingtalk.NewMarkdownMessage().SetMarkdown(title, content)
	_, resp, err := client.Send(msg)
	if err != nil {
		return fmt.Errorf("failed to send dingding markdown, %s %d %s", resp.ErrMsg, resp.ErrCode, err)
	}

	// 检查响应
	if resp.ErrCode != 0 {
		return fmt.Errorf("钉钉推送失败: %s (错误码: %d)", resp.ErrMsg, resp.ErrCode)
	}

	return nil
}

// 批量推送IOC情报到钉钉机器人
func BatchPushIOCIntelligenceToDingDing(iocIntels []*IOCIntelligence, channel *PushChannel) error {
	// 获取通道配置
	config := channel.GetConfig()
	if config == nil {
		return fmt.Errorf("通道配置为空")
	}

	// 检查access_token和sign_secret是否存在
	accessToken, ok := config["access_token"].(string)
	if !ok || accessToken == "" {
		return fmt.Errorf("AccessToken未配置或格式不正确")
	}

	signSecret, _ := config["sign_secret"].(string)

	// 创建钉钉客户端
	client := dingtalk.NewClient(accessToken, signSecret)

	// 构建批量推送内容
	title := fmt.Sprintf("IOC情报批量预警 (%d条)", len(iocIntels))
	content := buildBatchIOCPushContent(iocIntels)

	// 特殊处理一下空行
	content = strings.ReplaceAll(content, "\n\n", "\n\n&nbsp;\n")

	// 创建并发送markdown消息
	msg := dingtalk.NewMarkdownMessage().SetMarkdown(title, content)
	_, resp, err := client.Send(msg)
	if err != nil {
		return fmt.Errorf("failed to send dingding markdown, %s %d %s", resp.ErrMsg, resp.ErrCode, err)
	}

	// 检查响应
	if resp.ErrCode != 0 {
		return fmt.Errorf("钉钉推送失败: %s (错误码: %d)", resp.ErrMsg, resp.ErrCode)
	}

	return nil
}