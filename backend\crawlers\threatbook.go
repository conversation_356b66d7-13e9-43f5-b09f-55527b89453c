package crawlers

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/go-resty/resty/v2"
	"vulnerability_push/internal/utils"
)

// 微步威胁情报采集器
type ThreatbookCrawler struct {
	client *resty.Client
	debug  bool
}

// 创建微步威胁情报采集器
func NewThreatbookCrawler() Grabber {
	client := resty.New()
	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	client.SetHeader("Referer", "https://x.threatbook.com/")
	client.SetHeader("Origin", "https://x.threatbook.com")
	client.SetHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	client.SetHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	client.SetHeader("Accept-Encoding", "gzip, deflate, br")
	client.SetHeader("DNT", "1")
	client.SetHeader("Connection", "keep-alive")
	client.SetHeader("Upgrade-Insecure-Requests", "1")
	client.SetTimeout(30 * time.Second)

	return &ThreatbookCrawler{
		client: client,
	}
}

// NewThreatbookCrawlerWithDebug 创建带调试功能的微步威胁情报采集器
func NewThreatbookCrawlerWithDebug() *ThreatbookCrawler {
	client := resty.New()
	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	client.SetHeader("Referer", "https://x.threatbook.com/")
	client.SetHeader("Origin", "https://x.threatbook.com")
	client.SetHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	client.SetHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	client.SetHeader("Accept-Encoding", "gzip, deflate, br")
	client.SetHeader("DNT", "1")
	client.SetHeader("Connection", "keep-alive")
	client.SetHeader("Upgrade-Insecure-Requests", "1")
	client.SetTimeout(30 * time.Second)
	
	// 不启用详细HTTP请求和响应日志
	// client.SetDebug(true)

	return &ThreatbookCrawler{
		client: client,
		debug:  true,
	}
}

// 获取采集源信息
func (c *ThreatbookCrawler) ProviderInfo() *Provider {
	return &Provider{
		Name:        "threatbook",
		DisplayName: "微步威胁情报",
		Link:        "https://x.threatbook.com",
	}
}

// 获取最新漏洞信息
func (c *ThreatbookCrawler) GetUpdate(ctx context.Context, pageLimit int, startDate string, endDate string) ([]*VulnInfo, error) {
	var results []*VulnInfo

	utils.Infof("微步威胁情报采集器开始执行")

	// 微步在线的威胁情报通常在不同的页面
	urls := []string{
		"https://x.threatbook.com/v5/vulIntelligence",
		"https://x.threatbook.com/v5/article",
		"https://research.threatbook.com/",
	}

	for _, url := range urls {
		vulns, err := c.fetchPageVulnerabilities(url)
		if err != nil {
			utils.Infof("获取微步威胁情报页面失败 %s: %v", url, err)
			continue
		}

		results = append(results, vulns...)

		// 添加随机延迟避免被反爬虫
		delay := 3000 + time.Now().Nanosecond()%2000
		time.Sleep(time.Duration(delay) * time.Millisecond)
	}

	// 如果没有获取到数据，返回一些示例数据
	if len(results) == 0 {
		utils.Infof("未获取到微步威胁情报数据，返回空结果")
		return []*VulnInfo{}, nil
	} else {
		utils.Infof("成功获取微步威胁情报数据，共 %d 条", len(results))
	}

	return results, nil
}

// 获取最新漏洞信息，支持检查数据库中是否存在
func (c *ThreatbookCrawler) GetUpdateWithCheck(ctx context.Context, pageLimit int, startDate string, endDate string, checkExists CheckVulnExistsFunc) ([]*VulnInfo, error) {
	var results []*VulnInfo

	utils.Infof("微步威胁情报采集器开始执行")

	// 微步在线的威胁情报通常在不同的页面
	urls := []string{
		"https://x.threatbook.com/v5/vulIntelligence",
		"https://x.threatbook.com/v5/article",
		"https://research.threatbook.com/",
	}

	// 收集所有漏洞信息
	var allVulns []*VulnInfo
	for _, url := range urls {
		vulns, err := c.fetchPageVulnerabilities(url)
		if err != nil {
			utils.Infof("获取微步威胁情报页面失败 %s: %v", url, err)
			continue
		}

		allVulns = append(allVulns, vulns...)

		// 添加随机延迟避免被反爬虫
		delay := 3000 + time.Now().Nanosecond()%2000
		time.Sleep(time.Duration(delay) * time.Millisecond)
	}

	// 如果没有获取到数据，直接返回空结果
	if len(allVulns) == 0 {
		utils.Infof("未获取到微步威胁情报数据，返回空结果")
		return []*VulnInfo{}, nil
	}

	utils.Infof("成功获取微步威胁情报数据，共 %d 条", len(allVulns))

	// 筛选出不存在于数据库中的漏洞
	var existingCount int
	for _, vuln := range allVulns {
		if checkExists != nil && checkExists(vuln.UniqueKey) {
			existingCount++
			utils.Infof("漏洞已存在于数据库中，跳过: %s", vuln.Title)
			continue
		}
		results = append(results, vuln)
	}

	utils.Infof("已过滤掉 %d 条已存在的漏洞，剩余 %d 条", existingCount, len(results))

	// 如果没有新的漏洞，直接返回空结果
	if len(results) == 0 {
		utils.Infof("未获取到新的微步威胁情报数据，返回空结果")
		return []*VulnInfo{}, nil
	}

	utils.Infof("成功获取新的微步威胁情报数据，共 %d 条", len(results))
	return results, nil
}

// 获取单个页面的漏洞信息
func (c *ThreatbookCrawler) fetchPageVulnerabilities(url string) ([]*VulnInfo, error) {
	utils.Infof("正在获取微步威胁情报页面: %s", url)

	resp, err := c.client.R().
		SetContext(context.Background()).
		Get(url)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("状态码错误: %d", resp.StatusCode())
	}

	// 解析HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(resp.String()))
	if err != nil {
		return nil, fmt.Errorf("解析HTML失败: %v", err)
	}

	var vulns []*VulnInfo

	// 尝试多种选择器来匹配微步在线的页面结构
	selectors := []string{
		".vuln-item",
		".intelligence-item",
		".vulnerability-card",
		".threat-item",
		".research-item",
		"article",
		".news-item",
		".list-item",
	}

	for _, selector := range selectors {
		items := doc.Find(selector)
		if items.Length() > 0 {
			utils.Infof("使用选择器 %s 找到 %d 个威胁情报项目", selector, items.Length())

			items.Each(func(i int, s *goquery.Selection) {
				if i >= 15 { // 限制数量
					return
				}

				if vuln := c.parseVulnerabilityItem(s); vuln != nil {
					vulns = append(vulns, vuln)
				}
			})

			if len(vulns) > 0 {
				break
			}
		}
	}

	return vulns, nil
}

// 解析漏洞项目
func (c *ThreatbookCrawler) parseVulnerabilityItem(s *goquery.Selection) *VulnInfo {
	// 提取标题
	title := c.extractTextBySelectors(s, []string{
		"h3", "h4", ".title", ".name", ".vuln-title", ".intelligence-title", "a[href*='vuln']", "a",
	})

	if title == "" || len(title) < 10 || !c.isVulnerabilityRelated(title) {
		return nil
	}

	// 提取描述
	description := c.extractTextBySelectors(s, []string{
		".description", ".desc", ".content", ".summary", ".abstract", "p",
	})
	if description == "" {
		description = "暂无描述"
	}

	// 提取CVE ID
	cveID := c.extractCVEID(title, description)

	// 提取严重程度
	severity := c.extractSeverity(s.Text())

	// 提取标签
	tags := []string{"微步威胁情报"}

	// 提取链接
	var link string
	if href, exists := s.Find("a").First().Attr("href"); exists {
		if strings.HasPrefix(href, "http") {
			link = href
		} else if strings.HasPrefix(href, "/") {
			link = "https://x.threatbook.com" + href
		} else {
			link = "https://x.threatbook.com/" + href
		}
	} else {
		link = "https://x.threatbook.com/v5/vulIntelligence"
	}

	// 创建漏洞信息
	info := &VulnInfo{
		UniqueKey:   fmt.Sprintf("TB-%s", time.Now().Format("20060102-150405")),
		Title:       title,
		Description: description,
		Severity:    severity,
		CVE:         cveID,
		Disclosure:  time.Now().Format("2006-01-02"),
		References:  []string{link},
		From:        link,
		Tags:        tags,
	}

	return info
}

// 提取文本
func (c *ThreatbookCrawler) extractTextBySelectors(s *goquery.Selection, selectors []string) string {
	for _, selector := range selectors {
		text := strings.TrimSpace(s.Find(selector).First().Text())
		if text != "" && len(text) > 5 {
			return text
		}
	}
	return ""
}

// 判断是否与漏洞相关
func (c *ThreatbookCrawler) isVulnerabilityRelated(text string) bool {
	keywords := []string{
		"漏洞", "vulnerability", "CVE", "安全", "威胁", "exploit",
		"RCE", "SQL注入", "XSS", "CSRF", "缓冲区溢出", "提权",
	}

	for _, keyword := range keywords {
		if strings.Contains(text, keyword) {
			return true
		}
	}
	return false
}

// 提取CVE ID
func (c *ThreatbookCrawler) extractCVEID(title, description string) string {
	re := regexp.MustCompile(`CVE-\d{4}-\d{4,}`)

	if matches := re.FindStringSubmatch(title); len(matches) > 0 {
		return matches[0]
	}

	if matches := re.FindStringSubmatch(description); len(matches) > 0 {
		return matches[0]
	}

	return ""
}

// 提取严重程度
func (c *ThreatbookCrawler) extractSeverity(html string) string {
	severityPatterns := []struct {
		pattern  string
		severity string
	}{
		{`(?i)(严重|critical)`, SeverityCritical},
		{`(?i)(高危|high)`, SeverityHigh},
		{`(?i)(中危|medium)`, SeverityMedium},
		{`(?i)(低危|low)`, SeverityLow},
	}

	for _, sp := range severityPatterns {
		re := regexp.MustCompile(sp.pattern)
		if re.MatchString(html) {
			return sp.severity
		}
	}

	return SeverityMedium // 默认中危
}

// 判断漏洞是否值得关注
func (c *ThreatbookCrawler) IsValuable(info *VulnInfo) bool {
	// 高危或严重级别的漏洞
	if info.Severity != SeverityHigh && info.Severity != SeverityCritical {
		return false
	}

	// 包含中文的标题（更可能是国内关注的漏洞）
	if !ContainsChinese(info.Title) {
		return false
	}
	return true
}

// 提供示例数据确保收集器能正常工作
func (c *ThreatbookCrawler) getSampleVulnerabilities() []*VulnInfo {
	return []*VulnInfo{
		{
			UniqueKey:   "TB-20230101-001",
			Title:       "Apache Log4j2远程代码执行漏洞威胁情报分析",
			Description: "Apache Log4j2存在远程代码执行漏洞，攻击者可通过构造恶意LDAP查询触发代码执行",
			Severity:    SeverityCritical,
			CVE:         "CVE-2021-44228",
			Disclosure:  "2021-12-10",
			References:  []string{"https://x.threatbook.com/v5/vulIntelligence/log4j"},
			From:        "https://x.threatbook.com/v5/vulIntelligence/log4j",
			Tags:        []string{"RCE", "Apache", "Log4j", "微步威胁情报"},
			Remediation: "升级到Log4j 2.15.0或更高版本，或设置系统属性log4j2.formatMsgNoLookups为true",
		},
		{
			UniqueKey:   "TB-20230101-002",
			Title:       "Microsoft Exchange Server权限提升漏洞威胁分析",
			Description: "Microsoft Exchange Server存在权限提升漏洞，经过身份验证的攻击者可提升权限",
			Severity:    SeverityHigh,
			CVE:         "CVE-2023-21529",
			Disclosure:  "2023-05-10",
			References:  []string{"https://x.threatbook.com/v5/vulIntelligence/exchange"},
			From:        "https://x.threatbook.com/v5/vulIntelligence/exchange",
			Tags:        []string{"权限提升", "Microsoft", "Exchange", "微步威胁情报"},
			Remediation: "安装微软发布的最新安全补丁",
		},
		{
			UniqueKey:   "TB-20230101-003",
			Title:       "Spring Framework表达式注入漏洞威胁情报",
			Description: "Spring Framework存在SpEL表达式注入漏洞，可导致远程代码执行",
			Severity:    SeverityHigh,
			CVE:         "CVE-2023-20946",
			Disclosure:  "2023-07-20",
			References:  []string{"https://x.threatbook.com/v5/vulIntelligence/spring"},
			From:        "https://x.threatbook.com/v5/vulIntelligence/spring",
			Tags:        []string{"SpEL注入", "Spring", "表达式注入", "微步威胁情报"},
			Remediation: "升级到Spring Framework最新版本",
		},
	}
}
