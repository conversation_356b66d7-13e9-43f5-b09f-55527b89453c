package service

import (
	"errors"
	"vulnerability_push/internal/models"
	"vulnerability_push/internal/utils"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// AuthService 认证服务
type AuthService struct {
	db         *gorm.DB
	jwtManager *utils.JWTManager
}

// NewAuthService 创建认证服务
func NewAuthService(db *gorm.DB, jwtSecret string, jwtExpireHours int) *AuthService {
	return &AuthService{
		db:         db,
		jwtManager: utils.NewJWTManager(jwtSecret, jwtExpireHours),
	}
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token string      `json:"token"`
	User  interface{} `json:"user"`
}

// Login 用户登录
func (s *AuthService) Login(username, password string) (*LoginResponse, error) {
	// 查找用户
	var user models.User
	err := s.db.Where("username = ?", username).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户名或密码错误")
		}
		return nil, errors.New("查询用户失败")
	}

	// 检查用户状态
	if !user.Status {
		return nil, errors.New("用户已被禁用")
	}

	// 验证密码
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password))
	if err != nil {
		return nil, errors.New("用户名或密码错误")
	}

	// 生成JWT token
	token, err := s.jwtManager.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return nil, errors.New("生成token失败")
	}

	// 构造响应
	userInfo := map[string]interface{}{
		"id":       user.ID,
		"username": user.Username,
		"email":    user.Email,
		"role":     user.Role,
		"status":   user.Status,
	}

	return &LoginResponse{
		Token: token,
		User:  userInfo,
	}, nil
}

// ValidateToken 验证token
func (s *AuthService) ValidateToken(tokenString string) (*utils.JWTClaims, error) {
	return s.jwtManager.ValidateToken(tokenString)
}

// GetUserByID 根据ID获取用户
func (s *AuthService) GetUserByID(userID uint) (*models.User, error) {
	var user models.User
	err := s.db.Where("id = ? AND status = ?", userID, true).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, errors.New("查询用户失败")
	}
	return &user, nil
}

// RefreshToken 刷新token
func (s *AuthService) RefreshToken(tokenString string) (string, error) {
	return s.jwtManager.RefreshToken(tokenString)
}

// HashPassword 加密密码
func (s *AuthService) HashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

// ValidateAPIKey 验证API密钥
func (s *AuthService) ValidateAPIKey(apiKey string) (*models.User, error) {
	var user models.User
	err := s.db.Where("api_key = ? AND status = ?", apiKey, true).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("API密钥无效")
		}
		return nil, errors.New("验证API密钥失败")
	}
	return &user, nil
}
