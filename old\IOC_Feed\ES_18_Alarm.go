package IOC_Feed

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"strings"
	"time"

	"github.com/elastic/go-elasticsearch/v6"
	"github.com/elastic/go-elasticsearch/v6/esapi"
	"vulnerability_push/internal/utils"
)

// AlarmService 告警服务结构体
type AlarmService struct {
	client    *elasticsearch.Client
	N         int    // 查询天数
	preDate   int    // 提前天数
	indexName string // 索引名称前缀
}

// DomesticThreat 国内威胁数据结构体
type DomesticThreat struct {
	NodeCode       string    `json:"nodeCode"`
	ActionTimeShow time.Time `json:"actionTimeShow"`
	// 根据实际需要添加其他字段
}

// ESSearchResponse Elasticsearch 6.8.0搜索响应结构体
type ESSearchResponse struct {
	ScrollID string `json:"_scroll_id"`
	Hits     struct {
		Total int `json:"total"` // ES 6.x中total是数字，不是对象
		Hits  []struct {
			Source map[string]interface{} `json:"_source"`
		} `json:"hits"`
	} `json:"hits"`
}

// NewAlarmService 创建新的告警服务实例
func NewAlarmService(esURL string, N, preDate int, indexName string) (*AlarmService, error) {
	cfg := elasticsearch.Config{
		Addresses: []string{esURL},
	}

	client, err := elasticsearch.NewClient(cfg)
	if err != nil {
		return nil, fmt.Errorf("创建Elasticsearch客户端失败: %v", err)
	}

	return &AlarmService{
		client:    client,
		N:         N,
		preDate:   preDate,
		indexName: indexName,
	}, nil
}

// GetNewestDataTest 获取最新数据测试方法
func (s *AlarmService) GetNewestDataTest() error {
	// 计算5分钟前的时间
	now := time.Now()
	fiveMinutesAgo := now.Add(-5 * time.Minute)

	// 构建查询体
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					{
						"range": map[string]interface{}{
							"actionTimeShow": map[string]interface{}{
								"gte": fiveMinutesAgo.Format("2006-01-02 15:04:05"),
								"lte": now.Format("2006-01-02 15:04:05"),
							},
						},
					},
				},
			},
		},
		"sort": []map[string]interface{}{
			{
				"actionTimeShow": map[string]interface{}{
					"order": "desc",
				},
			},
		},
		"size": 1000,
	}

	queryJSON, err := json.Marshal(query)
	if err != nil {
		return fmt.Errorf("构建查询JSON失败: %v", err)
	}

	utils.Infof("查询语句: %s", string(queryJSON))

	// 获取索引列表
	indices := s.getIndices()

	// 执行搜索请求
	req := esapi.SearchRequest{
		Index:  indices,
		Body:   bytes.NewReader(queryJSON),
		Scroll: time.Minute,
	}

	res, err := req.Do(context.Background(), s.client)
	if err != nil {
		return fmt.Errorf("执行搜索请求失败: %v", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("搜索请求返回错误: %s", res.String())
	}

	// 解析响应
	var searchResp ESSearchResponse
	if err := json.NewDecoder(res.Body).Decode(&searchResp); err != nil {
		return fmt.Errorf("解析搜索响应失败: %v", err)
	}

	utils.Infof("初始获取数据量: %d", len(searchResp.Hits.Hits))

	// 处理第一批结果
	var domesticThreats []DomesticThreat
	for _, hit := range searchResp.Hits.Hits {
		if nodeCode, ok := hit.Source["nodeCode"].(string); ok {
			// 处理数据逻辑...
			utils.Infof("处理节点代码: %s", nodeCode)
		}
	}

	// 使用scroll API获取剩余所有结果
	scrollID := searchResp.ScrollID
	for {
		if scrollID == "" {
			break
		}

		scrollReq := esapi.ScrollRequest{
			ScrollID: scrollID,
			Scroll:   time.Minute,
		}

		scrollRes, err := scrollReq.Do(context.Background(), s.client)
		if err != nil {
			return fmt.Errorf("执行scroll请求失败: %v", err)
		}
		defer scrollRes.Body.Close()

		if scrollRes.IsError() {
			return fmt.Errorf("scroll请求返回错误: %s", scrollRes.String())
		}

		var scrollResp ESSearchResponse
		if err := json.NewDecoder(scrollRes.Body).Decode(&scrollResp); err != nil {
			return fmt.Errorf("解析scroll响应失败: %v", err)
		}

		if len(scrollResp.Hits.Hits) == 0 {
			break
		}

		utils.Infof("后续获取数据量: %d", len(scrollResp.Hits.Hits))

		// 处理当前批次的结果
		for _, hit := range scrollResp.Hits.Hits {
			if nodeCode, ok := hit.Source["nodeCode"].(string); ok {
				// 处理数据逻辑...
				utils.Infof("处理节点代码: %s", nodeCode)
			}
		}

		scrollID = scrollResp.ScrollID
	}

	// 清除scroll上下文
	if scrollID != "" {
		clearReq := esapi.ClearScrollRequest{
			ScrollID: []string{scrollID},
		}
		clearRes, err := clearReq.Do(context.Background(), s.client)
		if err != nil {
			utils.Infof("清除scroll上下文失败: %v", err)
		} else {
			clearRes.Body.Close()
		}
	}

	utils.Infof("从ES取完所有数据，共处理 %d 条记录", len(domesticThreats))
	return nil
}

// getIndices 获取当前日期的索引列表
func (s *AlarmService) getIndices() []string {
	indices := make([]string, s.N+1)

	// 获取当前时间并调整
	now := time.Now()
	current := now.AddDate(0, 0, s.preDate) // 提前指定天数

	utils.Infof("基准日期: %s", current.Format("2006-01-02"))

	// 生成索引名称
	indices[0] = fmt.Sprintf("%s%s", s.indexName, current.Format("2006-01-02"))

	for i := 0; i < s.N; i++ {
		current = current.AddDate(0, 0, -1) // 往前推一天
		utils.Infof("索引日期: %s", current.Format("2006-01-02"))
		indices[i+1] = fmt.Sprintf("%s%s", s.indexName, current.Format("2006-01-02"))
	}

	return indices
}



// IsMaritimeInternalIP 检查是否为海事局内网IP地址 (198.10-30.*.*)
func IsMaritimeInternalIP(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	// 检查海事局内网地址范围 198.10-30.0.0/16
	if ipv4 := parsedIP.To4(); ipv4 != nil {
		if ipv4[0] == 198 && ipv4[1] >= 10 && ipv4[1] <= 30 {
			return true
		}
	}

	return false
}

// GetNodeCodeMapping 获取节点代码映射
func GetNodeCodeMapping() map[string]string {
	nodeMapping := make(map[string]string)

	// 添加测试节点代码
	nodeMapping["2001"] = "交通运输部救助打捞局"
	nodeMapping["2002"] = "船级社"
	nodeMapping["2003"] = "通信中心"
	nodeMapping["2004"] = "资格中心"
	nodeMapping["2005"] = "交科院"
	nodeMapping["2006"] = "公路院"
	nodeMapping["2007"] = "水运院"
	nodeMapping["2008"] = "规划院"
	nodeMapping["2009"] = "天科院"
	nodeMapping["2010"] = "路网中心"
	nodeMapping["2011"] = "大连海大"
	nodeMapping["2012"] = "交干院"
	nodeMapping["2013"] = "部海事局"
	nodeMapping["2014"] = "长航局"
	nodeMapping["2015"] = "珠航局"
	nodeMapping["2016"] = "交通报社"
	nodeMapping["2017"] = "交通出版社"
	nodeMapping["2018"] = "国际中心"

	return nodeMapping
}

// GetAttackCategoryMapping 获取攻击类别代码映射
func GetAttackCategoryMapping() map[string]string {
	categoryMapping := make(map[string]string)

	// 恶意代码类别
	categoryMapping["20101"] = "恶意代码"
	categoryMapping["20102"] = "木马"
	categoryMapping["20103"] = "网络蠕虫"
	categoryMapping["20104"] = "僵尸程序"
	categoryMapping["20105"] = "Webshell"
	categoryMapping["20106"] = "挖矿软件"
	categoryMapping["20107"] = "勒索软件"
	categoryMapping["20108"] = "流氓软件"
	categoryMapping["20109"] = "病毒感染"
	categoryMapping["20199"] = "其他有害程序"

	// 主机异常类别
	categoryMapping["20201"] = "主机异常"
	categoryMapping["20202"] = "流量异常"
	categoryMapping["20203"] = "终端行为异常"
	categoryMapping["20204"] = "网络行为异常"
	categoryMapping["20205"] = "账号异常"
	categoryMapping["20206"] = "非法外联"
	categoryMapping["20299"] = "其他异常行为"

	// 扫描类别
	categoryMapping["20301"] = "Web扫描"
	categoryMapping["20302"] = "漏洞扫描"
	categoryMapping["20303"] = "端口扫描"
	categoryMapping["20304"] = "主机存活扫描"
	categoryMapping["20305"] = "服务扫描"
	categoryMapping["20306"] = "扫描器指纹"
	categoryMapping["20399"] = "其他扫描行为"

	// 攻击类别
	categoryMapping["20401"] = "拒绝服务攻击"
	categoryMapping["20402"] = "Web攻击"
	categoryMapping["20403"] = "横向渗透"
	categoryMapping["20404"] = "漏洞利用攻击"
	categoryMapping["20405"] = "账号暴力破解"
	categoryMapping["20406"] = "权限许可和访问控制"
	categoryMapping["20407"] = "命令执行"
	categoryMapping["20408"] = "邮件攻击"
	categoryMapping["20409"] = "隐秘隧道"
	categoryMapping["20410"] = "系统后门"
	categoryMapping["20499"] = "其他网络攻击"

	// 信息破坏类别
	categoryMapping["20501"] = "信息泄露"
	categoryMapping["20502"] = "信息窃取"
	categoryMapping["20503"] = "信息篡改"
	categoryMapping["20599"] = "其他信息破坏"

	// 风险类别
	categoryMapping["20601"] = "漏洞风险"
	categoryMapping["20602"] = "配置风险"
	categoryMapping["20603"] = "端口风险"
	categoryMapping["20604"] = "未授权访问"
	categoryMapping["20605"] = "弱口令"
	categoryMapping["20606"] = "明文密码"
	categoryMapping["20699"] = "其他隐患风险"
	categoryMapping["20499"] = "其他网络攻击"

	return categoryMapping
}

// ConvertAttackCategories 将攻击类别代码转换为可读名称
func ConvertAttackCategories(categoryCodes string) string {
	if categoryCodes == "" {
		return "未知攻击类别"
	}

	categoryMapping := GetAttackCategoryMapping()
	codes := strings.Split(categoryCodes, ",")
	var categoryNames []string

	for _, code := range codes {
		code = strings.TrimSpace(code)
		if categoryName, exists := categoryMapping[code]; exists {
			categoryNames = append(categoryNames, fmt.Sprintf("%s(%s)", categoryName, code))
		} else {
			categoryNames = append(categoryNames, fmt.Sprintf("未知类别(%s)", code))
		}
	}

	return strings.Join(categoryNames, ", ")
}



// SearchAlarmData 搜索告警数据的通用方法
func (s *AlarmService) SearchAlarmData(startTime, endTime time.Time, size int) ([]map[string]interface{}, error) {
	var allResults []map[string]interface{}

	// 构建查询体
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					{
						"range": map[string]interface{}{
							"actionTimeShow": map[string]interface{}{
								"gte": startTime.Format("2006-01-02 15:04:05"),
								"lte": endTime.Format("2006-01-02 15:04:05"),
							},
						},
					},
				},
			},
		},
		"sort": []map[string]interface{}{
			{
				"actionTimeShow": map[string]interface{}{
					"order": "desc",
				},
			},
		},
		"size": size,
	}

	queryJSON, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("构建查询JSON失败: %v", err)
	}

	// 获取索引列表
	indices := s.getIndices()

	// 执行搜索请求
	req := esapi.SearchRequest{
		Index:  indices,
		Body:   bytes.NewReader(queryJSON),
		Scroll: time.Minute,
	}

	res, err := req.Do(context.Background(), s.client)
	if err != nil {
		return nil, fmt.Errorf("执行搜索请求失败: %v", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("搜索请求返回错误: %s", res.String())
	}

	// 解析响应
	var searchResp ESSearchResponse
	if err := json.NewDecoder(res.Body).Decode(&searchResp); err != nil {
		return nil, fmt.Errorf("解析搜索响应失败: %v", err)
	}

	// 处理第一批结果
	for _, hit := range searchResp.Hits.Hits {
		allResults = append(allResults, hit.Source)
	}

	// 使用scroll API获取剩余所有结果
	scrollID := searchResp.ScrollID
	for {
		if scrollID == "" {
			break
		}

		scrollReq := esapi.ScrollRequest{
			ScrollID: scrollID,
			Scroll:   time.Minute,
		}

		scrollRes, err := scrollReq.Do(context.Background(), s.client)
		if err != nil {
			return nil, fmt.Errorf("执行scroll请求失败: %v", err)
		}
		defer scrollRes.Body.Close()

		if scrollRes.IsError() {
			return nil, fmt.Errorf("scroll请求返回错误: %s", scrollRes.String())
		}

		var scrollResp ESSearchResponse
		if err := json.NewDecoder(scrollRes.Body).Decode(&scrollResp); err != nil {
			return nil, fmt.Errorf("解析scroll响应失败: %v", err)
		}

		if len(scrollResp.Hits.Hits) == 0 {
			break
		}

		// 处理当前批次的结果
		for _, hit := range scrollResp.Hits.Hits {
			allResults = append(allResults, hit.Source)
		}

		scrollID = scrollResp.ScrollID
	}

	// 清除scroll上下文
	if scrollID != "" {
		clearReq := esapi.ClearScrollRequest{
			ScrollID: []string{scrollID},
		}
		clearRes, err := clearReq.Do(context.Background(), s.client)
		if err != nil {
			utils.Infof("清除scroll上下文失败: %v", err)
		} else {
			clearRes.Body.Close()
		}
	}

	return allResults, nil
}

// Close 关闭Elasticsearch客户端连接
func (s *AlarmService) Close() error {
	// Go的Elasticsearch客户端不需要显式关闭
	return nil
}

// ListIndices 列出所有可用的索引
func (s *AlarmService) ListIndices() ([]string, error) {
	req := esapi.CatIndicesRequest{
		Format: "json",
	}

	res, err := req.Do(context.Background(), s.client)
	if err != nil {
		return nil, fmt.Errorf("获取索引列表失败: %v", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("获取索引列表返回错误: %s", res.String())
	}

	var indices []map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&indices); err != nil {
		return nil, fmt.Errorf("解析索引列表失败: %v", err)
	}

	var indexNames []string
	for _, index := range indices {
		if name, ok := index["index"].(string); ok {
			indexNames = append(indexNames, name)
		}
	}

	return indexNames, nil
}

// TestConnection 测试ES连接并显示可用索引
func (s *AlarmService) TestConnection() error {
	// 测试连接
	res, err := s.client.Info()
	if err != nil {
		return fmt.Errorf("连接ES失败: %v", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("ES连接返回错误: %s", res.String())
	}

	var info map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&info); err != nil {
		return fmt.Errorf("解析ES信息失败: %v", err)
	}

	utils.Infof("ES连接成功!")
	if version, ok := info["version"].(map[string]interface{}); ok {
		if number, ok := version["number"].(string); ok {
			utils.Infof("ES版本: %s", number)
		}
	}

	// 获取并显示所有索引
	indices, err := s.ListIndices()
	if err != nil {
		return fmt.Errorf("获取索引列表失败: %v", err)
	}

	utils.Infof("可用索引数量: %d", len(indices))
	utils.Infof("所有可用索引:")
	for i, index := range indices {
		utils.Infof("  %d. %s", i+1, index)
	}

	return nil
}

// 示例用法和测试函数
func ExampleUsage() {
	// 创建告警服务实例
	// 参数：Elasticsearch URL, 查询天数N, 提前天数preDate, 索引名称前缀
	service, err := NewAlarmService("http://127.0.0.1:9200", 7, 0, "alarm_info")
	if err != nil {
		log.Fatalf("创建告警服务失败: %v", err)
	}
	defer service.Close()

	// 首先测试连接并显示可用索引
	utils.Infof("测试ES连接...")
	if err := service.TestConnection(); err != nil {
		utils.Infof("ES连接测试失败: %v", err)
		return
	}

	utils.Infof("\n=== 索引前缀匹配成功! ===")
	utils.Infof("当前使用的索引前缀: alarm_info")

	// 先查看索引中的一些数据，了解节点代码分布
	utils.Infof("\n开始查询 alarm_info2025-07-23 索引中的数据样本...")

	// 查询前20条数据，查看节点代码分布
	sampleQuery := map[string]interface{}{
		"query": map[string]interface{}{
			"match_all": map[string]interface{}{},
		},
		"size": 20, // 获取20条数据样本
		"_source": []string{"nodeCode", "actionTimeShow", "incidentName"}, // 只返回关键字段
		"sort": []map[string]interface{}{
			{
				"actionTimeShow": map[string]interface{}{
					"order": "desc",
				},
			},
		},
	}

	sampleQueryJSON, err := json.Marshal(sampleQuery)
	if err != nil {
		utils.Infof("构建样本查询JSON失败: %v", err)
		return
	}

	// 执行样本查询
	sampleReq := esapi.SearchRequest{
		Index: []string{"alarm_info2025-07-23"},
		Body:  bytes.NewReader(sampleQueryJSON),
	}

	sampleRes, err := sampleReq.Do(context.Background(), service.client)
	if err != nil {
		utils.Infof("执行样本查询失败: %v", err)
		return
	}
	defer sampleRes.Body.Close()

	if sampleRes.IsError() {
		utils.Infof("样本查询返回错误: %s", sampleRes.String())
		return
	}

	// 解析样本结果
	var sampleResponse ESSearchResponse
	if err := json.NewDecoder(sampleRes.Body).Decode(&sampleResponse); err != nil {
		utils.Infof("解析样本响应失败: %v", err)
		return
	}

	// 统计节点代码分布
	nodeCodeCount := make(map[string]int)
	nodeMapping := GetNodeCodeMapping()

	utils.Infof("索引中共有 %d 条记录，以下是前20条的节点代码分布:", sampleResponse.Hits.Total)

	for i, hit := range sampleResponse.Hits.Hits {
		if nodeCode, ok := hit.Source["nodeCode"].(string); ok {
			nodeCodeCount[nodeCode]++
			if orgName, exists := nodeMapping[nodeCode]; exists {
				utils.Infof("  %d. 节点代码: %s (%s)", i+1, nodeCode, orgName)
			} else {
				utils.Infof("  %d. 节点代码: %s (未知组织)", i+1, nodeCode)
			}
		}
	}

	utils.Infof("\n节点代码统计 (前20条数据):")
	for nodeCode, count := range nodeCodeCount {
		if orgName, exists := nodeMapping[nodeCode]; exists {
			utils.Infof("  %s: %d条 (%s)", nodeCode, count, orgName)
		} else {
			utils.Infof("  %s: %d条 (未知组织)", nodeCode, count)
		}
	}

	// 现在查询索引中的所有数据
	utils.Infof("\n开始查询索引中的所有数据...")

	// 查询alarm_info2025-07-23索引中的所有数据，使用scroll API获取全部记录
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"match_all": map[string]interface{}{},
		},
		"size": 1000, // 每次获取1000条数据
		"sort": []map[string]interface{}{
			{
				"actionTimeShow": map[string]interface{}{
					"order": "desc",
				},
			},
		},
	}

	queryJSON, err := json.Marshal(query)
	if err != nil {
		utils.Infof("构建查询JSON失败: %v", err)
		return
	}

	// 执行搜索请求，使用scroll API获取所有数据
	req := esapi.SearchRequest{
		Index:  []string{"alarm_info2025-07-23"}, // 查询指定索引
		Body:   bytes.NewReader(queryJSON),
		Scroll: time.Minute, // 设置scroll超时时间
	}

	res, err := req.Do(context.Background(), service.client)
	if err != nil {
		utils.Infof("执行搜索请求失败: %v", err)
		return
	}
	defer res.Body.Close()

	if res.IsError() {
		utils.Infof("搜索请求返回错误: %s", res.String())
		return
	}

	// 解析响应
	var searchResp ESSearchResponse
	if err := json.NewDecoder(res.Body).Decode(&searchResp); err != nil {
		utils.Infof("解析搜索响应失败: %v", err)
		return
	}

	// 收集所有结果
	var allResults []map[string]interface{}

	// 处理第一批结果
	for _, hit := range searchResp.Hits.Hits {
		allResults = append(allResults, hit.Source)
	}

	utils.Infof("第一批获取到 %d 条数据", len(searchResp.Hits.Hits))

	// 使用scroll API获取剩余所有结果
	scrollID := searchResp.ScrollID
	batchCount := 1

	for {
		if scrollID == "" {
			break
		}

		scrollReq := esapi.ScrollRequest{
			ScrollID: scrollID,
			Scroll:   time.Minute,
		}

		scrollRes, err := scrollReq.Do(context.Background(), service.client)
		if err != nil {
			utils.Infof("执行scroll请求失败: %v", err)
			break
		}
		defer scrollRes.Body.Close()

		if scrollRes.IsError() {
			utils.Infof("scroll请求返回错误: %s", scrollRes.String())
			break
		}

		var scrollResp ESSearchResponse
		if err := json.NewDecoder(scrollRes.Body).Decode(&scrollResp); err != nil {
			utils.Infof("解析scroll响应失败: %v", err)
			break
		}

		if len(scrollResp.Hits.Hits) == 0 {
			break
		}

		batchCount++
		utils.Infof("第%d批获取到 %d 条数据", batchCount, len(scrollResp.Hits.Hits))

		// 处理当前批次的结果
		for _, hit := range scrollResp.Hits.Hits {
			allResults = append(allResults, hit.Source)
		}

		scrollID = scrollResp.ScrollID
	}

	// 清除scroll上下文
	if scrollID != "" {
		clearReq := esapi.ClearScrollRequest{
			ScrollID: []string{scrollID},
		}
		clearRes, err := clearReq.Do(context.Background(), service.client)
		if err != nil {
			utils.Infof("清除scroll上下文失败: %v", err)
		} else {
			clearRes.Body.Close()
		}
	}

	results := allResults
	utils.Infof("从 alarm_info2025-07-23 索引总共获取到 %d 条告警数据", len(results))

	// 显示合并后的告警数据摘要
	utils.Infof("\n=== 合并后的告警数据摘要 (攻击IP | 受害IP | 来源标签 | 攻击类别 | 攻击次数 | 首次/末次攻击时间) ===")

	nodeMappingData := GetNodeCodeMapping()

	// 攻击记录结构体
	type AttackRecord struct {
		SourceIP        string
		DestinationIP   string
		SourceLabel     string
		IncidentTypeSub string
		AttackTimes     []string
		AttackCount     int
		FirstTime       string
		LastTime        string
	}

	// 用于合并相同攻击IP和受害IP的记录
	attackMap := make(map[string]*AttackRecord) // key: sourceIP-destinationIP

	// 收集和合并数据
	var filteredCount int // 统计被过滤的内网攻击数量

	for _, result := range results {
		var sourceIP, destinationIP, nodeCode, sourceLabel, incidentTypeSub, attackTime string

		// 提取攻击IP
		if sip, ok := result["sourceIP"].(string); ok {
			sourceIP = sip
		}

		// 提取受害IP
		if dip, ok := result["destinationIP"].(string); ok {
			destinationIP = dip
		}

		// 提取节点代码并转换为来源标签
		if nc, ok := result["nodeCode"].(string); ok {
			nodeCode = nc
			if orgName, exists := nodeMappingData[nodeCode]; exists {
				sourceLabel = orgName
			} else {
				sourceLabel = "未知组织"
			}
		}

		// 提取攻击类别
		if its, ok := result["incidentTypeSub"].(float64); ok {
			incidentTypeSub = fmt.Sprintf("%.0f", its)
		}

		// 提取攻击时间
		if at, ok := result["actionTimeShow"].(string); ok {
			attackTime = at
		}

		if sourceIP != "" && destinationIP != "" {
			// 过滤内网到内网的攻击
			if IsPrivateIP(sourceIP) && IsPrivateIP(destinationIP) {
				filteredCount++
				continue // 跳过内网到内网的攻击
			}

			// 特殊过滤：部海事局(节点代码2013)的内网IP地址 198.10-30.*.*
			if nodeCode == "2013" {
				if IsMaritimeInternalIP(sourceIP) || IsMaritimeInternalIP(destinationIP) {
					filteredCount++
					continue // 跳过部海事局的内网IP攻击
				}
			}

			key := fmt.Sprintf("%s-%s", sourceIP, destinationIP)

			if record, exists := attackMap[key]; exists {
				// 合并现有记录
				record.AttackTimes = append(record.AttackTimes, attackTime)
				record.AttackCount++

				// 更新首次和末次攻击时间
				if attackTime < record.FirstTime {
					record.FirstTime = attackTime
				}
				if attackTime > record.LastTime {
					record.LastTime = attackTime
				}

				// 合并攻击类别（如果不同）
				if !strings.Contains(record.IncidentTypeSub, incidentTypeSub) {
					if record.IncidentTypeSub != "" {
						record.IncidentTypeSub += "," + incidentTypeSub
					} else {
						record.IncidentTypeSub = incidentTypeSub
					}
				}
			} else {
				// 创建新记录
				attackMap[key] = &AttackRecord{
					SourceIP:        sourceIP,
					DestinationIP:   destinationIP,
					SourceLabel:     sourceLabel,
					IncidentTypeSub: incidentTypeSub,
					AttackTimes:     []string{attackTime},
					AttackCount:     1,
					FirstTime:       attackTime,
					LastTime:        attackTime,
				}
			}
		}
	}

	// 显示合并后的记录
	recordIndex := 1
	for _, record := range attackMap {
		utils.Infof("\n--- 合并记录 %d ---", recordIndex)
		utils.Infof("攻击IP: %s", record.SourceIP)
		utils.Infof("受害IP: %s", record.DestinationIP)
		utils.Infof("来源标签: %s", record.SourceLabel)
		utils.Infof("攻击类别: %s", ConvertAttackCategories(record.IncidentTypeSub))
		utils.Infof("攻击次数: %d次", record.AttackCount)
		utils.Infof("首次攻击时间: %s", record.FirstTime)
		utils.Infof("末次攻击时间: %s", record.LastTime)

		// 如果攻击次数较多，显示时间跨度
		if record.AttackCount > 1 {
			firstTime, _ := time.Parse("2006-01-02T15:04:05-0700", record.FirstTime)
			lastTime, _ := time.Parse("2006-01-02T15:04:05-0700", record.LastTime)
			duration := lastTime.Sub(firstTime)
			utils.Infof("攻击时间跨度: %v", duration)
		}

		recordIndex++
	}

	utils.Infof("\n=== 合并统计 ===")
	utils.Infof("原始记录数: %d条", len(results))
	utils.Infof("过滤内网攻击: %d条", filteredCount)
	utils.Infof("有效外部攻击: %d条", len(results)-filteredCount)
	utils.Infof("合并后记录数: %d条", len(attackMap))
	utils.Infof("数据压缩率: %.1f%%", float64(len(attackMap))/float64(len(results)-filteredCount)*100)

	// 显示统计信息
	utils.Infof("\n=== 数据统计分析 ===")

	// 统计节点代码分布
	nodeCodeStats := make(map[string]int)
	deviceBrandStats := make(map[string]int)
	incidentLevelStats := make(map[float64]int)
	incidentTypeStats := make(map[float64]int)

	for _, result := range results {
		if nodeCode, ok := result["nodeCode"].(string); ok {
			nodeCodeStats[nodeCode]++
		}

		if deviceBrand, ok := result["deviceBrand"].(string); ok {
			deviceBrandStats[deviceBrand]++
		}

		if incidentLevel, ok := result["incidentLevel"].(float64); ok {
			incidentLevelStats[incidentLevel]++
		}

		if incidentType, ok := result["incidentType"].(float64); ok {
			incidentTypeStats[incidentType]++
		}
	}

	utils.Infof("\n节点代码分布:")
	for nodeCode, count := range nodeCodeStats {
		if orgName, exists := nodeMapping[nodeCode]; exists {
			utils.Infof("  %s: %d条 (%s)", nodeCode, count, orgName)
		} else {
			utils.Infof("  %s: %d条 (未知组织)", nodeCode, count)
		}
	}

	utils.Infof("\n设备品牌分布:")
	for brand, count := range deviceBrandStats {
		utils.Infof("  %s: %d条", brand, count)
	}

	utils.Infof("\n事件级别分布:")
	for level, count := range incidentLevelStats {
		utils.Infof("  级别%.0f: %d条", level, count)
	}

	utils.Infof("\n事件类型分布:")
	for iType, count := range incidentTypeStats {
		utils.Infof("  类型%.0f: %d条", iType, count)
	}

	// 统计所有数据中的字段完整性
	fieldCount := make(map[string]int)
	for _, result := range results {
		for key := range result {
			fieldCount[key]++
		}
	}

	utils.Infof("\n字段完整性统计:")
	for field, count := range fieldCount {
		percentage := float64(count) / float64(len(results)) * 100
		utils.Infof("  %s: %d/%d条记录 (%.1f%%)", field, count, len(results), percentage)
	}

	utils.Infof("\n=== 查询完成 ===")
	utils.Infof("alarm_info2025-07- 索引数据查询结束")
}

// ExampleUsageMain 函数用于测试（原main函数）
func ExampleUsageMain() {
	utils.Infof("=== Elasticsearch告警数据服务测试 ===")
	ExampleUsage()
}