<template>
  <div class="user-management">
    <div class="page-header">
      <h2>用户管理</h2>
      <div class="header-actions">
        <el-input
          v-model="searchQuery"
          placeholder="搜索用户名或邮箱"
          style="width: 250px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="fetchUsers">刷新</el-button>
        <el-button type="success" @click="showCreateDialog">
          <el-icon><Plus /></el-icon> 添加用户
        </el-button>
      </div>
    </div>
    
    <el-card shadow="hover" class="table-card">
      <el-table
        :data="displayUsers"
        border
        style="width: 100%"
        v-loading="loading"
        row-key="id"
        stripe
      >
        <el-table-column prop="id" label="ID" width="80" sortable />
        <el-table-column prop="username" label="用户名" sortable />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="role" label="角色" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.role === 'admin' ? 'danger' : 'success'">
              {{ scope.row.role || '普通用户' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status ? 'success' : 'info'">
              {{ scope.row.status ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="440">
          <template #default="scope">
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              type="success" 
              @click="handleViewApiKey(scope.row)"
            >
              查看API密钥
            </el-button>
            <el-button 
              size="small" 
              type="warning" 
              @click="handleResetApiKey(scope.row)"
            >
              重置API密钥
            </el-button>
            <el-button 
              size="small" 
              type="info" 
              @click="handleChangePassword(scope.row)"
            >
              修改密码
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDelete(scope.row)"
              :disabled="scope.row.role === 'admin'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          background
          layout="prev, pager, next, sizes, total"
          :total="total"
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <div class="empty-data" v-else>
        <el-empty description="暂无用户数据" />
      </div>
    </el-card>
    
    <!-- 编辑对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑用户" width="500px" destroy-on-close>
      <el-form :model="editForm" :rules="rules" ref="editFormRef" label-width="100px" status-icon>
        <el-form-item label="用户名">
          <el-input v-model="editForm.username" disabled />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" />
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="editForm.role" style="width: 100%">
            <el-option label="管理员" value="admin" />
            <el-option label="普通用户" value="user" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-switch
            v-model="editForm.status"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="updateUser" :loading="updating">确定</el-button>
      </template>
    </el-dialog>
    
    <!-- 创建用户对话框 -->
    <el-dialog v-model="createDialogVisible" title="创建用户" width="500px" destroy-on-close>
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px" status-icon>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="createForm.username" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="createForm.password" type="password" show-password />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="createForm.email" />
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="createForm.role" style="width: 100%">
            <el-option label="管理员" value="admin" />
            <el-option label="普通用户" value="user" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="createUser" :loading="creating">确定</el-button>
      </template>
    </el-dialog>
    
    <!-- 修改密码对话框 -->
    <el-dialog v-model="passwordDialogVisible" title="修改密码" width="500px" destroy-on-close>
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="100px" status-icon>
        <el-form-item label="用户名">
          <el-input v-model="passwordForm.username" disabled />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="passwordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="changePassword" :loading="changingPassword">确定</el-button>
      </template>
    </el-dialog>
    
    <!-- API密钥对话框 -->
    <el-dialog v-model="apiKeyDialogVisible" title="API密钥" width="500px" destroy-on-close>
      <div class="api-key-container">
        <p>用户: <strong>{{ currentApiKeyUser }}</strong></p>
        <p v-if="isResetApiKey">API密钥已重置，请妥善保管:</p>
        <p v-else>当前API密钥:</p>
        <el-input 
          v-model="currentApiKey" 
          readonly
          class="api-key-input"
        >
          <template #append>
            <el-button @click="copyApiKey">复制</el-button>
          </template>
        </el-input>
        <div class="api-key-warning" v-if="isResetApiKey">
          <el-alert
            title="注意：此密钥仅显示一次，请立即保存！"
            type="warning"
            show-icon
          />
        </div>
      </div>
      <template #footer>
        <el-button type="primary" @click="apiKeyDialogVisible = false">确定</el-button>
      </template>
    </el-dialog>
    
    <!-- 查看API密钥对话框 -->
    <el-dialog v-model="viewApiKeyDialogVisible" title="查看API密钥" width="500px" destroy-on-close>
      <div v-loading="loadingApiKey" class="api-key-container">
        <p>用户: <strong>{{ currentApiKeyUser }}</strong></p>
        <p>当前API密钥:</p>
        <el-input 
          v-model="currentApiKey" 
          readonly
          class="api-key-input"
        >
          <template #append>
            <el-button @click="copyApiKey">复制</el-button>
          </template>
        </el-input>
      </div>
      <template #footer>
        <el-button type="primary" @click="viewApiKeyDialogVisible = false">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import api from './api'

const users = ref([])
const loading = ref(false)
const editDialogVisible = ref(false)
const createDialogVisible = ref(false)
const apiKeyDialogVisible = ref(false)
const viewApiKeyDialogVisible = ref(false)
const passwordDialogVisible = ref(false)
const updating = ref(false)
const creating = ref(false)
const changingPassword = ref(false)
const loadingApiKey = ref(false)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const currentApiKey = ref('')
const currentApiKeyUser = ref('')
const isResetApiKey = ref(false)

const editFormRef = ref(null)
const createFormRef = ref(null)
const passwordFormRef = ref(null)

const editForm = ref({
  id: null,
  username: '',
  email: '',
  role: 'user',
  status: true
})

const createForm = ref({
  username: '',
  password: '',
  email: '',
  role: 'user'
})

const passwordForm = ref({
  id: null,
  username: '',
  newPassword: '',
  confirmPassword: ''
})

const rules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

const createRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

const passwordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// 显示的用户列表（直接使用从服务器获取的数据）
const displayUsers = computed(() => {
  return users.value
})

// 获取用户列表
const fetchUsers = async () => {
  loading.value = true
  try {
    const res = await api.getUsers({
      page: currentPage.value,
      pageSize: pageSize.value,
      username: searchQuery.value || undefined,
    })

    // 处理分页响应数据
    if (res.data && res.data.data) {
      // 根据实际返回的数据结构处理
      if (res.data.data.list) {
        // 新的数据结构：{ data: { list: [], pagination: {} } }
        users.value = res.data.data.list || []
        total.value = res.data.data.pagination?.total || 0
      } else if (Array.isArray(res.data.data)) {
        // 旧的数据结构：{ data: [] }
        users.value = res.data.data || []
        total.value = res.data.data.length || 0
      } else {
        // 其他可能的结构
        users.value = res.data.data.items || []
        total.value = res.data.data.total || 0
      }
    } else {
      users.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取用户列表失败', error)
    users.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 显示创建用户对话框
const showCreateDialog = () => {
  createForm.value = {
    username: '',
    password: '',
    email: '',
    role: 'user'
  }
  createDialogVisible.value = true
}

// 创建用户
const createUser = async () => {
  if (!createFormRef.value) return
  
  createFormRef.value.validate(async (valid) => {
    if (valid) {
      creating.value = true
      try {
        await api.createUser(createForm.value)
        ElMessage.success('创建成功')
        createDialogVisible.value = false
        fetchUsers()
      } catch (error) {
        console.error('创建用户失败', error)
      } finally {
        creating.value = false
      }
    }
  })
}

// 编辑用户
const handleEdit = (user) => {
  editForm.value = { ...user }
  editDialogVisible.value = true
}

// 更新用户
const updateUser = async () => {
  if (!editFormRef.value) return
  
  editFormRef.value.validate(async (valid) => {
    if (valid) {
      updating.value = true
      try {
        await api.updateUser(editForm.value.id, {
          email: editForm.value.email,
          role: editForm.value.role,
          status: editForm.value.status
        })
        ElMessage.success('更新成功')
        editDialogVisible.value = false
        fetchUsers()
      } catch (error) {
        console.error('更新用户失败', error)
      } finally {
        updating.value = false
      }
    }
  })
}

// 查看API密钥
const handleViewApiKey = async (user) => {
  try {
    loadingApiKey.value = true
    currentApiKeyUser.value = user.username

    // 获取用户详情，包含API密钥
    const res = await api.getUserDetail(user.id)
    if (res.data && res.data.data && res.data.data.apiKey) {
      currentApiKey.value = res.data.data.apiKey
      isResetApiKey.value = false
      viewApiKeyDialogVisible.value = true
    } else {
      ElMessage.warning('无法获取API密钥，请稍后再试')
      console.log('API响应数据结构:', res.data)
    }
  } catch (error) {
    console.error('获取API密钥失败', error)
    ElMessage.error('获取API密钥失败')
  } finally {
    loadingApiKey.value = false
  }
}

// 重置API密钥
const handleResetApiKey = (user) => {
  ElMessageBox.confirm(`确定要重置用户 ${user.username} 的API密钥吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      ElMessage.info('正在重置API密钥，请稍候...')
      const res = await api.resetApiKey(user.id)
      if (res.data && res.data.data && res.data.data.apiKey) {
        currentApiKey.value = res.data.data.apiKey
        currentApiKeyUser.value = user.username
        isResetApiKey.value = true
        apiKeyDialogVisible.value = true
        ElMessage.success('API密钥重置成功')
      } else {
        ElMessage.error('重置API密钥失败: 服务器返回数据格式错误')
        console.error('重置API密钥失败: 服务器返回数据格式错误', res)
      }
    } catch (error) {
      console.error('重置API密钥失败', error)
      const errorMsg = error.response?.data?.msg || '未知错误'
      ElMessage.error({
        message: `重置API密钥失败: ${errorMsg}`,
        duration: 5000
      })
    }
  }).catch(() => {})
}

// 复制API密钥
const copyApiKey = () => {
  // 创建一个临时的textarea元素
  const textarea = document.createElement('textarea')
  textarea.value = currentApiKey.value
  textarea.style.position = 'fixed'  // 避免滚动到底部
  textarea.style.opacity = '0'
  document.body.appendChild(textarea)
  
  try {
    // 选择文本并复制
    textarea.select()
    const successful = document.execCommand('copy')
    if (successful) {
      ElMessage.success('已复制到剪贴板')
    } else {
      ElMessage.error('复制失败，请手动复制')
    }
  } catch (err) {
    console.error('复制失败:', err)
    ElMessage.error('复制失败，请手动复制')
  } finally {
    // 移除临时元素
    document.body.removeChild(textarea)
  }
}

// 删除用户
const handleDelete = (user) => {
  // 禁止删除管理员
  if (user.role === 'admin') {
    ElMessage.warning('不能删除管理员用户')
    return
  }
  
  ElMessageBox.confirm(`确定要删除用户 ${user.username} 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await api.deleteUser(user.id)
      ElMessage.success('删除成功')
      fetchUsers()
    } catch (error) {
      console.error('删除用户失败', error)
    }
  }).catch(() => {})
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  // 添加防抖延迟，避免频繁请求
  clearTimeout(searchTimeout.value)
  searchTimeout.value = setTimeout(() => {
    fetchUsers()
  }, 300)
}

// 搜索防抖定时器
const searchTimeout = ref(null)

// 分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchUsers()
}

// 当前页变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchUsers()
}

// 显示修改密码对话框
const handleChangePassword = (user) => {
  passwordForm.value = {
    id: user.id,
    username: user.username,
    newPassword: '',
    confirmPassword: ''
  }
  passwordDialogVisible.value = true
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return
  
  passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      changingPassword.value = true
      try {
        await api.adminChangePassword(passwordForm.value.id, {
          newPassword: passwordForm.value.newPassword
        })
        ElMessage.success('密码修改成功')
        passwordDialogVisible.value = false
      } catch (error) {
        console.error('修改密码失败', error)
      } finally {
        changingPassword.value = false
      }
    }
  })
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.user-management {
  padding: 0;
  height: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.empty-data {
  padding: 30px 0;
}

.api-key-container {
  padding: 10px;
}

.api-key-input {
  margin: 15px 0;
}

.api-key-warning {
  margin-top: 15px;
}
</style> 