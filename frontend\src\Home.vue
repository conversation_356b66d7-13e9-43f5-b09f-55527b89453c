<template>
  <div class="dashboard-container">
    <!-- 统计卡片行 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-card-header">
            <div class="stat-title">总漏洞数</div>
            <el-icon class="stat-icon database-icon"><DataLine /></el-icon>
          </div>
          <div class="stat-value">{{ stats.total?.count || 0 }}</div>
          <div class="stat-trend" :class="{ 'trend-up': (stats.total?.growthRate || 0) > 0, 'trend-down': (stats.total?.growthRate || 0) < 0 }">
            <el-icon v-if="(stats.total?.growthRate || 0) > 0"><ArrowUp /></el-icon>
            <el-icon v-else-if="(stats.total?.growthRate || 0) < 0"><ArrowDown /></el-icon>
            <span>{{ Math.abs(stats.total?.growthRate || 0) }}% 相比上周</span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card warning-card" shadow="hover">
          <div class="stat-card-header">
            <div class="stat-title">IOC情报数</div>
            <el-icon class="stat-icon warning-icon"><Clock /></el-icon>
          </div>
          <div class="stat-value">{{ iocStats.total?.count || 0 }}</div>
          <div class="stat-alert" v-if="iocStats.total?.count > 0">
            <el-icon><AlarmClock /></el-icon>
            <span>情报已更新</span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card danger-card" shadow="hover">
          <div class="stat-card-header">
            <div class="stat-title">源告警数</div>
            <el-icon class="stat-icon danger-icon"><WarningFilled /></el-icon>
          </div>
          <div class="stat-value">{{ sourceAlarmStats?.count || 0 }}</div>
          <div class="stat-trend" :class="{ 'trend-up': (sourceAlarmStats?.growthRate || 0) > 0, 'trend-down': (sourceAlarmStats?.growthRate || 0) < 0 }">
            <el-icon v-if="(sourceAlarmStats?.growthRate || 0) > 0"><ArrowUp /></el-icon>
            <el-icon v-else-if="(sourceAlarmStats?.growthRate || 0) < 0"><ArrowDown /></el-icon>
            <span>{{ Math.abs(sourceAlarmStats?.growthRate || 0) }}% 相比上周</span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card success-card" shadow="hover">
          <div class="stat-card-header">
            <div class="stat-title">活跃收集器</div>
            <el-icon class="stat-icon success-icon"><Message /></el-icon>
          </div>
          <div class="stat-value">{{ stats.activeCollectors?.count || 0 }}</div>
          <div class="stat-status success-status" v-if="stats.activeCollectors?.status === '运行正常'">
            <el-icon><CircleCheck /></el-icon>
            <span>运行正常</span>
          </div>
          <div class="stat-status warning-status" v-else>
            <el-icon><Warning /></el-icon>
            <span>{{ stats.activeCollectors?.status || '状态未知' }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- IP情报卡片行 -->
    <el-row :gutter="20" class="stat-cards ip-intel-row">
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>今日来源标签排名</span>
            </div>
          </template>

          <div class="source-label-ranking-container">
            <div v-if="loading" class="chart-loading">
              <el-skeleton :rows="5" animated />
            </div>
            <div v-else-if="!sourceLabelRanking || sourceLabelRanking.length === 0" class="chart-empty">
              <el-empty description="暂无排名数据" />
            </div>
            <div v-else class="source-label-ranking-list">
              <div v-for="(item, index) in sourceLabelRanking.slice(0, 8)" :key="item.label"
                   class="source-label-ranking-item">
                <div class="ranking-info">
                  <div class="ranking-badge" :class="getRankingBadgeClass(index)">
                    {{ index + 1 }}
                  </div>
                  <div class="ranking-content">
                    <div class="ranking-label" :title="item.label">{{ item.label }}</div>
                    <div class="ranking-count">{{ formatNumber(item.count) }} 次</div>
                  </div>
                </div>
                <div class="ranking-progress-bar">
                  <div class="ranking-progress"
                       :style="{ width: (item.count / sourceLabelRanking[0].count * 100) + '%' }"></div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>今日生产IOC</span>
            </div>
          </template>

          <div class="today-ioc-container">
            <div v-if="loading" class="chart-loading">
              <el-skeleton :rows="5" animated />
            </div>
            <div v-else-if="!todayIOCList || todayIOCList.length === 0" class="chart-empty">
              <el-empty description="今天暂无新生产IOC" />
            </div>
            <div v-else class="today-ioc-list">
              <div v-for="(ioc, index) in todayIOCList" :key="index"
                   class="today-ioc-item" @click="viewIOCDetail(ioc.id)">
                <div class="ioc-content" :title="ioc.iocValue">{{ ioc.iocValue }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>今日入库漏洞</span>
            </div>
          </template>

          <div class="today-vulns-container">
            <div v-if="loading" class="chart-loading">
              <el-skeleton :rows="5" animated />
            </div>
            <div v-else-if="!stats.todayVulnerabilities || stats.todayVulnerabilities.length === 0" class="chart-empty">
              <el-empty description="今天暂无新入库漏洞" />
            </div>
            <div v-else class="today-vulns-list">
              <div v-for="(vuln, index) in stats.todayVulnerabilities" :key="index"
                   class="today-vuln-item" @click="viewVulnDetail(vuln.id)">
                <div class="vuln-info">
                  <el-tag :type="getSeverityType(vuln.severity)" size="small" class="vuln-severity">
                    {{ vuln.severity }}
                  </el-tag>
                  <div class="vuln-name" :title="vuln.name">{{ vuln.name }}</div>
                </div>
                <div class="vuln-id">{{ vuln.vulnId }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表行 -->
    <el-row :gutter="20" class="chart-row">
      <!-- 漏洞严重程度分布 -->
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>漏洞严重程度分布</span>
            </div>
          </template>
          
          <div class="chart-container">
            <div v-if="loading" class="chart-loading">
              <el-skeleton :rows="5" animated />
            </div>
            <div v-else-if="!hasDistributionData" class="chart-empty">
              <el-empty description="暂无数据" />
            </div>
            <div v-else id="pieChart" class="severity-chart" ref="pieChart"></div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 威胁类型分布 -->
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>威胁类型分布</span>
            </div>
          </template>

          <div class="chart-container">
            <div v-if="loading" class="chart-loading">
              <el-skeleton :rows="3" animated />
            </div>
            <div v-else-if="!hasThreatTypeData" class="chart-empty">
              <el-empty description="暂无数据" />
            </div>
            <div v-else id="threatTypeChart" class="threat-type-chart" ref="threatTypeChart"></div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 收集器状态 -->
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>收集器状态</span>
            </div>
          </template>
          
          <div class="collectors-container">
            <div v-if="loading" class="chart-loading">
              <el-skeleton :rows="4" animated />
            </div>
            <div v-else-if="!stats.collectorStats || stats.collectorStats.length === 0" class="chart-empty">
              <el-empty description="暂无收集器数据" />
            </div>
            <div v-else class="collector-list">
              <div class="collector-item" v-for="(collector, index) in stats.collectorStats" :key="index">
                <div class="collector-info">
                  <div class="collector-status-dot" :class="collector.status"></div>
                  <div class="collector-name">{{ collector.name }}</div>
                  <div class="collector-dash">-</div>
                </div>
                <div class="collector-count">{{ collector.count }} 条漏洞</div>
                <div class="collector-actions">
                  <el-tooltip content="运行收集器" placement="top">
                    <el-button circle size="small" type="primary" plain @click="confirmRunCrawler(collector)">
                      <el-icon><VideoPlay /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="查看详情" placement="top">
                    <el-button circle size="small" plain @click="viewCrawlerDetail(collector)">
                      <el-icon><Document /></el-icon>
                    </el-button>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 确认对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="确认操作"
      width="30%"
      :before-close="handleDialogClose"
    >
      <span>{{ dialogMessage }}</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirm" :loading="confirmLoading">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 漏洞详情抽屉 -->
    <el-drawer
      v-model="vulnDrawerVisible"
      :title="currentVuln.name"
      direction="rtl"
      size="50%"
    >
      <div v-if="vulnLoading" class="drawer-loading">
        <el-skeleton :rows="10" animated />
      </div>
      <div v-else class="vuln-detail">
        <el-table :data="vulnDetailItems" style="width: 100%" border>
          <el-table-column prop="label" label="属性" width="120" />
          <el-table-column prop="value" label="内容">
            <template #default="scope">
              <!-- 危害等级 -->
              <template v-if="scope.row.type === 'severity'">
                <el-tag :type="getSeverityType(scope.row.value)" effect="dark">
                  {{ scope.row.value }}
                </el-tag>
              </template>
              
              <!-- 标签 -->
              <template v-else-if="scope.row.type === 'tags'">
                <el-tag
                  v-for="tag in scope.row.value"
                  :key="tag"
                  size="small"
                  effect="plain"
                  class="detail-tag"
                >
                  {{ tag }}
                </el-tag>
                <span v-if="!scope.row.value || scope.row.value.length === 0">无</span>
              </template>
              
              <!-- 来源 -->
              <template v-else-if="scope.row.type === 'source'">
                <a 
                  v-if="isValidUrl(scope.row.value)" 
                  :href="scope.row.value" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  class="source-link"
                >
                  {{ scope.row.value }}
                </a>
                <span v-else>{{ scope.row.value || '无' }}</span>
              </template>
              
              <!-- 参考链接 -->
              <template v-else-if="scope.row.type === 'references'">
                <div v-if="scope.row.value && scope.row.value.length > 0">
                  <div v-for="(link, index) in scope.row.value" :key="index" class="reference-link">
                    <a :href="link" target="_blank" rel="noopener noreferrer">{{ link }}</a>
                  </div>
                </div>
                <div v-else>无</div>
              </template>
              
              <!-- 多行文本 -->
              <template v-else-if="scope.row.type === 'multiline'">
                <div class="multiline-text">{{ scope.row.value }}</div>
              </template>
              
              <!-- 默认文本 -->
              <template v-else>
                {{ scope.row.value || '无' }}
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>

    <!-- 日志对话框 -->
    <el-dialog
      v-model="logsDialogVisible"
      :title="`采集日志 - ${selectedCrawlerName}`"
      width="80%"
    >
      <div class="dialog-header-actions" style="margin-bottom: 15px; text-align: right;">
        <el-button type="danger" @click="confirmClearLogs" :loading="clearLogsLoading">
          清除日志
        </el-button>
      </div>

      <el-table :data="crawlerLogs" style="width: 100%" v-loading="logsLoading" border stripe>
        <el-table-column prop="id" label="ID" width="60" align="center" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getLogStatusType(scope.row.status)">
              {{ getLogStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="采集数量" width="100" align="center" />
        <el-table-column prop="newCount" label="新增数量" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.newCount > 0" type="success" effect="plain">
              {{ scope.row.newCount }}
            </el-tag>
            <span v-else>{{ scope.row.newCount || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="消息" show-overflow-tooltip />
        <el-table-column prop="startTime" label="开始时间" width="180" align="center">
          <template #default="scope">
            <span>{{ formatTimestamp(scope.row.startTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="endTime" label="结束时间" width="180" align="center">
          <template #default="scope">
            <span v-if="scope.row.endTime">{{ formatTimestamp(scope.row.endTime) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container" style="margin-top: 15px; text-align: right;">
        <el-pagination
          v-model:current-page="logsCurrentPage"
          v-model:page-size="logsPageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="logsTotal"
          @size-change="handleLogsSizeChange"
          @current-change="handleLogsCurrentChange"
        />
      </div>
    </el-dialog>

    <!-- 确认清除日志对话框 -->
    <el-dialog
      v-model="clearLogsDialogVisible"
      title="确认清除日志"
      width="30%"
    >
      <span>确定要清除{{ selectedCrawlerName ? `"${selectedCrawlerName}"的` : '所有' }}采集日志吗？此操作不可恢复！</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="clearLogsDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="doClearLogs" :loading="clearLogsLoading">
            确认清除
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { 
  DataLine, 
  Warning, 
  WarningFilled, 
  Message, 
  ArrowUp, 
  ArrowDown, 
  CircleCheck, 
  AlarmClock,
  VideoPlay,
  Document,
  Clock,
  Monitor,
  ArrowRight
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from './api'
import * as echarts from 'echarts'
import { useRouter } from 'vue-router'

const router = useRouter()

// 创建默认统计数据对象
const defaultStats = () => ({
  total: { count: 0, growthRate: 0 },
  urgent: { count: 0, needAction: false },
  highRisk: { count: 0, growthRate: 0 },
  activeCollectors: { count: 0, status: '运行正常' },
  severityDistribution: {},
  collectorStats: [],
  todayVulnerabilities: []
})

// 创建默认IOC统计数据对象
const defaultIOCStats = () => ({
  total: { count: 0, growthRate: 0 },
  highRisk: { count: 0, needAction: false },
  todayNew: { count: 0, status: '正常' },
  riskLevelDistribution: {},
  iocTypeDistribution: {}
})

// 创建默认源告警统计数据对象
const defaultSourceAlarmStats = () => ({
  count: 0,
  growthRate: 0,
  categories: {}
})

const loading = ref(true)
const stats = ref(defaultStats())
const iocStats = ref(defaultIOCStats())
const sourceAlarmStats = ref(defaultSourceAlarmStats())
const sourceLabelRanking = ref([])
const todayIOCList = ref([])

const pieChart = ref(null)
const threatTypeChart = ref(null)
let chart = null
let threatTypeChartInstance = null

// 对话框相关
const dialogVisible = ref(false)
const dialogMessage = ref('')
const confirmLoading = ref(false)
const currentCrawler = ref(null)
const confirmAction = ref('')

// 日志对话框相关
const logsDialogVisible = ref(false)
const crawlerLogs = ref([])
const logsLoading = ref(false)
const selectedCrawlerName = ref('')
const logsCurrentPage = ref(1)
const logsPageSize = ref(10)
const logsTotal = ref(0)

// 清除日志相关
const clearLogsDialogVisible = ref(false)
const clearLogsLoading = ref(false)
const currentCrawlerId = ref(null)

// 漏洞详情抽屉相关
const vulnDrawerVisible = ref(false)
const vulnLoading = ref(false)
const currentVuln = ref({
  id: null,
  name: '',
  vulnId: '',
  severity: '',
  tags: [],
  disclosureDate: '',
  source: '',
  description: '',
  references: [],
  remediation: ''
})

// 计算是否有分布数据
const hasDistributionData = computed(() => {
  const distribution = stats.value.severityDistribution || {}
  return Object.values(distribution).some(value => value > 0)
})

// 计算是否有威胁类型分布数据
const hasThreatTypeData = computed(() => {
  const distribution = sourceAlarmStats.value.categories || {}
  return Object.values(distribution).some(value => value > 0)
})

// 为漏洞详情表格提供数据
const vulnDetailItems = computed(() => {
  if (!currentVuln.value) return []
  
  return [
    { label: '漏洞编号', value: currentVuln.value.vulnId, type: 'text' },
    { label: '危害等级', value: currentVuln.value.severity, type: 'severity' },
    { label: '标签', value: currentVuln.value.tags || [], type: 'tags' },
    { label: '披露日期', value: currentVuln.value.disclosureDate, type: 'text' },
    { label: '信息来源', value: currentVuln.value.source, type: 'source' },
    { label: '漏洞描述', value: currentVuln.value.description, type: 'multiline' },
    { label: '参考链接', value: currentVuln.value.references || [], type: 'references' },
    { label: '修复建议', value: currentVuln.value.remediation, type: 'multiline' }
  ]
})

// 初始化饼图
const initPieChart = () => {
  // 确保DOM已加载，使用nextTick等待DOM更新
  nextTick(() => {
    if (!pieChart.value || !hasDistributionData.value) return
    
    // 销毁旧图表
    if (chart) {
      chart.dispose()
    }
    
    // 创建新图表
    chart = echarts.init(pieChart.value)
    
    // 准备数据
    const distribution = stats.value.severityDistribution || {}
    const data = [
      { name: '严重', value: distribution['严重'] || 0, itemStyle: { color: '#F56C6C' } },
      { name: '高危', value: distribution['高危'] || 0, itemStyle: { color: '#E6A23C' } },
      { name: '中危', value: distribution['中危'] || 0, itemStyle: { color: '#909399' } },
      { name: '低危', value: distribution['低危'] || 0, itemStyle: { color: '#67C23A' } }
    ].filter(item => item.value > 0) // 过滤掉数量为0的项
    
    // 设置图表选项
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        right: '5%',
        top: 'center',
        data: data.map(item => item.name)
      },
      series: [
        {
          name: '漏洞级别',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '16',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: data
        }
      ]
    }
    
    // 渲染图表
    chart.setOption(option)
  })
}

// 初始化威胁类型环形图
const initThreatTypeChart = () => {
  nextTick(() => {
    if (!threatTypeChart.value || !hasThreatTypeData.value) return

    // 销毁旧图表
    if (threatTypeChartInstance) {
      threatTypeChartInstance.dispose()
    }

    // 创建新图表
    threatTypeChartInstance = echarts.init(threatTypeChart.value)

    // 准备数据
    const distribution = sourceAlarmStats.value.categories || {}

    // 定义威胁类型颜色映射
    const colorMap = {
      '弱口令': '#FF6B6B',
      'Tor暗网流量': '#4ECDC4',
      'struts漏洞攻击': '#45B7D1',
      '漏洞利用攻击': '#F39C12',
      'Web攻击': '#E74C3C',
      '恶意软件': '#9B59B6',
      '网络扫描': '#1ABC9C',
      '异常流量': '#F1C40F',
      '信息收集': '#95A5A6',
      '僵尸网络': '#E67E22',
      '钓鱼攻击': '#3498DB',
      '暴力破解': '#8E44AD'
    }

    // 转换数据格式并分配颜色，只显示Top10
    const allData = Object.entries(distribution)
      .filter(([_, value]) => value > 0)
      .map(([name, value], index) => ({
        name,
        value,
        itemStyle: {
          color: colorMap[name] || `hsl(${(index * 137.5) % 360}, 70%, 60%)`
        }
      }))
      .sort((a, b) => b.value - a.value) // 按数量降序排序

    // 只取前10个，其余合并为"其他"
    let data = allData.slice(0, 10)
    if (allData.length > 10) {
      const otherValue = allData.slice(10).reduce((sum, item) => sum + item.value, 0)
      if (otherValue > 0) {
        data.push({
          name: '其他',
          value: otherValue,
          itemStyle: { color: '#CCCCCC' }
        })
      }
    }

    // 设置图表选项
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        right: '2%',
        top: 'center',
        data: data.map(item => item.name),
        textStyle: {
          fontSize: 11
        },
        itemWidth: 14,
        itemHeight: 10,
        itemGap: 8,
        formatter: function(name) {
          // 限制图例文字长度，避免过长
          return name.length > 8 ? name.substring(0, 8) + '...' : name
        }
      },
      series: [
        {
          name: '威胁类型',
          type: 'pie',
          radius: ['35%', '65%'],
          center: ['35%', '50%'], // 环形图居中偏左
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 6,
            borderColor: '#fff',
            borderWidth: 1
          },
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '12',
              fontWeight: 'bold'
            },
            itemStyle: {
              shadowBlur: 8,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          },
          labelLine: {
            show: false
          },
          data: data
        }
      ]
    }

    // 渲染图表
    threatTypeChartInstance.setOption(option)
  })
}

// 获取统计数据
const fetchStats = async () => {
  // 检查用户是否已登录，如果未登录则不获取统计数据
  if (!localStorage.getItem('token')) {
    return;
  }

  loading.value = true
  try {
    // 并行获取漏洞统计、IOC统计和源告警统计
    const [vulnRes, iocRes, sourceAlarmRes] = await Promise.all([
      api.getStats(),
      api.getIOCIntelligenceStats(),
      api.getIOCSourceDataStats()
    ])

    // 处理漏洞统计数据
    if (vulnRes.data && vulnRes.data.code === 200) {
      stats.value = {
        total: vulnRes.data.data.total,
        urgent: vulnRes.data.data.urgent,
        highRisk: vulnRes.data.data.highRisk,
        activeCollectors: vulnRes.data.data.activeCollectors,
        severityDistribution: vulnRes.data.data.severityDistribution,
        collectorStats: vulnRes.data.data.collectorStats || [],
        todayVulnerabilities: vulnRes.data.data.todayVulnerabilities || []
      }
    } else {
      console.warn('获取漏洞统计数据失败:', vulnRes.data?.msg)
    }

    // 处理IOC统计数据
    if (iocRes.data && iocRes.data.code === 200) {
      iocStats.value = {
        total: iocRes.data.data.total,
        highRisk: iocRes.data.data.highRisk,
        todayNew: iocRes.data.data.todayNew,
        riskLevelDistribution: iocRes.data.data.riskLevelDistribution || {},
        iocTypeDistribution: iocRes.data.data.iocTypeDistribution || {}
      }

      // 处理今日IOC列表数据
      todayIOCList.value = iocRes.data.data.todayIOCList || []
    } else {
      console.warn('获取IOC统计数据失败:', iocRes.data?.msg)
    }

    // 处理源告警统计数据
    if (sourceAlarmRes.data && sourceAlarmRes.data.code === 200) {
      sourceAlarmStats.value = {
        count: sourceAlarmRes.data.data.totalRecords || 0,
        growthRate: 0, // TODO: 后端需要提供增长率数据
        categories: sourceAlarmRes.data.data.categories || {}
      }

      // 处理来源标签排名数据
      const sourceLabels = sourceAlarmRes.data.data.sourceLabels || {}
      sourceLabelRanking.value = Object.entries(sourceLabels)
        .map(([label, count]) => ({ label, count }))
        .sort((a, b) => b.count - a.count) // 按命中次数降序排序
    } else {
      console.warn('获取源告警统计数据失败:', sourceAlarmRes.data?.msg)
    }

    // 等待DOM更新后初始化图表
    nextTick(() => {
      initPieChart()
      initThreatTypeChart()
    })
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  } finally {
    loading.value = false
  }
}

// 确认运行收集器
const confirmRunCrawler = (crawler) => {
  currentCrawler.value = crawler
  dialogMessage.value = `确定要运行收集器 "${crawler.name}" 吗？这将立即开始漏洞采集任务。`
  confirmAction.value = 'runCrawler'
  dialogVisible.value = true
}

// 查看收集器详情
const viewCrawlerDetail = async (crawler) => {
  if (!crawler.id) {
    ElMessage.warning('收集器ID不存在，无法查看详情')
    return
  }
  
  // 显示日志对话框
  selectedCrawlerName.value = crawler.name
  logsDialogVisible.value = true
  currentCrawlerId.value = crawler.id
  logsCurrentPage.value = 1
  logsPageSize.value = 10
  
  // 加载日志数据
  await fetchCrawlerLogs()
}

// 获取采集器日志
const fetchCrawlerLogs = async () => {
  logsLoading.value = true
  
  try {
    // 获取采集器日志
    const res = await api.getCrawlerLogs(
      currentCrawlerId.value, 
      logsCurrentPage.value, 
      logsPageSize.value
    )
    
    if (res.data && res.data.code === 200) {
      const data = res.data.data
      crawlerLogs.value = data.list || []
      logsTotal.value = data.total || 0
    } else {
      ElMessage.error(res.data?.msg || '获取采集器日志失败')
    }
  } catch (error) {
    console.error('获取采集器日志失败:', error)
    ElMessage.error('获取采集器日志失败')
  } finally {
    logsLoading.value = false
  }
}

// 处理日志分页大小变化
const handleLogsSizeChange = (val) => {
  logsPageSize.value = val
  fetchCrawlerLogs()
}

// 处理日志当前页变化
const handleLogsCurrentChange = (val) => {
  logsCurrentPage.value = val
  fetchCrawlerLogs()
}

// 确认清除日志
const confirmClearLogs = () => {
  clearLogsDialogVisible.value = true
}

// 执行清除日志
const doClearLogs = async () => {
  clearLogsLoading.value = true
  
  try {
    await api.clearCrawlerLogs(currentCrawlerId.value)
    ElMessage.success('日志清除成功')
    
    // 重新加载日志数据
    logsCurrentPage.value = 1
    await fetchCrawlerLogs()
  } catch (error) {
    console.error('清除日志失败:', error)
    ElMessage.error('清除日志失败')
  } finally {
    clearLogsLoading.value = false
    clearLogsDialogVisible.value = false
  }
}

// 格式化时间戳
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp * 1000)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
}

// 获取日志状态类型
const getLogStatusType = (status) => {
  switch (status?.toLowerCase()) {
    case 'success':
      return 'success'
    case 'failed':
      return 'danger'
    case 'running':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取日志状态文本
const getLogStatusText = (status) => {
  switch (status?.toLowerCase()) {
    case 'success':
      return '成功'
    case 'failed':
      return '失败'
    case 'running':
      return '运行中'
    default:
      return status
  }
}

// 处理对话框关闭
const handleDialogClose = () => {
  dialogVisible.value = false
}

// 处理确认操作
const handleConfirm = async () => {
  if (!currentCrawler.value) {
    dialogVisible.value = false
    return
  }
  
  if (confirmAction.value === 'runCrawler') {
    await runCrawler(currentCrawler.value)
  }
  
  dialogVisible.value = false
}

// 运行收集器
const runCrawler = async (crawler) => {
  if (!crawler.id) {
    ElMessage.warning('收集器ID不存在，无法运行')
    return
  }
  
  confirmLoading.value = true
  try {
    // 调用API运行收集器
    const res = await api.runCrawler(crawler.id)
    if (res.data && res.data.code === 200) {
      ElMessage.success(`收集器 "${crawler.name}" 已开始运行`)
      // 重新获取统计数据
      await fetchStats()
    } else {
      ElMessage.error(res.data?.msg || '运行收集器失败')
    }
  } catch (error) {
    console.error('运行收集器失败', error)
    ElMessage.error('运行收集器失败: ' + (error.response?.data?.msg || '网络错误'))
  } finally {
    confirmLoading.value = false
  }
}

// 获取漏洞详情
const viewVulnDetail = async (vulnId) => {
  vulnLoading.value = true
  try {
    const res = await api.getVulnerabilityDetail(vulnId)
    if (res.data && res.data.code === 200) {
      currentVuln.value = res.data.data
      vulnDrawerVisible.value = true
    } else {
      ElMessage.error(res.data?.msg || '获取漏洞详情失败')
    }
  } catch (error) {
    console.error('获取漏洞详情失败:', error)
    ElMessage.error('获取漏洞详情失败')
  } finally {
    vulnLoading.value = false
  }
}

// 根据危害等级获取标签类型
const getSeverityType = (severity) => {
  switch (severity) {
    case '严重':
      return 'error'  // 使用更深的红色
    case '高危':
      return 'danger'
    case '中危':
      return 'warning'
    case '低危':
      return 'success'
    case '信息':
      return 'info'
    default:
      return 'info'
  }
}

// 检查是否是有效的URL
const isValidUrl = (url) => {
  if (!url) return false
  try {
    new URL(url)
    return true
  } catch (e) {
    return false
  }
}

// 导航到IP情报页面
const navigateToIPIntelligence = () => {
  router.push('/ip-intelligence')
}

// 格式化数字显示
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

// 获取排名徽章样式
const getRankingBadgeClass = (index) => {
  if (index === 0) return 'ranking-badge-gold'
  if (index === 1) return 'ranking-badge-silver'
  if (index === 2) return 'ranking-badge-bronze'
  return 'ranking-badge-default'
}

// 查看IOC详情
const viewIOCDetail = (iocId) => {
  // TODO: 实现IOC详情查看功能
  console.log('查看IOC详情:', iocId)
  ElMessage.info('IOC详情功能开发中...')
}

onMounted(() => {
  fetchStats()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
  
  // 监听存储变化，当token被移除时（退出登录）停止获取数据
  window.addEventListener('storage', handleStorageChange)
})

onUnmounted(() => {
  // 清理图表和事件监听
  if (chart) {
    chart.dispose()
    chart = null
  }
  if (threatTypeChartInstance) {
    threatTypeChartInstance.dispose()
    threatTypeChartInstance = null
  }
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('storage', handleStorageChange)
})

// 处理窗口大小变化
const handleResize = () => {
  if (chart) {
    chart.resize()
  }
  if (threatTypeChartInstance) {
    threatTypeChartInstance.resize()
  }
}

// 处理存储变化
const handleStorageChange = (event) => {
  if (event.key === 'token' && !event.newValue) {
    // token被移除，用户已退出登录
    stats.value = defaultStats()
  }
}
</script>

<style scoped>
.dashboard-container {
  padding: 0;
}

.stat-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 140px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.stat-title {
  font-size: 16px;
  font-weight: 500;
  color: #606266;
}

.stat-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.database-icon {
  color: #409EFF;
  background-color: rgba(64, 158, 255, 0.1);
}

.warning-icon {
  color: #E6A23C;
  background-color: rgba(230, 162, 60, 0.1);
}

.danger-icon {
  color: #F56C6C;
  background-color: rgba(245, 108, 108, 0.1);
}

.success-icon {
  color: #67C23A;
  background-color: rgba(103, 194, 58, 0.1);
}

.stat-value {
  font-size: 36px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #303133;
}

.stat-trend, .stat-alert, .stat-status {
  display: flex;
  align-items: center;
  font-size: 14px;
  gap: 5px;
}

.trend-up {
  color: #F56C6C;
}

.trend-down {
  color: #67C23A;
}

.stat-alert {
  color: #E6A23C;
}

.success-status {
  color: #67C23A;
}

.warning-status {
  color: #E6A23C;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-card {
  border-radius: 8px;
  overflow: hidden;
  min-height: 350px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.chart-container {
  height: 300px;
  position: relative;
}

.chart-loading, .chart-empty {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.severity-chart {
  height: 100%;
  width: 100%;
}

.threat-type-chart {
  height: 100%;
  width: 100%;
}

.today-vulns-container {
  height: 300px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #909399 #f5f7fa;
}

/* WebKit浏览器的滚动条样式 */
.today-vulns-container::-webkit-scrollbar {
  width: 6px;
}

.today-vulns-container::-webkit-scrollbar-track {
  background: #f5f7fa;
}

.today-vulns-container::-webkit-scrollbar-thumb {
  background-color: #909399;
  border-radius: 3px;
}

.today-vulns-list {
  padding: 10px 0;
}

.today-vuln-item {
  padding: 15px 10px;
  border-bottom: 1px solid #EBEEF5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 4px;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.today-vuln-item:hover {
  background-color: #f5f7fa;
  transform: translateY(-2px);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.today-vuln-item:last-child {
  border-bottom: none;
}

.vuln-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  overflow: hidden;
  min-width: 0; /* 确保flex容器可以正确缩小 */
}

.vuln-severity {
  font-weight: 500;
  flex-shrink: 0;
}

.vuln-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0; /* 这是关键，确保flex项可以正确缩小 */
}

.vuln-id {
  color: #909399;
  font-size: 14px;
  margin-left: 5px;
  max-width: 130px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.collectors-container {
  height: 300px;
  overflow-y: auto;
}

.collector-list {
  padding: 10px 0;
}

.collector-item {
  padding: 15px 10px;
  border-bottom: 1px solid #EBEEF5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.collector-item:last-child {
  border-bottom: none;
}

.collector-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.collector-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.collector-status-dot.error {
  background-color: #F56C6C;
}

.collector-status-dot.success {
  background-color: #67C23A;
}

.collector-name {
  font-weight: 500;
}

.collector-dash {
  color: #909399;
}

.collector-count {
  color: #909399;
  font-size: 14px;
  flex: 1;
  text-align: center;
}

.collector-actions {
  display: flex;
  gap: 5px;
  justify-content: flex-end;
  margin-left: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.vuln-detail {
  padding: 20px;
}

.el-table {
  margin-bottom: 20px;
}

.el-table .el-table__header th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

.multiline-text {
  white-space: pre-wrap;
  line-height: 1.5;
  max-height: 300px;
  overflow-y: auto;
  padding: 5px;
}

.source-link {
  color: #409EFF;
  text-decoration: none;
}

.source-link:hover {
  text-decoration: underline;
}

.reference-link {
  margin-bottom: 5px;
}

.reference-link a {
  color: #409EFF;
  text-decoration: none;
}

.reference-link a:hover {
  text-decoration: underline;
}

.drawer-loading {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.pagination-container {
  text-align: right;
  margin-top: 15px;
}

.ip-intel-row {
  margin-bottom: 20px;
}

.view-more {
  color: #409EFF;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 来源标签排名样式 */
.source-label-ranking-container {
  height: 300px;
  position: relative;
}

.source-label-ranking-list {
  height: 100%;
  overflow-y: auto;
  padding: 10px 0;
}

.source-label-ranking-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.source-label-ranking-item:hover {
  background: #e9ecef;
  transform: translateX(2px);
}

.source-label-ranking-item:last-child {
  margin-bottom: 0;
}

.ranking-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.ranking-badge {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: white;
  margin-right: 12px;
  flex-shrink: 0;
}

.ranking-badge-gold {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.ranking-badge-silver {
  background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);
}

.ranking-badge-bronze {
  background: linear-gradient(135deg, #CD7F32, #B8860B);
  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);
}

.ranking-badge-default {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}

.ranking-content {
  flex: 1;
  min-width: 0;
}

.ranking-label {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ranking-count {
  font-size: 12px;
  color: #909399;
}

.ranking-progress-bar {
  width: 80px;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-left: 12px;
  flex-shrink: 0;
}

.ranking-progress {
  height: 100%;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  border-radius: 3px;
  transition: width 0.6s ease;
}

/* 今日IOC样式 */
.today-ioc-container {
  height: 300px;
  position: relative;
}

.today-ioc-list {
  height: 100%;
  overflow-y: auto;
  padding: 10px 0;
  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.today-ioc-list::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

.today-ioc-item {
  padding: 12px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.today-ioc-item:hover {
  background: #e9ecef;
  transform: translateX(2px);
}

.today-ioc-item:last-child {
  margin-bottom: 0;
}

.ioc-content {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>