# 数据库初始化文档

## 概述

本文档描述了V2版本的数据库初始化流程。我们已经移除了所有迁移代码，改为使用统一的数据库初始化逻辑。

## 主要变更

### 1. 移除的组件
- 删除了 `backend/cmd/migrate/` 目录及其迁移工具
- 删除了 `backend/migrations/` 目录及所有SQL迁移文件
- 移除了 `manager.go` 中的所有迁移函数：
  - `migrateIOCIntelligencePushStatus`
  - `migratePushChannelFields`
  - `migrateProductionStrategyScheduleFields`
  - `migrateDataInterfaceStatsFields`
  - `migrateTJUNFields`
  - `migrateWeibuFields`
  - `migrateIOCSourceDataUUIDField`
  - `migrateDataInterfaceLogFields`
  - `migrateDataInterfaceCollectionFields`

### 2. 新增组件
- 新增 `backend/cmd/init_db/main.go` 数据库初始化工具
- 简化的数据库初始化流程

## 数据库初始化流程

### 1. 自动初始化
数据库初始化现在完全基于模型定义，所有字段、索引和约束都在模型中定义：

```go
// 在 internal/database/manager.go 中
func (m *Manager) Initialize(models []interface{}, skipSeeding bool) error {
    // 1. 连接数据库
    // 2. 初始化数据填充器
    // 3. 自动迁移数据库表结构（包含所有字段和索引）
    // 4. 填充默认数据
}
```

### 2. 模型定义
所有数据库表结构都在模型中完整定义，包括：

#### IOC情报相关表
- `IOCIntelligence` - IOC情报主表，包含天际友盟和微步威胁情报字段
- `IOCIntelligenceData` - IOC情报源数据表，包含UUID字段
- `IOCIntelligencePushRecord` - IOC情报推送记录表
- `IOCWhitelist` - IOC白名单表

#### 数据接口相关表
- `DataInterface` - 数据接口配置表，包含采集配置和统计字段
- `DataInterfaceLog` - 数据接口日志表，包含执行时间和数据量字段

#### 生产策略相关表
- `ProductionStrategy` - 生产策略表，包含定时任务配置字段
- `ProductionStrategyLog` - 生产策略日志表

#### 推送相关表
- `PushChannel` - 推送通道表，包含启用状态字段
- `PushRecord` - 推送记录表，包含策略ID和创建时间字段
- `PushPolicy` - 推送策略表
- `PushWhitelist` - 推送白名单表

#### 其他表
- `User` - 用户表
- `Vulnerability` - 漏洞表
- `Crawler` - 采集器表
- `CrawlerLog` - 采集器日志表
- `RssConfig` - RSS配置表
- `ExportConfig` - 导出配置表
- `ProcessedUUID` - UUID去重表

## 使用方法

### 1. 全新数据库初始化
```bash
cd backend
go run cmd/init_db/main.go -config config.yaml
```

### 2. 重新创建数据库（删除所有表）
```bash
cd backend
go run cmd/init_db/main.go -config config.yaml -drop-tables
```

### 3. 跳过默认数据填充
```bash
cd backend
go run cmd/init_db/main.go -config config.yaml -skip-seed
```

### 4. 参数说明
- `-config`: 配置文件路径（默认：config.yaml）
- `-drop-tables`: 删除所有表后重新创建（需要确认）
- `-skip-seed`: 跳过默认数据填充

## 字段完整性

所有之前通过迁移添加的字段现在都已经在模型中完整定义：

### IOCIntelligence表新增字段
- `push_status` - 推送状态
- `pushed_at` - 推送时间
- `tjun_data` - 天际友盟原始数据
- `tjun_query_status` - 天际友盟查询状态
- `tjun_query_time` - 天际友盟查询时间
- `tjun_error_message` - 天际友盟查询错误信息
- `weibu_data` - 微步威胁情报原始数据
- `weibu_query_status` - 微步威胁情报查询状态
- `weibu_query_time` - 微步威胁情报查询时间
- `weibu_error_message` - 微步威胁情报查询错误信息

### DataInterface表新增字段
- 采集配置字段：`collection_enabled`, `collection_freq`, `collection_interval`
- 时间范围字段：`time_range_type`, `time_range_value`, `start_time`, `end_time`
- 统计字段：`last_run_time`, `last_run_status`, `last_run_message`, `total_runs`, `success_runs`, `failed_runs`, `last_data_count`, `total_data_count`

### ProductionStrategy表新增字段
- 定时任务字段：`schedule_enabled`, `schedule_interval`, `last_run_time`, `next_run_time`

### PushChannel表新增字段
- `is_enabled` - 启用状态

### PushRecord表新增字段
- `policy_id` - 策略ID
- `created_at` - 创建时间

### DataInterfaceLog表新增字段
- `started_at` - 开始时间
- `ended_at` - 结束时间
- `duration` - 执行时长
- `data_count` - 处理数据量

### IOCIntelligenceData表新增字段
- `uuid` - 数据源唯一标识

## 优势

1. **简化维护**：不再需要维护复杂的迁移脚本
2. **一致性**：所有表结构都在模型中统一定义
3. **可靠性**：避免了迁移过程中的错误和不一致
4. **易于理解**：新开发者只需查看模型定义即可了解完整的表结构
5. **版本控制**：表结构变更直接体现在模型代码中

## 注意事项

1. **数据备份**：在使用 `-drop-tables` 参数前，请确保已备份重要数据
2. **配置文件**：确保 `config.yaml` 中的数据库配置正确
3. **权限**：确保数据库用户有创建表和索引的权限
4. **新数据库**：建议使用全新的数据库名称，避免与旧版本冲突

## 故障排除

### 1. 连接失败
- 检查数据库服务是否运行
- 验证配置文件中的连接参数
- 确认网络连接

### 2. 权限错误
- 确保数据库用户有足够的权限
- 检查数据库和表的创建权限

### 3. 表已存在错误
- 使用 `-drop-tables` 参数重新创建
- 或手动删除冲突的表

### 4. 默认数据填充失败
- 使用 `-skip-seed` 跳过默认数据填充
- 检查模型定义和种子数据的一致性
