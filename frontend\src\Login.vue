<template>
  <div class="login-container">
    <el-card class="login-card">
      <div class="login-header">
        <h2>威胁情报管理平台</h2>
        <div class="divider"></div>
      </div>
      
      <el-form :model="form" :rules="rules" ref="formRef">
        <el-form-item prop="username">
          <el-input v-model="form.username" placeholder="用户名" />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input v-model="form.password" type="password" placeholder="密码" show-password />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleLogin" :loading="loading" style="width: 100%">
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import api from './api'

const router = useRouter()
const formRef = ref(null)
const loading = ref(false)

const form = ref({
  username: '',
  password: ''
})

const rules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

// 在组件挂载时清除任何可能导致错误提示的状态
onMounted(() => {
  // 如果是从其他页面退出登录跳转过来的，可能会有错误提示
  // 清除这些错误提示的定时器
  const errorMessageElements = document.querySelectorAll('.el-message--error');
  if (errorMessageElements.length > 0) {
    errorMessageElements.forEach(el => {
      el.remove();
    });
  }
});

const handleLogin = () => {
  if (!formRef.value) return
  
  formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const res = await api.login({
          username: form.value.username,
          password: form.value.password
        })
        
        if (res.data && res.data.data && res.data.data.token) {
          console.log('登录响应:', res.data)

          // 存储token并触发更新
          localStorage.setItem('token', res.data.data.token)
          console.log('Token已存储:', localStorage.getItem('token'))

          // 触发存储事件，以便其他组件能够监听到登录状态的变化
          window.dispatchEvent(new Event('storage'))

          // 登录成功后立即获取用户信息
          try {
            const userRes = await api.getCurrentUser()
            console.log('获取用户信息成功:', userRes)
          } catch (error) {
            console.error('获取用户信息失败', error)
            // 即使获取用户信息失败，也继续跳转
          }

          ElMessage.success('登录成功')
          console.log('准备跳转到首页，当前URL:', window.location.href)

          // 确保token已经存储，然后等待一小段时间
          await new Promise(resolve => setTimeout(resolve, 100))
          console.log('Token验证:', localStorage.getItem('token') ? '存在' : '不存在')

          // 直接使用window.location.href进行跳转，这是最可靠的方式
          console.log('使用window.location.href跳转到首页')
          window.location.href = '/home'
        } else {
          console.error('登录响应格式错误:', res)
          ElMessage.error('登录响应格式错误')
        }
      } catch (error) {
        console.error('登录失败', error)
        const errorMsg = error.response?.data?.msg || '登录失败，请检查账号和密码'
        ElMessage.error(errorMsg)
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style scoped>
.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(120deg, #e0e7ff 0%, #f0fdfa 100%);
}

.login-card {
  width: 350px;
  padding: 20px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 10px;
}

.divider {
  height: 4px;
  width: 50px;
  background: linear-gradient(90deg, #6a8dff 0%, #4fd1c5 100%);
  margin: 0 auto;
}

.el-form-item {
  margin-bottom: 20px;
}
</style> 