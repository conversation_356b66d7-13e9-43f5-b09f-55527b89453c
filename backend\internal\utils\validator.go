package utils

import (
	"fmt"
	"net"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"unicode/utf8"
)

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// Error 实现error接口
func (ve ValidationError) Error() string {
	return fmt.Sprintf("%s: %s", ve.Field, ve.Message)
}

// ValidationErrors 验证错误集合
type ValidationErrors []ValidationError

// Error 实现error接口
func (ves ValidationErrors) Error() string {
	if len(ves) == 0 {
		return ""
	}
	
	var messages []string
	for _, ve := range ves {
		messages = append(messages, ve.Error())
	}
	return strings.Join(messages, "; ")
}

// HasErrors 检查是否有错误
func (ves ValidationErrors) HasErrors() bool {
	return len(ves) > 0
}

// Add 添加验证错误
func (ves *ValidationErrors) Add(field, message string) {
	*ves = append(*ves, ValidationError{Field: field, Message: message})
}

// Validator 验证器
type Validator struct {
	errors ValidationErrors
}

// NewValidator 创建验证器
func NewValidator() *Validator {
	return &Validator{
		errors: make(ValidationErrors, 0),
	}
}

// HasErrors 检查是否有错误
func (v *Validator) HasErrors() bool {
	return v.errors.HasErrors()
}

// GetErrors 获取错误列表
func (v *Validator) GetErrors() ValidationErrors {
	return v.errors
}

// Error 获取错误信息
func (v *Validator) Error() error {
	if !v.HasErrors() {
		return nil
	}
	return v.errors
}

// 基础验证方法

// Required 必填验证
func (v *Validator) Required(field, value string) *Validator {
	if strings.TrimSpace(value) == "" {
		v.errors.Add(field, "不能为空")
	}
	return v
}

// MinLength 最小长度验证
func (v *Validator) MinLength(field, value string, min int) *Validator {
	if utf8.RuneCountInString(value) < min {
		v.errors.Add(field, fmt.Sprintf("长度不能少于%d个字符", min))
	}
	return v
}

// MaxLength 最大长度验证
func (v *Validator) MaxLength(field, value string, max int) *Validator {
	if utf8.RuneCountInString(value) > max {
		v.errors.Add(field, fmt.Sprintf("长度不能超过%d个字符", max))
	}
	return v
}

// Length 长度范围验证
func (v *Validator) Length(field, value string, min, max int) *Validator {
	length := utf8.RuneCountInString(value)
	if length < min || length > max {
		v.errors.Add(field, fmt.Sprintf("长度必须在%d-%d个字符之间", min, max))
	}
	return v
}

// Email 邮箱验证
func (v *Validator) Email(field, value string) *Validator {
	if value != "" && !IsValidEmail(value) {
		v.errors.Add(field, "邮箱格式不正确")
	}
	return v
}

// IP IP地址验证
func (v *Validator) IP(field, value string) *Validator {
	if value != "" && net.ParseIP(value) == nil {
		v.errors.Add(field, "IP地址格式不正确")
	}
	return v
}

// Domain 域名验证
func (v *Validator) Domain(field, value string) *Validator {
	if value != "" && !IsValidDomain(value) {
		v.errors.Add(field, "域名格式不正确")
	}
	return v
}

// URL URL验证
func (v *Validator) URL(field, value string) *Validator {
	if value != "" {
		if _, err := url.Parse(value); err != nil {
			v.errors.Add(field, "URL格式不正确")
		}
	}
	return v
}

// CVE CVE编号验证
func (v *Validator) CVE(field, value string) *Validator {
	if value != "" && !IsValidCVE(value) {
		v.errors.Add(field, "CVE编号格式不正确")
	}
	return v
}

// IntRange 整数范围验证
func (v *Validator) IntRange(field string, value, min, max int) *Validator {
	if value < min || value > max {
		v.errors.Add(field, fmt.Sprintf("值必须在%d-%d之间", min, max))
	}
	return v
}

// IntMin 整数最小值验证
func (v *Validator) IntMin(field string, value, min int) *Validator {
	if value < min {
		v.errors.Add(field, fmt.Sprintf("值不能小于%d", min))
	}
	return v
}

// IntMax 整数最大值验证
func (v *Validator) IntMax(field string, value, max int) *Validator {
	if value > max {
		v.errors.Add(field, fmt.Sprintf("值不能大于%d", max))
	}
	return v
}

// OneOf 枚举值验证
func (v *Validator) OneOf(field, value string, options []string) *Validator {
	if value != "" {
		for _, option := range options {
			if value == option {
				return v
			}
		}
		v.errors.Add(field, fmt.Sprintf("值必须是以下之一: %s", strings.Join(options, ", ")))
	}
	return v
}

// Regex 正则表达式验证
func (v *Validator) Regex(field, value, pattern, message string) *Validator {
	if value != "" {
		matched, err := regexp.MatchString(pattern, value)
		if err != nil {
			v.errors.Add(field, "正则表达式验证失败")
		} else if !matched {
			v.errors.Add(field, message)
		}
	}
	return v
}

// Custom 自定义验证
func (v *Validator) Custom(field string, fn func() (bool, string)) *Validator {
	if valid, message := fn(); !valid {
		v.errors.Add(field, message)
	}
	return v
}

// 业务相关验证方法

// Username 用户名验证
func (v *Validator) Username(field, value string) *Validator {
	if value == "" {
		v.errors.Add(field, "用户名不能为空")
		return v
	}
	
	// 长度验证
	if len(value) < 3 || len(value) > 50 {
		v.errors.Add(field, "用户名长度必须在3-50个字符之间")
		return v
	}
	
	// 格式验证：只允许字母、数字、下划线
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_]+$`, value)
	if !matched {
		v.errors.Add(field, "用户名只能包含字母、数字和下划线")
	}
	
	return v
}

// Password 密码验证
func (v *Validator) Password(field, value string) *Validator {
	if value == "" {
		v.errors.Add(field, "密码不能为空")
		return v
	}
	
	// 长度验证
	if len(value) < 6 || len(value) > 100 {
		v.errors.Add(field, "密码长度必须在6-100个字符之间")
		return v
	}
	
	// 复杂度验证（可选）
	// 这里可以添加更复杂的密码强度验证
	
	return v
}

// VulnID 漏洞ID验证
func (v *Validator) VulnID(field, value string) *Validator {
	if value == "" {
		v.errors.Add(field, "漏洞ID不能为空")
		return v
	}
	
	// 长度验证
	if len(value) > 100 {
		v.errors.Add(field, "漏洞ID长度不能超过100个字符")
	}
	
	return v
}

// Severity 严重程度验证
func (v *Validator) Severity(field, value string) *Validator {
	validSeverities := []string{"严重", "高危", "中危", "低危", "信息"}
	return v.OneOf(field, value, validSeverities)
}

// IOCType IOC类型验证
func (v *Validator) IOCType(field, value string) *Validator {
	validTypes := []string{"ip", "domain", "hash", "url"}
	return v.OneOf(field, value, validTypes)
}

// RiskLevel 风险等级验证
func (v *Validator) RiskLevel(field, value string) *Validator {
	validLevels := []string{"严重", "高危", "中危", "低危"}
	return v.OneOf(field, value, validLevels)
}

// UserRole 用户角色验证
func (v *Validator) UserRole(field, value string) *Validator {
	validRoles := []string{"admin", "user"}
	return v.OneOf(field, value, validRoles)
}

// PushChannelType 推送通道类型验证
func (v *Validator) PushChannelType(field, value string) *Validator {
	validTypes := []string{"wechat", "lark", "dingding", "webhook", "email"}
	return v.OneOf(field, value, validTypes)
}

// CrawlerType 采集器类型验证
func (v *Validator) CrawlerType(field, value string) *Validator {
	validTypes := []string{"chaitin", "qianxin", "aliyun", "threatbook", "seebug", "venustech", "oscs", "nvd"}
	return v.OneOf(field, value, validTypes)
}

// 分页参数验证
func (v *Validator) PaginationParams(page, pageSize int) *Validator {
	v.IntMin("page", page, 1)
	v.IntRange("pageSize", pageSize, 5, 100)
	return v
}

// 工具函数

// ValidateStruct 验证结构体（简化版本）
func ValidateStruct(data interface{}) error {
	// 这里可以实现基于反射的结构体验证
	// 或者集成第三方验证库如go-playground/validator
	return nil
}

// SanitizeInput 清理输入数据
func SanitizeInput(input string) string {
	// 移除前后空格
	input = strings.TrimSpace(input)
	
	// 清理特殊字符
	input = SanitizeString(input)
	
	return input
}

// ValidateAndSanitize 验证并清理字符串
func ValidateAndSanitize(field, value string, required bool, maxLen int) (string, error) {
	validator := NewValidator()
	
	// 清理输入
	value = SanitizeInput(value)
	
	// 验证
	if required {
		validator.Required(field, value)
	}
	
	if maxLen > 0 {
		validator.MaxLength(field, value, maxLen)
	}
	
	if validator.HasErrors() {
		return value, validator.Error()
	}
	
	return value, nil
}

// ValidateID 验证ID参数
func ValidateID(idStr string) (uint, error) {
	if idStr == "" {
		return 0, fmt.Errorf("ID不能为空")
	}
	
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return 0, fmt.Errorf("ID格式不正确")
	}
	
	if id == 0 {
		return 0, fmt.Errorf("ID不能为0")
	}
	
	return uint(id), nil
}

// ValidateIDs 验证ID列表
func ValidateIDs(idStrs []string) ([]uint, error) {
	if len(idStrs) == 0 {
		return []uint{}, nil
	}
	
	ids := make([]uint, 0, len(idStrs))
	for _, idStr := range idStrs {
		id, err := ValidateID(idStr)
		if err != nil {
			return nil, fmt.Errorf("ID %s 验证失败: %w", idStr, err)
		}
		ids = append(ids, id)
	}
	
	return ids, nil
}
