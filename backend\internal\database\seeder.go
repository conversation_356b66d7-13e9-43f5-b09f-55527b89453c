package database

import (
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// Seeder 数据填充器
type Seeder struct {
	db     *gorm.DB
	logger Logger
}

// NewSeeder 创建数据填充器
func NewSeeder(db *gorm.DB, logger Logger) *Seeder {
	return &Seeder{
		db:     db,
		logger: logger,
	}
}

// SeedDefaultData 填充默认数据
func (s *Seeder) SeedDefaultData() error {
	s.logger.Infof("正在检查并初始化默认数据...")
	
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 创建默认管理员用户
		if err := s.seedDefaultAdmin(tx); err != nil {
			return fmt.Errorf("创建默认管理员失败: %w", err)
		}
		
		// 创建默认RSS配置
		if err := s.seedDefaultRSSConfig(tx); err != nil {
			return fmt.Errorf("创建默认RSS配置失败: %w", err)
		}
		
		// 创建默认推送策略
		if err := s.seedDefaultPushPolicy(tx); err != nil {
			return fmt.Errorf("创建默认推送策略失败: %w", err)
		}
		
		// 创建默认推送白名单
		if err := s.seedDefaultPushWhitelist(tx); err != nil {
			return fmt.Errorf("创建默认推送白名单失败: %w", err)
		}
		
		// 创建默认生产策略
		if err := s.seedDefaultProductionStrategy(tx); err != nil {
			return fmt.Errorf("创建默认生产策略失败: %w", err)
		}

		// 创建默认采集器
		if err := s.seedDefaultCrawlers(tx); err != nil {
			return fmt.Errorf("创建默认采集器失败: %w", err)
		}

		// 创建默认数据接口
		if err := s.seedDefaultDataInterfaces(tx); err != nil {
			return fmt.Errorf("创建默认数据接口失败: %w", err)
		}

		return nil
	})
}

// seedDefaultAdmin 创建默认管理员用户
func (s *Seeder) seedDefaultAdmin(tx *gorm.DB) error {
	// 检查是否已有管理员用户
	var adminCount int64
	if err := tx.Table("users").Where("role = ?", "admin").Count(&adminCount).Error; err != nil {
		return fmt.Errorf("检查管理员用户失败: %w", err)
	}
	
	if adminCount > 0 {
		s.logger.Infof("已存在 %d 个管理员用户，跳过创建", adminCount)
		return nil
	}
	
	s.logger.Infof("创建默认管理员用户...")
	
	// 加密密码
	hashedPassword, err := s.hashPassword("admin123")
	if err != nil {
		return fmt.Errorf("密码加密失败: %w", err)
	}
	
	// 生成API密钥
	apiKey, err := s.generateAPIKey()
	if err != nil {
		return fmt.Errorf("生成API密钥失败: %w", err)
	}
	
	// 创建管理员用户
	admin := map[string]interface{}{
		"username":   "admin",
		"password":   hashedPassword,
		"email":      "<EMAIL>",
		"role":       "admin",
		"status":     true,
		"api_key":    apiKey,
		"created_at": time.Now().Unix(),
		"updated_at": time.Now().Unix(),
	}
	
	if err := tx.Table("users").Create(&admin).Error; err != nil {
		return fmt.Errorf("创建管理员用户失败: %w", err)
	}
	
	s.logger.Infof("默认管理员用户创建成功，用户名: admin, 密码: admin123")
	return nil
}

// seedDefaultRSSConfig 创建默认RSS配置
func (s *Seeder) seedDefaultRSSConfig(tx *gorm.DB) error {
	// 检查是否已有RSS配置
	var rssCount int64
	if err := tx.Table("rss_configs").Count(&rssCount).Error; err != nil {
		return fmt.Errorf("检查RSS配置失败: %w", err)
	}
	
	if rssCount > 0 {
		s.logger.Infof("已存在 %d 个RSS配置，跳过创建", rssCount)
		return nil
	}
	
	s.logger.Infof("创建默认RSS配置...")
	
	rssConfig := map[string]interface{}{
		"enabled":          true,
		"require_auth":     false,
		"title":            "漏洞情报管理平台 - RSS订阅",
		"description":      "最新漏洞信息",
		"item_count":       50,
		"include_severity": "严重,高危,中危,低危,信息",
		"exclude_tags":     "",
		"created_at":       time.Now().Unix(),
		"updated_at":       time.Now().Unix(),
	}
	
	if err := tx.Table("rss_configs").Create(&rssConfig).Error; err != nil {
		return fmt.Errorf("创建RSS配置失败: %w", err)
	}
	
	s.logger.Infof("默认RSS配置创建成功")
	return nil
}

// seedDefaultPushPolicy 创建默认推送策略
func (s *Seeder) seedDefaultPushPolicy(tx *gorm.DB) error {
	// 检查是否已有默认推送策略
	var policyCount int64
	if err := tx.Table("push_policies").Where("is_default = ?", true).Count(&policyCount).Error; err != nil {
		return fmt.Errorf("检查推送策略失败: %w", err)
	}
	
	if policyCount > 0 {
		s.logger.Infof("已存在 %d 个默认推送策略，跳过创建", policyCount)
		return nil
	}
	
	s.logger.Infof("创建默认推送策略...")
	
	policy := map[string]interface{}{
		"name":        "默认推送策略",
		"description": "系统默认推送策略",
		"channel_ids": "",
		"is_default":  true,
		"created_at":  time.Now().Unix(),
		"updated_at":  time.Now().Unix(),
	}
	
	if err := tx.Table("push_policies").Create(&policy).Error; err != nil {
		return fmt.Errorf("创建推送策略失败: %w", err)
	}
	
	s.logger.Infof("默认推送策略创建成功")
	return nil
}

// seedDefaultPushWhitelist 创建默认推送白名单
func (s *Seeder) seedDefaultPushWhitelist(tx *gorm.DB) error {
	// 检查是否已有推送白名单
	var whitelistCount int64
	if err := tx.Table("push_whitelists").Count(&whitelistCount).Error; err != nil {
		return fmt.Errorf("检查推送白名单失败: %w", err)
	}
	
	if whitelistCount > 0 {
		s.logger.Infof("已存在 %d 个推送白名单配置，跳过创建", whitelistCount)
		return nil
	}
	
	s.logger.Infof("创建默认推送白名单配置...")
	
	// 获取默认策略ID
	var policyID uint
	if err := tx.Table("push_policies").Where("is_default = ?", true).Select("id").Scan(&policyID).Error; err != nil {
		return fmt.Errorf("获取默认策略ID失败: %w", err)
	}
	
	whitelist := map[string]interface{}{
		"keywords":    "CVE,远程代码执行,RCE,任意代码执行,ACE",
		"auto_push":   true,
		"policy_id":   policyID,
		"description": "系统默认白名单配置，匹配关键漏洞自动推送",
		"created_at":  time.Now().Unix(),
		"updated_at":  time.Now().Unix(),
	}
	
	if err := tx.Table("push_whitelists").Create(&whitelist).Error; err != nil {
		return fmt.Errorf("创建推送白名单失败: %w", err)
	}
	
	s.logger.Infof("默认推送白名单配置创建成功")
	return nil
}

// seedDefaultProductionStrategy 创建默认生产策略
func (s *Seeder) seedDefaultProductionStrategy(tx *gorm.DB) error {
	// 检查是否已有生产策略
	var strategyCount int64
	if err := tx.Table("production_strategies").Count(&strategyCount).Error; err != nil {
		return fmt.Errorf("检查生产策略失败: %w", err)
	}
	
	if strategyCount > 0 {
		s.logger.Infof("已存在 %d 个生产策略，跳过创建", strategyCount)
		return nil
	}
	
	s.logger.Infof("创建默认生产策略...")
	
	strategy := map[string]interface{}{
		"name":                    "默认生产策略",
		"description":             "系统默认的IOC情报生产策略，用于从数据源生成威胁情报",
		"status":                  "enabled",
		"attack_count_threshold":  3,
		"threat_score_threshold":  2,
		"enable_threat_scoring":   true,
		"risk_level_filter":       "严重,高危,中危",
		"default_validity_days":   30,   // 默认有效期30天
		"enable_validity_control": true, // 启用有效期控制
		"schedule_enabled":        false,
		"schedule_interval":       60,
		"last_run_time":           nil,
		"next_run_time":           nil,
		"time_range_hours":        24,
		"allowed_countries":       "",
		"blocked_countries":       "",
		"allowed_ip_ranges":       "",
		"blocked_ip_ranges":       "",
		"created_by":              "system",
		"created_at":              time.Now().Unix(),
		"updated_at":              time.Now().Unix(),
	}
	
	if err := tx.Table("production_strategies").Create(&strategy).Error; err != nil {
		return fmt.Errorf("创建生产策略失败: %w", err)
	}
	
	s.logger.Infof("默认生产策略创建成功")
	return nil
}

// seedDefaultCrawlers 创建默认采集器
func (s *Seeder) seedDefaultCrawlers(tx *gorm.DB) error {
	// 检查是否已有采集器
	var crawlerCount int64
	if err := tx.Table("crawlers").Count(&crawlerCount).Error; err != nil {
		return fmt.Errorf("检查采集器失败: %w", err)
	}

	if crawlerCount > 0 {
		s.logger.Infof("已存在 %d 个采集器，跳过创建", crawlerCount)
		return nil
	}

	s.logger.Infof("创建默认采集器...")

	// 定义默认采集器配置（参考V1版本，所有采集器默认启用，默认手动执行）
	defaultCrawlers := []map[string]interface{}{
		{
			"name":        "长亭漏洞库",
			"type":        "chaitin",
			"description": "长亭科技漏洞库，提供最新的安全漏洞信息",
			"config":      `{"pageLimit":1}`,
			"interval":    "manual", // V1版本默认手动执行
			"status":      true,     // V1版本默认启用
			"created_at":  time.Now().Unix(),
			"updated_at":  time.Now().Unix(),
		},
		{
			"name":        "奇安信威胁情报中心",
			"type":        "qianxin",
			"description": "奇安信威胁情报中心，提供最新的安全漏洞信息",
			"config":      `{"pageLimit":1}`,
			"interval":    "manual", // V1版本默认手动执行
			"status":      true,     // V1版本默认启用
			"created_at":  time.Now().Unix(),
			"updated_at":  time.Now().Unix(),
		},
		{
			"name":        "阿里云漏洞库",
			"type":        "aliyun",
			"description": "阿里云漏洞库，提供最新的安全漏洞信息",
			"config":      `{"pageLimit":1}`,
			"interval":    "manual", // V1版本默认手动执行
			"status":      true, // V1版本默认启用
			"created_at":  time.Now().Unix(),
			"updated_at":  time.Now().Unix(),
		},
		{
			"name":        "微步威胁情报",
			"type":        "threatbook",
			"description": "微步威胁情报，提供最新的安全漏洞信息",
			"config":      `{"pageLimit":1}`,
			"interval":    "manual", // V1版本默认手动执行
			"status":      true,     // V1版本默认启用
			"created_at":  time.Now().Unix(),
			"updated_at":  time.Now().Unix(),
		},
		{
			"name":        "Seebug漏洞平台",
			"type":        "seebug",
			"description": "知道创宇Seebug漏洞平台，提供最新的安全漏洞信息",
			"config":      `{"pageLimit":1}`,
			"interval":    "manual", // V1版本默认手动执行
			"status":      true,     // V1版本默认启用
			"created_at":  time.Now().Unix(),
			"updated_at":  time.Now().Unix(),
		},
		{
			"name":        "启明星辰漏洞通告",
			"type":        "venustech",
			"description": "启明星辰漏洞通告，提供最新的安全漏洞信息",
			"config":      `{"pageLimit":1}`,
			"interval":    "manual", // V1版本默认手动执行
			"status":      true,     // V1版本默认启用
			"created_at":  time.Now().Unix(),
			"updated_at":  time.Now().Unix(),
		},
		{
			"name":        "OSCS开源安全情报预警",
			"type":        "oscs",
			"description": "OSCS开源安全情报预警，提供最新的安全漏洞信息",
			"config":      `{"pageLimit":1}`,
			"interval":    "manual", // V1版本默认手动执行
			"status":      true,     // V1版本默认启用
			"created_at":  time.Now().Unix(),
			"updated_at":  time.Now().Unix(),
		},
		{
			"name":        "NVD国家漏洞数据库",
			"type":        "nvd",
			"description": "NVD国家漏洞数据库，提供最新的安全漏洞信息",
			"config":      `{"pageLimit":1}`, // V1版本不需要API密钥配置
			"interval":    "manual",          // V1版本默认手动执行
			"status":      true,              // V1版本默认启用
			"created_at":  time.Now().Unix(),
			"updated_at":  time.Now().Unix(),
		},
	}

	// 批量创建采集器
	for _, crawler := range defaultCrawlers {
		if err := tx.Table("crawlers").Create(&crawler).Error; err != nil {
			return fmt.Errorf("创建采集器 %s 失败: %w", crawler["name"], err)
		}
	}

	s.logger.Infof("默认采集器创建成功，共创建 %d 个采集器", len(defaultCrawlers))
	return nil
}

// seedDefaultDataInterfaces 创建默认数据接口
func (s *Seeder) seedDefaultDataInterfaces(tx *gorm.DB) error {
	// 检查是否已有数据接口
	var interfaceCount int64
	if err := tx.Table("data_interfaces").Count(&interfaceCount).Error; err != nil {
		return fmt.Errorf("检查数据接口失败: %w", err)
	}

	if interfaceCount > 0 {
		s.logger.Infof("已存在 %d 个数据接口，跳过创建", interfaceCount)
		return nil
	}

	s.logger.Infof("创建默认数据接口...")

	// 创建默认CCCC黑科技接口配置
	config := map[string]interface{}{
		"host":     "**********:5443",
		"user_key": "",
	}

	configJSON, err := s.marshalJSON(config)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}

	// 创建默认数据接口
	defaultInterface := map[string]interface{}{
		"name":        "中交黑科技威胁情报接口",
		"type":        "cccc_black_tech",
		"description": "从中交黑科技平台采集威胁情报数据，自动分析攻击流并生成IOC情报",
		"config":      configJSON,
		"status":      "disabled", // 默认禁用，需要用户配置后启用
		"interval":    3600,       // 1小时
		"last_run_at": 0,
		"created_at":  time.Now().Unix(),
		"updated_at":  time.Now().Unix(),
	}

	if err := tx.Table("data_interfaces").Create(&defaultInterface).Error; err != nil {
		return fmt.Errorf("创建默认数据接口失败: %w", err)
	}

	s.logger.Infof("默认数据接口创建成功")
	return nil
}

// 辅助方法

// hashPassword 加密密码
func (s *Seeder) hashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

// generateAPIKey 生成API密钥
func (s *Seeder) generateAPIKey() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// marshalJSON 序列化为JSON字符串
func (s *Seeder) marshalJSON(data interface{}) (string, error) {
	bytes, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}
