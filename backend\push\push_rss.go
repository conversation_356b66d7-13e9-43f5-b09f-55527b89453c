package push

import (
	"encoding/xml"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"vulnerability_push/internal/models"
)

// RSS相关常量
const TypeRss = "rss"

// RSS相关结构体
type RssItem struct {
	XMLName     xml.Name `xml:"item"`
	Title       string   `xml:"title"`
	Link        string   `xml:"link"`
	Description string   `xml:"description"`
	PubDate     string   `xml:"pubDate"`
	GUID        string   `xml:"guid"`
	Category    string   `xml:"category,omitempty"`
}

type RssFeed struct {
	XMLName  xml.Name   `xml:"rss"`
	Version  string     `xml:"version,attr"`
	Channel  RssChannel `xml:"channel"`
}

type RssChannel struct {
	XMLName       xml.Name  `xml:"channel"`
	Title         string    `xml:"title"`
	Link          string    `xml:"link"`
	Description   string    `xml:"description"`
	Language      string    `xml:"language"`
	LastBuildDate string    `xml:"lastBuildDate"`
	Items         []RssItem `xml:"item"`
}

// 获取RSS配置
func GetRssConfig(c *gin.Context) {
	var config RssConfig
	result := DB.First(&config)
	if result.Error != nil {
		// 如果没有找到配置，则返回空对象
		config = RssConfig{
			Enabled:         true,
			RequireAuth:     false,
			Title:           "威胁情报管理平台 - 漏洞订阅",
			Description:     "最新安全漏洞信息订阅",
			ItemCount:       50,
			IncludeSeverity: "高,中,低",
			ExcludeTags:     "",
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"msg": "获取RSS配置成功",
		"data": gin.H{
			"id":              config.ID,
			"enabled":         config.Enabled,
			"requireAuth":     config.RequireAuth,
			"title":          config.Title,
			"description":    config.Description,
			"itemCount":      config.ItemCount,
			"includeSeverity": config.IncludeSeverity,
			"excludeTags":    config.ExcludeTags,
			"createdAt":      config.CreatedAt,
			"updatedAt":      config.UpdatedAt,
		},
	})
}

// 更新RSS配置
func UpdateRssConfig(c *gin.Context) {
	var req struct {
		Enabled         bool   `json:"enabled"`
		RequireAuth     bool   `json:"requireAuth"`
		Title           string `json:"title"`
		Description     string `json:"description"`
		ItemCount       int    `json:"itemCount"`
		IncludeSeverity string `json:"includeSeverity"`
		ExcludeTags     string `json:"excludeTags"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"msg": "参数错误", "error": err.Error()})
		return
	}

	var config RssConfig
	result := DB.First(&config)

	if result.Error != nil {
		// 如果不存在则创建
		config = RssConfig{
			Enabled:         req.Enabled,
			RequireAuth:     req.RequireAuth,
			Title:           req.Title,
			Description:     req.Description,
			ItemCount:       req.ItemCount,
			IncludeSeverity: req.IncludeSeverity,
			ExcludeTags:     req.ExcludeTags,
		}
		if err := DB.Create(&config).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"msg": "创建RSS配置失败", "error": err.Error()})
			return
		}
	} else {
		// 更新现有记录
		config.Enabled = req.Enabled
		config.RequireAuth = req.RequireAuth
		config.Title = req.Title
		config.Description = req.Description
		config.ItemCount = req.ItemCount
		config.IncludeSeverity = req.IncludeSeverity
		config.ExcludeTags = req.ExcludeTags
		config.UpdatedAt = time.Now().Unix()

		if err := DB.Save(&config).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"msg": "更新RSS配置失败", "error": err.Error()})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"msg": "更新RSS配置成功",
		"data": gin.H{
			"id":              config.ID,
			"enabled":         config.Enabled,
			"requireAuth":     config.RequireAuth,
			"title":          config.Title,
			"description":    config.Description,
			"itemCount":      config.ItemCount,
			"includeSeverity": config.IncludeSeverity,
			"excludeTags":    config.ExcludeTags,
		},
	})
}

// 生成RSS订阅
func GenerateRssFeed(c *gin.Context) {
	// 获取RSS配置
	var config RssConfig
	if err := DB.First(&config).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"msg": "获取RSS配置失败"})
		return
	}

	// 检查是否启用RSS
	if !config.Enabled {
		c.JSON(http.StatusForbidden, gin.H{"msg": "RSS订阅功能已禁用"})
		return
	}

	// 检查认证
	if config.RequireAuth {
		apiKey := c.Query("key")
		if apiKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"msg": "需要API密钥访问RSS"})
			return
		}

		// 验证API密钥
		var user models.User
		if err := DB.Where("api_key = ?", apiKey).First(&user).Error; err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"msg": "无效的API密钥"})
			return
		}

		// 检查用户状态
		if !user.Status {
			c.JSON(http.StatusForbidden, gin.H{"msg": "用户已禁用"})
			return
		}
	}

	// 先获取所有漏洞数据，不应用过滤条件
	var allVulnerabilities []Vulnerability
	if err := DB.Model(&Vulnerability{}).Order("created_at DESC").Limit(200).Find(&allVulnerabilities).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"msg": "获取所有漏洞数据失败"})
		return
	}
	
	Infof("RSS: 数据库中总共找到 %d 条漏洞数据", len(allVulnerabilities))
	
	// 打印RSS配置信息
	Infof("RSS配置: 启用=%v, 需要认证=%v, 标题=%s", config.Enabled, config.RequireAuth, config.Title)
	Infof("RSS配置: 包含严重程度=%s", config.IncludeSeverity)
	Infof("RSS配置: 排除标签=%s", config.ExcludeTags)
	
	// 打印所有漏洞的严重程度，查看实际值
	severityMap := make(map[string]int)
	for _, vuln := range allVulnerabilities {
		severityMap[vuln.Severity]++
	}
	
	Infof("RSS: 漏洞严重程度统计:")
	for severity, count := range severityMap {
		Infof("RSS: - %s: %d条", severity, count)
	}

	// 应用过滤条件
	var filteredVulnerabilities []Vulnerability
	
	if config.IncludeSeverity != "" {
		Infof("RSS: 配置的严重程度过滤: %s", config.IncludeSeverity)
		severities := strings.Split(config.IncludeSeverity, ",")
		
		// 严重程度映射表，处理前端和数据库中存储值的差异
		severityMap := map[string][]string{
			"严重": {"严重"},
			"高危": {"高危", "高"},
			"中危": {"中危", "中"},
			"低危": {"低危", "低"},
			"信息": {"信息"},
		}
		
		// 手动过滤，而不是使用SQL查询
		for _, vuln := range allVulnerabilities {
			// 检查漏洞的严重程度是否在包含列表中
			included := false
			for _, sev := range severities {
				sev = strings.TrimSpace(sev)
				
				// 直接匹配
				if strings.EqualFold(vuln.Severity, sev) {
					included = true
					break
				}
				
				// 使用映射表匹配
				if mappedSeverities, ok := severityMap[sev]; ok {
					for _, mappedSev := range mappedSeverities {
						if strings.EqualFold(vuln.Severity, mappedSev) {
							included = true
							break
						}
					}
					if included {
						break
					}
				}
			}
			
			if included {
				// 检查是否需要排除基于标签
				excluded := false
				if config.ExcludeTags != "" {
					excludeTags := strings.Split(config.ExcludeTags, ",")
					for _, tag := range excludeTags {
						tag = strings.TrimSpace(tag)
						if tag != "" && strings.Contains(vuln.Tags, tag) {
							excluded = true
							break
						}
					}
				}
				
				if !excluded {
					filteredVulnerabilities = append(filteredVulnerabilities, vuln)
				}
			}
		}
	} else {
		// 如果没有指定严重程度，则包含所有漏洞
		filteredVulnerabilities = allVulnerabilities
	}
	
	Infof("RSS: 过滤后剩余 %d 条漏洞数据", len(filteredVulnerabilities))
	
	// 限制条目数量
	vulnerabilities := filteredVulnerabilities
	if len(vulnerabilities) > config.ItemCount {
		vulnerabilities = vulnerabilities[:config.ItemCount]
	}

	// 构建RSS订阅
	baseURL := c.Request.Host
	scheme := "http"
	if c.Request.TLS != nil {
		scheme = "https"
	}
	baseURL = fmt.Sprintf("%s://%s", scheme, baseURL)

	feed := RssFeed{
		Version: "2.0",
		Channel: RssChannel{
			Title:         config.Title,
			Link:          baseURL,
			Description:   config.Description,
			Language:      "zh-cn",
			LastBuildDate: time.Now().Format(time.RFC1123Z),
			Items:         make([]RssItem, 0),
		},
	}

	// 添加漏洞条目
	for _, vuln := range vulnerabilities {
		pubDate := time.Unix(vuln.CreatedAt, 0).Format(time.RFC1123Z)
		link := fmt.Sprintf("%s/vulnerabilities/%d", baseURL, vuln.ID)
		
		// 构建描述
		description := fmt.Sprintf("<p><strong>漏洞编号:</strong> %s</p>", vuln.VulnID)
		description += fmt.Sprintf("<p><strong>严重程度:</strong> %s</p>", vuln.Severity)
		if vuln.DisclosureDate != "" {
			description += fmt.Sprintf("<p><strong>披露日期:</strong> %s</p>", vuln.DisclosureDate)
		}
		if vuln.Description != "" {
			description += fmt.Sprintf("<p><strong>描述:</strong> %s</p>", vuln.Description)
		}
		if vuln.Remediation != "" {
			description += fmt.Sprintf("<p><strong>修复建议:</strong> %s</p>", vuln.Remediation)
		}
		
		tags := vuln.GetTags()
		if len(tags) > 0 {
			description += "<p><strong>标签:</strong> "
			for i, tag := range tags {
				if i > 0 {
					description += ", "
				}
				description += tag
			}
			description += "</p>"
		}
		
		refs := vuln.GetReferences()
		if len(refs) > 0 {
			description += "<p><strong>参考链接:</strong></p><ul>"
			for _, ref := range refs {
				description += fmt.Sprintf("<li><a href=\"%s\">%s</a></li>", ref, ref)
			}
			description += "</ul>"
		}

		item := RssItem{
			Title:       vuln.Name,
			Link:        link,
			Description: description,
			PubDate:     pubDate,
			GUID:        link,
			Category:    vuln.Severity,
		}

		feed.Channel.Items = append(feed.Channel.Items, item)
	}

	Infof("RSS: 生成了 %d 条RSS条目", len(feed.Channel.Items))

	// 设置响应头
	c.Header("Content-Type", "application/xml; charset=utf-8")
	
	// 生成XML
	output, err := xml.MarshalIndent(feed, "", "  ")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"msg": "生成RSS失败"})
		return
	}
	
	// 添加XML声明
	xmlHeader := []byte("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n")
	output = append(xmlHeader, output...)
	
	c.Data(http.StatusOK, "application/xml", output)
} 