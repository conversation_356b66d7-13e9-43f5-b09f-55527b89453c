package push

import (
	"encoding/json"
	"gorm.io/gorm"
)

var (
	// DB 全局数据库实例
	DB *gorm.DB
)

// ContainsSensitiveWordsFunc 敏感词检查函数类型
type ContainsSensitiveWordsFunc func(content string) (bool, []string)

// ContainsSensitiveWords 敏感词检查函数，由主程序设置实现
var ContainsSensitiveWords ContainsSensitiveWordsFunc = func(content string) (bool, []string) {
	// 默认实现，返回没有敏感词
	return false, nil
}

// PushChannel 推送通道模型
type PushChannel struct {
	ID          uint   `gorm:"primaryKey" json:"id"`
	Name        string `gorm:"size:50;not null" json:"name"`
	Type        string `gorm:"size:20;not null" json:"type"`
	Description string `gorm:"size:200" json:"description"`
	Config      string `gorm:"type:text" json:"-"` // 配置信息，JSON格式
	Status      bool   `gorm:"default:true" json:"status"`
	IsEnabled   bool   `gorm:"default:true" json:"isEnabled"`
	CreatedAt   int64  `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt   int64  `gorm:"autoUpdateTime" json:"updatedAt"`
}

// GetConfig 获取通道配置
func (c *PushChannel) GetConfig() map[string]interface{} {
	if c.Config == "" {
		return make(map[string]interface{})
	}

	var config map[string]interface{}
	if err := json.Unmarshal([]byte(c.Config), &config); err != nil {
		return make(map[string]interface{})
	}

	if config == nil {
		return make(map[string]interface{})
	}

	return config
}

// PushRecord 推送记录模型
type PushRecord struct {
	ID              uint   `gorm:"primaryKey" json:"id"`
	VulnerabilityID uint   `gorm:"not null;index" json:"vulnerabilityID"`
	ChannelID       uint   `gorm:"not null;index" json:"channelID"`
	PolicyID        uint   `gorm:"index" json:"policyID"`
	Status          string `gorm:"size:20;not null" json:"status"` // success, failed, pending
	ErrorMessage    string `gorm:"type:text" json:"errorMessage"`
	CreatedAt       int64  `gorm:"autoCreateTime" json:"createdAt"`
	PushedAt        int64  `json:"pushedAt"`
}

// RssConfig RSS配置
type RssConfig struct {
	ID              uint   `gorm:"primaryKey" json:"id"`
	Enabled         bool   `gorm:"default:true" json:"enabled"`
	RequireAuth     bool   `gorm:"default:false" json:"requireAuth"`
	Title           string `gorm:"size:100;not null" json:"title"`
	Description     string `gorm:"size:200" json:"description"`
	ItemCount       int    `gorm:"default:50" json:"itemCount"`
	IncludeSeverity string `gorm:"type:text" json:"includeSeverity"` // 逗号分隔的严重程度
	ExcludeTags     string `gorm:"type:text" json:"excludeTags"`     // 逗号分隔的排除标签
	CreatedAt       int64  `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt       int64  `gorm:"autoUpdateTime" json:"updatedAt"`
}

// Vulnerability 漏洞信息
type Vulnerability struct {
	ID             uint   `gorm:"primaryKey" json:"id"`
	Name           string `gorm:"size:200;not null" json:"name"`
	VulnID         string `gorm:"size:50;not null;uniqueIndex" json:"vulnId"`
	Severity       string `gorm:"size:20;not null" json:"severity"`
	DisclosureDate string `gorm:"size:20" json:"disclosureDate"`
	Source         string `gorm:"size:200" json:"source"`
	Description    string `gorm:"type:text" json:"description"`
	Remediation    string `gorm:"type:text" json:"remediation"`
	References     string `gorm:"type:text" json:"references"`
	Tags           string `gorm:"type:text" json:"tags"`
	PushReason     string `gorm:"type:text" json:"pushReason"`
	Status         string `gorm:"size:20;not null" json:"status"`
	CreatedAt      int64  `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt      int64  `gorm:"autoUpdateTime" json:"updatedAt"`
}



// GetReferences 获取参考链接列表
func (v *Vulnerability) GetReferences() []string {
	if v.References == "" {
		return []string{}
	}
	
	// 按行分割
	var refs []string
	for _, line := range splitLines(v.References) {
		if line != "" {
			refs = append(refs, line)
		}
	}
	
	return refs
}

// GetTags 获取标签列表
func (v *Vulnerability) GetTags() []string {
	if v.Tags == "" {
		return []string{}
	}

	// 按逗号分割
	var tags []string
	for _, tag := range splitByComma(v.Tags) {
		if tag != "" {
			tags = append(tags, tag)
		}
	}

	return tags
}

// IOCIntelligence IOC情报信息（用于推送）
type IOCIntelligence struct {
	ID            uint     `json:"id"`
	IOC           string   `json:"ioc"`
	IOCType       string   `json:"iocType"`
	Location      string   `json:"location"`
	Type          string   `json:"type"`
	RiskLevel     string   `json:"riskLevel"`
	HitCount      int      `json:"hitCount"`
	Description   string   `json:"description"`
	Source        string   `json:"source"`
	DiscoveryDate string   `json:"discoveryDate"`
	Tags          []string `json:"tags"`
	PushReason    string   `json:"pushReason"`
}

// IOCIntelligencePushRecord IOC情报推送记录（用于推送）
type IOCIntelligencePushRecord struct {
	ID                   uint   `json:"id"`
	IOCIntelligenceID    uint   `json:"iocIntelligenceId"`
	ChannelID            uint   `json:"channelId"`
	Status               string `json:"status"`
	ErrorMessage         string `json:"errorMessage"`
	PushedAt             int64  `json:"pushedAt"`
}