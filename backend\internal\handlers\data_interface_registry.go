package handlers

import (
	"sync"

	"vulnerability_push/internal/models"
	"vulnerability_push/internal/utils"
)

// DataInterfaceExecutor 数据接口执行器接口
// 所有数据接口都需要实现这个接口
type DataInterfaceExecutor interface {
	// Execute 执行数据接口采集
	Execute(dataInterface *models.DataInterface, config map[string]interface{}, log *models.DataInterfaceLog) error
	
	// GetType 获取接口类型
	GetType() string
	
	// GetDescription 获取接口描述
	GetDescription() string
	
	// ValidateConfig 验证配置
	ValidateConfig(config map[string]interface{}) error
	
	// GetConfigSchema 获取配置模式（用于前端动态生成配置表单）
	GetConfigSchema() map[string]interface{}
}

// DataInterfaceRegistry 数据接口注册器
// 管理所有可用的数据接口类型
type DataInterfaceRegistry struct {
	interfaces map[string]DataInterfaceExecutor
	mutex      sync.RWMutex
}

// NewDataInterfaceRegistry 创建数据接口注册器
func NewDataInterfaceRegistry() *DataInterfaceRegistry {
	return &DataInterfaceRegistry{
		interfaces: make(map[string]DataInterfaceExecutor),
	}
}

// Register 注册数据接口
func (r *DataInterfaceRegistry) Register(interfaceType string, executor DataInterfaceExecutor) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	r.interfaces[interfaceType] = executor
	utils.Infof("注册数据接口: %s - %s", interfaceType, executor.GetDescription())
}

// Get 获取数据接口执行器
func (r *DataInterfaceRegistry) Get(interfaceType string) (DataInterfaceExecutor, bool) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	executor, exists := r.interfaces[interfaceType]
	return executor, exists
}

// GetAll 获取所有注册的数据接口
func (r *DataInterfaceRegistry) GetAll() map[string]DataInterfaceExecutor {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	result := make(map[string]DataInterfaceExecutor)
	for k, v := range r.interfaces {
		result[k] = v
	}
	return result
}

// GetTypes 获取所有接口类型列表
func (r *DataInterfaceRegistry) GetTypes() []string {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	types := make([]string, 0, len(r.interfaces))
	for interfaceType := range r.interfaces {
		types = append(types, interfaceType)
	}
	return types
}

// ValidateType 验证接口类型是否存在
func (r *DataInterfaceRegistry) ValidateType(interfaceType string) bool {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	_, exists := r.interfaces[interfaceType]
	return exists
}

// GetInterfaceInfo 获取接口信息
func (r *DataInterfaceRegistry) GetInterfaceInfo(interfaceType string) map[string]interface{} {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	executor, exists := r.interfaces[interfaceType]
	if !exists {
		return nil
	}
	
	return map[string]interface{}{
		"type":         interfaceType,
		"description":  executor.GetDescription(),
		"configSchema": executor.GetConfigSchema(),
	}
}

// GetAllInterfaceInfo 获取所有接口信息
func (r *DataInterfaceRegistry) GetAllInterfaceInfo() []map[string]interface{} {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	result := make([]map[string]interface{}, 0, len(r.interfaces))
	for interfaceType, executor := range r.interfaces {
		result = append(result, map[string]interface{}{
			"type":         interfaceType,
			"description":  executor.GetDescription(),
			"configSchema": executor.GetConfigSchema(),
		})
	}
	return result
}
