<template>
  <div class="ioc-intelligence-production-container">
    <div class="header">
      <h2>IOC情报生产管理</h2>
    </div>
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="IOC值">
          <el-input
            v-model="filterForm.ioc"
            placeholder="输入IOC值"
            clearable
            prefix-icon="Search"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="IOC类型">
          <el-select 
            v-model="filterForm.iocType" 
            placeholder="全部" 
            clearable
            style="width: 120px;"
          >
            <el-option label="IP" value="ip" />
            <el-option label="域名" value="domain" />
          </el-select>
        </el-form-item>
        <el-form-item label="风险等级">
          <el-select 
            v-model="filterForm.riskLevel" 
            placeholder="全部" 
            clearable
            style="width: 180px;"
          >
            <el-option label="严重" value="严重" />
            <el-option label="高危" value="高危" />
            <el-option label="中危" value="中危" />
            <el-option label="低危" value="低危" />
            <el-option label="信息" value="信息" />
          </el-select>
        </el-form-item>
        <el-form-item label="情报类型">
          <el-select
            v-model="filterForm.type"
            placeholder="选择类型"
            clearable
            filterable
          >
            <el-option label="僵尸网络" value="僵尸网络" />
            <el-option label="恶意扫描" value="恶意扫描" />
            <el-option label="网络攻击" value="网络攻击" />
            <el-option label="勒索软件" value="勒索软件" />
            <el-option label="钓鱼网站" value="钓鱼网站" />
            <el-option label="挖矿木马" value="挖矿木马" />
            <el-option label="代理服务" value="代理服务" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <!-- 被攻击单位字段已移除，替换为推送状态 -->
        <el-form-item label="发现日期">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item label="关键词">
          <el-input
            v-model="filterForm.keyword"
            placeholder="搜索IOC值、描述等"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
          <el-button @click="handleReset" :icon="RefreshLeft">重置</el-button>
          <el-button @click="fetchIOCIntelligence" :icon="Refresh">刷新</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card class="table-card">
      <div class="table-header">
        <div class="table-title">IOC情报列表</div>
        <div class="table-actions">
          <el-button 
            v-if="isAdmin" 
            type="primary" 
            @click="handleCreate" 
            :icon="Plus"
          >
            新增IOC情报
          </el-button>
          <el-button
            v-if="isAdmin"
            type="success"
            @click="handleBatchPush"
            :icon="Promotion"
            :disabled="multipleSelection.length === 0"
          >
            批量推送 ({{ multipleSelection.length }})
          </el-button>
          <el-button
            v-if="isAdmin"
            type="danger"
            @click="handleBatchDelete"
            :icon="Delete"
            :disabled="multipleSelection.length === 0"
          >
            批量删除 ({{ multipleSelection.length }})
          </el-button>
          <el-button @click="handleExport" :icon="Download">导出</el-button>
        </div>
      </div>

      <el-table
        :data="iocIntelligence"
        border
        style="width: 100%"
        v-loading="loading"
        row-key="id"
        stripe
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          textAlign: 'center',
          fontWeight: '500',
          writingMode: 'horizontal-tb',
          whiteSpace: 'nowrap'
        }"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="ioc" label="IOC值" width="150" show-overflow-tooltip sortable />
        <el-table-column prop="iocType" label="IOC类型" width="80">
          <template #default="scope">
            <el-tag size="small" :type="scope.row.iocType === 'ip' ? 'primary' : 'success'">
              {{ scope.row.iocType === 'ip' ? 'IP' : '域名' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="location" label="地理位置" width="120" show-overflow-tooltip />
        <el-table-column prop="type" label="情报类型" width="100">
          <template #default="scope">
            <el-tag size="small" effect="plain">{{ scope.row.type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="riskLevel" label="风险等级" width="100">
          <template #default="scope">
            <el-tag size="small" :type="getRiskLevelType(scope.row.riskLevel)" effect="dark">
              {{ scope.row.riskLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="hitCount" label="命中次数" width="120" sortable />
        <el-table-column label="推送时间" width="160" show-overflow-tooltip>
          <template #default="scope">
            <div v-if="scope.row.pushedAt && scope.row.pushedAt > 0" class="push-time-container">
              <div class="push-time-text">
                {{ formatTimestamp(scope.row.pushedAt) }}
              </div>
              <el-tag
                v-if="scope.row.pushStatus === 'failed'"
                size="small"
                type="danger"
                effect="plain"
                class="push-status-tag"
              >
                失败
              </el-tag>
            </div>
            <span v-else class="no-push-text">未推送</span>
          </template>
        </el-table-column>
        <el-table-column prop="source" label="情报来源" width="120" show-overflow-tooltip />
        <el-table-column label="有效状态" width="100">
          <template #default="scope">
            <el-tag
              size="small"
              :type="getValidityStatusType(scope.row)"
              effect="dark"
            >
              {{ getValidityStatusText(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="160" show-overflow-tooltip>
          <template #default="scope">
            <div v-if="scope.row.validityDays > 0 && scope.row.expiresAt > 0">
              {{ formatTimestamp(scope.row.expiresAt) }}
            </div>
            <span v-else class="no-expiry-text">永不过期</span>
          </template>
        </el-table-column>
        <el-table-column label="最后更新" width="160">
          <template #default="scope">
            {{ formatTimestamp(scope.row.lastUpdatedAt || scope.row.updatedAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="scope">
            {{ formatTimestamp(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="handleDetail(scope.row)" :icon="InfoFilled" circle title="详情"></el-button>
              <el-button v-if="isAdmin" size="small" type="primary" @click="handleEdit(scope.row)" :icon="Edit" circle title="编辑"></el-button>
              <el-button v-if="isAdmin" size="small" type="success" @click="handlePush(scope.row)" :icon="Promotion" circle title="推送"></el-button>
              <el-button v-if="isAdmin" size="small" type="danger" @click="handleDelete(scope.row)" :icon="Delete" circle title="删除"></el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- IOC情报表单对话框 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="isEditing ? '编辑IOC情报' : '新增IOC情报'"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="iocForm"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="IOC值" prop="ioc">
          <el-input v-model="iocForm.ioc" placeholder="请输入IOC值（IP地址或域名）" />
        </el-form-item>
        <el-form-item label="IOC类型" prop="iocType">
          <el-select v-model="iocForm.iocType" placeholder="请选择IOC类型" style="width: 100%">
            <el-option label="IP地址" value="ip" />
            <el-option label="域名" value="domain" />
          </el-select>
        </el-form-item>
        <el-form-item label="地理位置">
          <el-input v-model="iocForm.location" placeholder="请输入地理位置（可选）" />
        </el-form-item>
        <el-form-item label="情报类型" prop="type">
          <el-select v-model="iocForm.type" placeholder="请选择情报类型" style="width: 100%">
            <el-option label="僵尸网络" value="僵尸网络" />
            <el-option label="恶意扫描" value="恶意扫描" />
            <el-option label="网络攻击" value="网络攻击" />
            <el-option label="勒索软件" value="勒索软件" />
            <el-option label="钓鱼网站" value="钓鱼网站" />
            <el-option label="挖矿木马" value="挖矿木马" />
            <el-option label="代理服务" value="代理服务" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="风险等级" prop="riskLevel">
          <el-select v-model="iocForm.riskLevel" placeholder="请选择风险等级" style="width: 100%">
            <el-option label="严重" value="严重" />
            <el-option label="高危" value="高危" />
            <el-option label="中危" value="中危" />
            <el-option label="低危" value="低危" />
            <el-option label="信息" value="信息" />
          </el-select>
        </el-form-item>
        <el-form-item label="命中次数">
          <el-input-number v-model="iocForm.hitCount" :min="0" :max="999999" style="width: 100%" />
        </el-form-item>
        <!-- 被攻击单位字段已移除，替换为推送状态 -->
        <el-form-item label="威胁描述">
          <el-input
            v-model="iocForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入威胁描述"
          />
        </el-form-item>
        <el-form-item label="情报来源" prop="source">
          <el-input v-model="iocForm.source" placeholder="请输入情报来源" />
        </el-form-item>
        <el-form-item label="发现日期">
          <el-date-picker
            v-model="iocForm.discoveryDate"
            type="date"
            placeholder="选择发现日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="标签">
          <el-input v-model="iocForm.tags" placeholder="多个标签用逗号分隔" />
        </el-form-item>
        <el-form-item label="推送原因">
          <el-input
            v-model="iocForm.pushReason"
            type="textarea"
            :rows="2"
            placeholder="请输入推送原因（可选）"
          />
        </el-form-item>

        <!-- 有效期配置 -->
        <el-divider content-position="left">有效期配置</el-divider>

        <el-form-item label="是否有效">
          <el-switch v-model="iocForm.isValid" />
          <div class="form-help-text">设置IOC情报是否有效</div>
        </el-form-item>

        <el-form-item label="有效期(天)" v-if="iocForm.isValid">
          <el-input-number
            v-model="iocForm.validityDays"
            :min="0"
            :max="100"
            placeholder="设置有效期天数，0表示永不过期"
            style="width: 100%"
          />
          <div class="form-help-text">设置IOC情报有效期，单位：天（0表示永不过期，最大100天）</div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="formDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEditing ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 推送对话框 -->
    <el-dialog
      v-model="pushDialogVisible"
      :title="isBatchPush ? `批量推送IOC情报 (${currentPushIOCs.length}条)` : '推送IOC情报'"
      width="600px"
      destroy-on-close
    >
      <!-- 单个IOC推送信息 -->
      <div v-if="!isBatchPush && currentPushIOC">
        <el-descriptions :column="1" border class="push-ioc-info">
          <el-descriptions-item label="IOC值">{{ currentPushIOC.ioc }}</el-descriptions-item>
          <el-descriptions-item label="IOC类型">{{ currentPushIOC.iocType }}</el-descriptions-item>
          <el-descriptions-item label="风险等级">
            <el-tag :type="getRiskLevelType(currentPushIOC.riskLevel)">
              {{ currentPushIOC.riskLevel }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 批量IOC推送信息 -->
      <div v-if="isBatchPush && currentPushIOCs.length > 0">
        <div class="batch-push-info">
          <div class="batch-push-header">
            <span>选中的IOC情报 ({{ currentPushIOCs.length }}条)</span>
          </div>
          <div class="batch-push-list">
            <el-table :data="currentPushIOCs" border size="small" max-height="300">
              <el-table-column prop="ioc" label="IOC值" width="200" show-overflow-tooltip />
              <el-table-column prop="iocType" label="类型" width="80">
                <template #default="scope">
                  <el-tag size="small" :type="scope.row.iocType === 'ip' ? 'primary' : 'success'">
                    {{ scope.row.iocType === 'ip' ? 'IP' : '域名' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="riskLevel" label="风险等级" width="100">
                <template #default="scope">
                  <el-tag size="small" :type="getRiskLevelType(scope.row.riskLevel)">
                    {{ scope.row.riskLevel }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="情报类型" show-overflow-tooltip />
            </el-table>
          </div>
        </div>
      </div>

      <el-form :model="{ pushType }" label-width="100px" style="margin-top: 20px;">
        <el-form-item label="推送方式">
          <el-radio-group v-model="pushType">
            <el-radio label="default">使用默认策略</el-radio>
            <el-radio label="channel">指定推送通道</el-radio>
            <el-radio label="policy">指定推送策略</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="推送通道" v-if="pushType === 'channel'">
          <el-select v-model="selectedPushChannel" placeholder="请选择推送通道" style="width: 100%">
            <el-option
              v-for="channel in pushChannels.filter(c => c.status)"
              :key="channel.id"
              :label="channel.name"
              :value="channel.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="推送策略" v-if="pushType === 'policy'">
          <el-select v-model="selectedPushPolicy" placeholder="请选择推送策略" style="width: 100%">
            <el-option
              v-for="policy in pushPolicies"
              :key="policy.id"
              :label="policy.name"
              :value="policy.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="pushDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="executePush" :loading="pushing">
            确认推送
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- IOC情报详情抽屉 -->
    <el-drawer
      v-model="detailDrawerVisible"
      title="IOC情报详情"
      size="60%"
      direction="rtl"
      destroy-on-close
    >
      <div v-if="currentIOCIntelligence" class="ioc-intelligence-detail">
        <div class="custom-descriptions">
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">IOC值</div>
            <div class="custom-descriptions-content ioc-value">{{ currentIOCIntelligence.ioc }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">IOC类型</div>
            <div class="custom-descriptions-content">
              <el-tag size="small" :type="currentIOCIntelligence.iocType === 'ip' ? 'primary' : 'success'">
                {{ currentIOCIntelligence.iocType === 'ip' ? 'IP地址' : '域名' }}
              </el-tag>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">地理位置</div>
            <div class="custom-descriptions-content">{{ currentIOCIntelligence.location || '-' }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">情报类型</div>
            <div class="custom-descriptions-content">
              <el-tag size="small" effect="plain">{{ currentIOCIntelligence.type }}</el-tag>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">风险等级</div>
            <div class="custom-descriptions-content">
              <el-tag size="small" :type="getRiskLevelType(currentIOCIntelligence.riskLevel)" effect="dark">
                {{ currentIOCIntelligence.riskLevel }}
              </el-tag>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">命中次数</div>
            <div class="custom-descriptions-content">{{ currentIOCIntelligence.hitCount }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">推送时间</div>
            <div class="custom-descriptions-content">
              <div v-if="currentIOCIntelligence.pushedAt && currentIOCIntelligence.pushedAt > 0">
                <div>{{ formatTimestamp(currentIOCIntelligence.pushedAt) }}</div>
                <el-tag
                  v-if="currentIOCIntelligence.pushStatus === 'failed'"
                  size="small"
                  type="danger"
                  effect="plain"
                  style="margin-top: 4px;"
                >
                  推送失败
                </el-tag>
              </div>
              <span v-else>未推送</span>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">威胁描述</div>
            <div class="custom-descriptions-content ioc-description">{{ currentIOCIntelligence.description || '-' }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">情报来源</div>
            <div class="custom-descriptions-content">{{ currentIOCIntelligence.source }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">发现日期</div>
            <div class="custom-descriptions-content">{{ currentIOCIntelligence.discoveryDate || '-' }}</div>
          </div>
          <div class="custom-descriptions-row" v-if="currentIOCIntelligence.tags && currentIOCIntelligence.tags.trim()">
            <div class="custom-descriptions-label">标签</div>
            <div class="custom-descriptions-content">
              <el-tag
                v-for="tag in currentIOCIntelligence.tags.split(',')"
                :key="tag"
                size="small"
                style="margin-right: 8px;"
              >
                {{ tag.trim() }}
              </el-tag>
            </div>
          </div>
          <div class="custom-descriptions-row" v-if="currentIOCIntelligence.pushReason">
            <div class="custom-descriptions-label">推送原因</div>
            <div class="custom-descriptions-content ioc-description">{{ currentIOCIntelligence.pushReason }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">创建时间</div>
            <div class="custom-descriptions-content">{{ formatTimestamp(currentIOCIntelligence.createdAt) }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">更新时间</div>
            <div class="custom-descriptions-content">{{ formatTimestamp(currentIOCIntelligence.updatedAt) }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">有效状态</div>
            <div class="custom-descriptions-content">
              <el-tag
                size="small"
                :type="getValidityStatusType(currentIOCIntelligence)"
                effect="dark"
              >
                {{ getValidityStatusText(currentIOCIntelligence) }}
              </el-tag>
            </div>
          </div>
          <div class="custom-descriptions-row" v-if="currentIOCIntelligence.expiresAt > 0">
            <div class="custom-descriptions-label">过期时间</div>
            <div class="custom-descriptions-content">{{ formatTimestamp(currentIOCIntelligence.expiresAt) }}</div>
          </div>
        </div>

        <!-- 天际友盟情报卡片 -->
        <el-card class="detail-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span class="card-title">天际友盟情报</span>
              <div class="tjun-header-actions">
                <el-tag
                  v-if="currentIOCIntelligence.tjunQueryStatus"
                  :type="getTJUNStatusTagType(currentIOCIntelligence.tjunQueryStatus)"
                  size="small"
                  style="margin-right: 8px;"
                >
                  {{ getTJUNStatusText(currentIOCIntelligence.tjunQueryStatus) }}
                </el-tag>
                <el-button
                  type="primary"
                  size="small"
                  :loading="tjunLoading"
                  @click="queryTJUNIntelligence"
                >
                  {{ currentIOCIntelligence.tjunData ? '刷新' : '查询' }}
                </el-button>
              </div>
            </div>
          </template>

          <!-- 显示查询时间 -->
          <div v-if="currentIOCIntelligence.tjunQueryTime" class="tjun-query-info">
            <span class="tjun-query-time">
              查询时间: {{ formatTimestamp(currentIOCIntelligence.tjunQueryTime) }}
            </span>
          </div>

          <div v-if="tjunLoading" class="tjun-loading">
            <el-skeleton :rows="3" animated />
          </div>
          <div v-else-if="tjunError" class="tjun-error">
            <el-alert
              :title="tjunError"
              type="error"
              show-icon
              :closable="false"
            />
          </div>
          <div v-else-if="currentIOCIntelligence.tjunErrorMessage" class="tjun-error">
            <el-alert
              :title="currentIOCIntelligence.tjunErrorMessage"
              type="error"
              show-icon
              :closable="false"
            />
          </div>
          <div v-else-if="displayTJUNData" class="tjun-content">
            <div v-if="currentIOCIntelligence.iocType === 'ip' && displayTJUNData.ip_reputation">
              <!-- IP信誉信息 -->
              <div v-for="(ipInfo, index) in displayTJUNData.ip_reputation" :key="index" class="ip-reputation-item">
                <div class="ip-header">
                  <h4>{{ ipInfo.ip }}</h4>
                  <el-tag size="small" type="info">{{ getLocationText(ipInfo) }}</el-tag>
                </div>
                <div v-if="ipInfo.reputation && ipInfo.reputation.length > 0" class="reputation-list">
                  <div v-for="(rep, repIndex) in ipInfo.reputation" :key="repIndex" class="reputation-item">
                    <div class="reputation-header">
                      <el-tag :type="getReputationTagType(rep.score)" size="small">
                        {{ translateCategory(rep.category) }}
                      </el-tag>
                      <span class="reputation-score">评分: {{ rep.score }}</span>
                    </div>
                    <div v-if="rep.tag && rep.tag.length > 0" class="reputation-tags">
                      <el-tag
                        v-for="tag in rep.tag"
                        :key="tag"
                        size="small"
                        effect="plain"
                        style="margin-right: 4px; margin-top: 4px;"
                      >
                        {{ tag }}
                      </el-tag>
                    </div>
                    <div class="reputation-time">
                      时间: {{ formatTimestamp(new Date(rep.timestamp).getTime() / 1000) }}
                    </div>
                  </div>
                </div>
                <div v-else class="no-reputation">
                  <el-empty description="暂无信誉信息" :image-size="60" />
                </div>
              </div>
            </div>
            <div v-else-if="currentIOCIntelligence.iocType === 'ip'">
              <!-- IP类型但没有信誉数据 -->
              <div class="no-reputation">
                <el-empty description="暂无IP信誉信息" :image-size="60" />
              </div>
            </div>
            <div v-else>
              <!-- 其他类型IOC信息 -->
              <pre class="tjun-raw-data">{{ JSON.stringify(displayTJUNData, null, 2) }}</pre>
            </div>
          </div>
          <div v-else-if="currentIOCIntelligence.tjunQueryStatus === 'success'" class="tjun-empty">
            <el-empty description="查询成功但暂无天际友盟情报数据" :image-size="80" />
          </div>
          <div v-else-if="currentIOCIntelligence.tjunQueryStatus === 'not_queried'" class="tjun-empty">
            <el-empty description="该IOC情报创建时未自动查询天际友盟数据，点击查询按钮获取最新情报" :image-size="80" />
          </div>
          <div v-else class="tjun-empty">
            <el-empty description="暂无天际友盟情报数据" :image-size="80" />
          </div>
        </el-card>

        <!-- 微步威胁情报卡片 -->
        <el-card class="tjun-card" style="margin-top: 20px;">
          <template #header>
            <div class="card-header">
              <span>微步威胁情报</span>
              <div class="header-actions">
                <el-tag
                  :type="getWeibuQueryStatusType(currentIOCIntelligence?.weibuQueryStatus)"
                  size="small"
                  style="margin-right: 10px;"
                >
                  {{ getWeibuQueryStatusText(currentIOCIntelligence?.weibuQueryStatus) }}
                </el-tag>
                <span v-if="currentIOCIntelligence?.weibuQueryTime" class="query-time">
                  查询时间: {{ formatTimestamp(currentIOCIntelligence.weibuQueryTime) }}
                </span>
                <el-button
                  type="primary"
                  size="small"
                  :icon="Refresh"
                  :loading="weibuLoading"
                  @click="queryWeibuIntelligence"
                  style="margin-left: 10px;"
                >
                  {{ weibuLoading ? '查询中...' : '手动查询' }}
                </el-button>
              </div>
            </div>
          </template>

          <div v-if="weibuLoading" class="weibu-loading">
            <el-skeleton :rows="3" animated />
          </div>
          <div v-else-if="weibuError" class="weibu-error">
            <el-alert
              :title="weibuError"
              type="error"
              show-icon
              :closable="false"
            />
          </div>
          <div v-else-if="currentIOCIntelligence.weibuErrorMessage" class="weibu-error">
            <el-alert
              :title="currentIOCIntelligence.weibuErrorMessage"
              type="error"
              show-icon
              :closable="false"
            />
          </div>
          <div v-else-if="displayWeibuData" class="weibu-content">
            <div v-if="currentIOCIntelligence.iocType === 'ip' && displayWeibuData">
              <!-- IP信誉信息 -->
              <div class="ip-reputation-item">
                <div class="ip-header">
                  <h4>{{ currentIOCIntelligence.ioc }}</h4>
                </div>

                <!-- 基本信息 -->
                <div class="basic-info">
                  <div class="info-row">
                    <span class="label">是否恶意:</span>
                    <el-tag :type="displayWeibuData.is_malicious ? 'danger' : 'success'" size="small">
                      {{ displayWeibuData.is_malicious ? '是' : '否' }}
                    </el-tag>
                  </div>
                  <div class="info-row" v-if="displayWeibuData.confidence_level">
                    <span class="label">可信度:</span>
                    <span>{{ displayWeibuData.confidence_level }}</span>
                  </div>
                  <div class="info-row" v-if="displayWeibuData.severity">
                    <span class="label">严重程度:</span>
                    <el-tag :type="getSeverityType(displayWeibuData.severity)" size="small">
                      {{ displayWeibuData.severity }}
                    </el-tag>
                  </div>
                  <div class="info-row" v-if="displayWeibuData.judgments && displayWeibuData.judgments.length > 0">
                    <span class="label">威胁类型:</span>
                    <div class="tags">
                      <el-tag v-for="judgment in displayWeibuData.judgments" :key="judgment" size="small" type="warning">
                        {{ judgment }}
                      </el-tag>
                    </div>
                  </div>
                </div>

                <!-- 标签信息 -->
                <div v-if="displayWeibuData.tags_classes && displayWeibuData.tags_classes.length > 0" class="tags-section">
                  <h5>标签信息</h5>
                  <div v-for="(tagClass, index) in displayWeibuData.tags_classes" :key="index" class="tag-class">
                    <div class="tag-type">{{ tagClass.tags_type }}</div>
                    <div class="tags">
                      <el-tag
                        v-for="tag in (Array.isArray(tagClass.tags) ? tagClass.tags : [tagClass.tags])"
                        :key="tag"
                        size="small"
                        type="info"
                      >
                        {{ tag }}
                      </el-tag>
                    </div>
                  </div>
                </div>

                <!-- 地理位置信息 -->
                <div v-if="displayWeibuData.basic && displayWeibuData.basic.location" class="location-section">
                  <h5>地理位置</h5>
                  <div class="location-info">
                    <div class="info-row" v-if="displayWeibuData.basic.location.country">
                      <span class="label">国家:</span>
                      <span>{{ displayWeibuData.basic.location.country }}</span>
                    </div>
                    <div class="info-row" v-if="displayWeibuData.basic.location.province">
                      <span class="label">省份:</span>
                      <span>{{ displayWeibuData.basic.location.province }}</span>
                    </div>
                    <div class="info-row" v-if="displayWeibuData.basic.location.city">
                      <span class="label">城市:</span>
                      <span>{{ displayWeibuData.basic.location.city }}</span>
                    </div>
                    <div class="info-row" v-if="displayWeibuData.basic.carrier">
                      <span class="label">运营商:</span>
                      <span>{{ displayWeibuData.basic.carrier }}</span>
                    </div>
                  </div>
                </div>

                <!-- 更新时间 -->
                <div v-if="displayWeibuData.update_time" class="update-time">
                  <span class="label">更新时间:</span>
                  <span>{{ displayWeibuData.update_time }}</span>
                </div>
              </div>
            </div>
            <div v-else>
              <!-- 其他类型IOC信息或原始数据 -->
              <pre class="weibu-raw-data">{{ JSON.stringify(displayWeibuData, null, 2) }}</pre>
            </div>
          </div>
          <div v-else-if="currentIOCIntelligence.weibuQueryStatus === 'success'" class="weibu-empty">
            <el-empty description="查询成功但暂无微步威胁情报数据" :image-size="80" />
          </div>
          <div v-else-if="currentIOCIntelligence.weibuQueryStatus === 'not_queried'" class="weibu-empty">
            <el-empty description="该IOC情报创建时未自动查询微步数据，点击查询按钮获取最新情报" :image-size="80" />
          </div>
          <div v-else class="weibu-empty">
            <el-empty description="暂无微步威胁情报数据" :image-size="80" />
          </div>
        </el-card>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { Search, RefreshLeft, Refresh, Plus, Delete, Download, InfoFilled, Edit, Promotion } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import api from './api'

// IOC情报数据
const iocIntelligence = ref([])

const router = useRouter()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)
const multipleSelection = ref([])
const detailDrawerVisible = ref(false)
const currentIOCIntelligence = ref(null)
const dateRange = ref(null)

// 天际友盟相关数据
const tjunData = ref(null)
const tjunLoading = ref(false)
const tjunError = ref('')

// 微步威胁情报相关数据
const weibuData = ref(null)
const weibuLoading = ref(false)
const weibuError = ref('')

// 计算属性：显示的天际友盟数据（优先显示实时查询的数据，否则显示数据库中的数据）
const displayTJUNData = computed(() => {
  if (tjunData.value) {
    return tjunData.value
  }

  if (currentIOCIntelligence.value?.tjunData) {
    try {
      return JSON.parse(currentIOCIntelligence.value.tjunData)
    } catch (error) {
      console.error('解析数据库中的天际友盟数据失败:', error)
      return null
    }
  }

  return null
})

// 计算属性：显示的微步威胁情报数据（优先显示实时查询的数据，否则显示数据库中的数据）
const displayWeibuData = computed(() => {
  if (weibuData.value) {
    return weibuData.value
  }

  if (currentIOCIntelligence.value?.weibuData) {
    try {
      return JSON.parse(currentIOCIntelligence.value.weibuData)
    } catch (error) {
      console.error('解析数据库中的微步威胁情报数据失败:', error)
      return null
    }
  }

  return null
})

// 推送相关数据
const pushDialogVisible = ref(false)
const currentPushIOC = ref(null)
const currentPushIOCs = ref([]) // 批量推送的IOC列表
const isBatchPush = ref(false) // 是否为批量推送
const pushChannels = ref([])
const pushPolicies = ref([])
const selectedPushChannel = ref('')
const selectedPushPolicy = ref('')
const pushType = ref('default') // default, channel, policy
const pushing = ref(false)

// 表单相关
const formDialogVisible = ref(false)
const isEditing = ref(false)
const submitting = ref(false)
const formRef = ref(null)

// IOC情报表单数据
const iocForm = ref({
  ioc: '',
  iocType: '',
  location: '',
  type: '',
  riskLevel: '',
  hitCount: 1,
  description: '',
  source: '',
  discoveryDate: '',
  tags: '',
  pushReason: '',
  // 有效期相关字段
  isValid: true,
  validityDays: 30
})

// 表单验证规则
const formRules = {
  ioc: [
    { required: true, message: '请输入IOC值', trigger: 'blur' }
  ],
  iocType: [
    { required: true, message: '请选择IOC类型', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择情报类型', trigger: 'change' }
  ],
  riskLevel: [
    { required: true, message: '请选择风险等级', trigger: 'change' }
  ],
  source: [
    { required: true, message: '请输入情报来源', trigger: 'blur' }
  ]
}

const filterForm = ref({
  ioc: '',
  iocType: '',
  riskLevel: '',
  type: '',
  startDate: '',
  endDate: '',
  keyword: ''
})

const isAdmin = computed(() => {
  // 从localStorage获取用户信息或其他方式判断是否为管理员
  return true // 示例，实际应根据用户权限判断
})

// 处理日期范围变化
const handleDateRangeChange = (val) => {
  if (val) {
    filterForm.value.startDate = val[0]
    filterForm.value.endDate = val[1]
  } else {
    filterForm.value.startDate = ''
    filterForm.value.endDate = ''
  }
}

// 根据风险等级获取对应的标签类型
const getRiskLevelType = (level) => {
  const map = {
    '严重': 'danger',
    '高危': 'warning',
    '中危': 'warning',
    '低危': 'success',
    '信息': 'info'
  }
  return map[level] || 'info'
}

// 获取有效性状态文本
const getValidityStatusText = (row) => {
  if (!row.isValid) {
    return '无效'
  }
  if (row.expiresAt > 0 && row.expiresAt < Date.now() / 1000) {
    return '已过期'
  }
  return '有效'
}

// 获取有效性状态标签类型
const getValidityStatusType = (row) => {
  if (!row.isValid) {
    return 'danger'
  }
  if (row.expiresAt > 0 && row.expiresAt < Date.now() / 1000) {
    return 'warning'
  }
  return 'success'
}

// 格式化时间戳
const formatTimestamp = (timestamp) => {
  if (!timestamp) return ''
  return dayjs.unix(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchIOCIntelligence()
}

// 重置
const handleReset = () => {
  filterForm.value = {
    ioc: '',
    iocType: '',
    riskLevel: '',
    type: '',
    startDate: '',
    endDate: '',
    keyword: ''
  }
  dateRange.value = null
  currentPage.value = 1
  fetchIOCIntelligence()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchIOCIntelligence()
}

// 处理页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchIOCIntelligence()
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

// 处理排序变化
const handleSortChange = ({ column, prop, order }) => {
  // 实现排序逻辑
  fetchIOCIntelligence()
}

// 查看详情
const handleDetail = async (row) => {
  try {
    const res = await api.getIOCIntelligenceDetail(row.id)
    if (res.data && res.data.data) {
      currentIOCIntelligence.value = res.data.data
      // 重置天际友盟数据
      tjunData.value = null
      tjunError.value = ''
      tjunLoading.value = false

      // 重置微步威胁情报数据
      weibuData.value = null
      weibuError.value = ''
      weibuLoading.value = false
      detailDrawerVisible.value = true
    } else {
      ElMessage.error('获取IOC情报详情失败')
    }
  } catch (error) {
    console.error('获取IOC情报详情失败', error)
    ElMessage.error('获取IOC情报详情失败：' + (error.response?.data?.msg || '未知错误'))
  }
}

// 处理创建
const handleCreate = () => {
  isEditing.value = false
  iocForm.value = {
    ioc: '',
    iocType: '',
    location: '',
    type: '',
    riskLevel: '',
    hitCount: 1,
    description: '',
    source: '',
    discoveryDate: '',
    tags: '',
    pushReason: '',
    // 有效期相关字段
    isValid: true,
    validityDays: 30
  }
  formDialogVisible.value = true
}

// 处理编辑
const handleEdit = (row) => {
  isEditing.value = true
  iocForm.value = { ...row }
  formDialogVisible.value = true
}

// 处理推送
const handlePush = async (row) => {
  currentPushIOC.value = row
  currentPushIOCs.value = []
  isBatchPush.value = false

  // 获取推送通道和策略
  try {
    const [channelsRes, policiesRes] = await Promise.all([
      api.getPushChannels({ page: 1, pageSize: 100 }),
      api.getPushPolicies({ page: 1, pageSize: 100 })
    ])

    console.log('获取推送通道响应:', channelsRes.data)
    console.log('获取推送策略响应:', policiesRes.data)

    // 处理通道数据
    pushChannels.value = (channelsRes.data.data?.list || channelsRes.data.data || []).map(channel => ({
      id: Number(channel.ID || channel.id || 0),
      name: channel.Name || channel.name || '',
      status: Boolean(channel.Status || channel.status)
    })).filter(c => c.status) // 只显示启用的通道

    // 处理策略数据
    pushPolicies.value = (policiesRes.data.data?.list || policiesRes.data.data || []).map(policy => ({
      id: Number(policy.ID || policy.id || 0),
      name: policy.Name || policy.name || '',
      isDefault: Boolean(policy.IsDefault || policy.isDefault)
    }))

    // 重置选择
    selectedPushChannel.value = ''
    selectedPushPolicy.value = ''
    pushType.value = 'default'

    pushDialogVisible.value = true
  } catch (error) {
    console.error('获取推送配置失败:', error)
    ElMessage.error('获取推送配置失败')
  }
}

// 处理批量推送
const handleBatchPush = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请先选择要推送的IOC情报')
    return
  }

  currentPushIOC.value = null
  currentPushIOCs.value = [...multipleSelection.value]
  isBatchPush.value = true

  // 获取推送通道和策略
  try {
    const [channelsRes, policiesRes] = await Promise.all([
      api.getPushChannels({ page: 1, pageSize: 100 }),
      api.getPushPolicies({ page: 1, pageSize: 100 })
    ])

    console.log('批量推送 - 获取推送通道响应:', channelsRes.data)
    console.log('批量推送 - 获取推送策略响应:', policiesRes.data)

    // 处理通道数据
    pushChannels.value = (channelsRes.data.data?.list || channelsRes.data.data || []).map(channel => ({
      id: Number(channel.ID || channel.id || 0),
      name: channel.Name || channel.name || '',
      status: Boolean(channel.Status || channel.status)
    })).filter(c => c.status) // 只显示启用的通道

    // 处理策略数据
    pushPolicies.value = (policiesRes.data.data?.list || policiesRes.data.data || []).map(policy => ({
      id: Number(policy.ID || policy.id || 0),
      name: policy.Name || policy.name || '',
      isDefault: Boolean(policy.IsDefault || policy.isDefault)
    }))

    // 重置选择
    selectedPushChannel.value = ''
    selectedPushPolicy.value = ''
    pushType.value = 'default'

    pushDialogVisible.value = true
  } catch (error) {
    console.error('获取推送配置失败:', error)
    ElMessage.error('获取推送配置失败')
  }
}

// 执行推送
const executePush = async () => {
  // 检查是否有要推送的数据
  if (!isBatchPush.value && !currentPushIOC.value) return
  if (isBatchPush.value && currentPushIOCs.value.length === 0) return

  // 验证推送配置
  if (pushType.value === 'channel' && !selectedPushChannel.value) {
    ElMessage.warning('请选择推送通道')
    return
  }
  if (pushType.value === 'policy' && !selectedPushPolicy.value) {
    ElMessage.warning('请选择推送策略')
    return
  }

  pushing.value = true
  try {
    let params = {}

    if (pushType.value === 'channel' && selectedPushChannel.value) {
      params.channel_id = selectedPushChannel.value
    } else if (pushType.value === 'policy' && selectedPushPolicy.value) {
      params.policy_id = selectedPushPolicy.value
    }

    if (isBatchPush.value) {
      // 批量推送
      const ids = currentPushIOCs.value.map(ioc => ioc.id)
      const batchData = {
        ioc_ids: ids,
        ...(params.channel_id && { channel_id: params.channel_id }),
        ...(params.policy_id && { policy_id: params.policy_id })
      }
      await api.batchPushIOCIntelligence(batchData)
      ElMessage.success(`批量推送成功 (${ids.length}条)`)
    } else {
      // 单个推送
      await api.pushIOCIntelligence(currentPushIOC.value.id, params.channel_id, params.policy_id)
      ElMessage.success('推送成功')
    }

    pushDialogVisible.value = false
  } catch (error) {
    ElMessage.error('推送失败：' + (error.response?.data?.msg || '未知错误'))
  } finally {
    pushing.value = false
  }
}

// 处理删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除IOC情报 "${row.ioc}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await api.deleteIOCIntelligence(row.id)
    ElMessage.success('删除成功')
    fetchIOCIntelligence()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${multipleSelection.value.length} 条IOC情报吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const ids = multipleSelection.value.map(item => item.id)
    await api.batchDeleteIOCIntelligence(ids)
    ElMessage.success('批量删除成功')
    fetchIOCIntelligence()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 导出
const handleExport = async () => {
  try {
    // 显示导出选项对话框
    const result = await ElMessageBox.prompt(
      '请选择导出方式：\n1. 导出选中的记录\n2. 导出当前筛选结果\n3. 导出全部记录',
      '导出IOC情报',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'select',
        inputOptions: {
          'selected': '导出选中的记录',
          'filtered': '导出当前筛选结果',
          'all': '导出全部记录'
        },
        inputValue: 'selected',
        inputValidator: (value) => {
          if (!value) {
            return '请选择导出方式'
          }
          if (value === 'selected' && multipleSelection.value.length === 0) {
            return '请先选择要导出的记录'
          }
          return true
        }
      }
    )

    const exportOption = result.value

    // 准备导出参数
    const exportData = {
      format: 'csv' // 默认导出CSV格式
    }

    if (exportOption === 'selected') {
      // 导出选中的记录
      exportData.ids = multipleSelection.value.map(item => item.id)
    } else if (exportOption === 'filtered') {
      // 导出当前筛选结果
      exportData.ioc = filterForm.value.ioc
      exportData.ioc_type = filterForm.value.iocType
      exportData.risk_level = filterForm.value.riskLevel
      exportData.type = filterForm.value.type
      // target_org字段已移除，替换为推送状态
      exportData.start_date = filterForm.value.startDate
      exportData.end_date = filterForm.value.endDate
    }
    // 如果是导出全部记录，不需要添加额外参数

    // 显示加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在导出数据...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      // 调用导出API
      const response = await api.exportIOCIntelligence(exportData)

      // 创建下载链接
      const blob = new Blob([response.data], {
        type: 'text/csv; charset=utf-8'
      })

      // 生成文件名
      const now = new Date()
      const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_')
      const filename = `ioc_intelligence_${timestamp}.csv`

      // 创建下载链接并触发下载
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', filename)
      document.body.appendChild(link)
      link.click()

      // 清理
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      ElMessage.success('导出成功')
    } finally {
      loadingInstance.close()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('导出IOC情报失败', error)
      ElMessage.error('导出失败，请重试')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    if (isEditing.value) {
      await api.updateIOCIntelligence(iocForm.value.id, iocForm.value)
      ElMessage.success('更新成功')
    } else {
      await api.createIOCIntelligence(iocForm.value)
      ElMessage.success('创建成功')
    }

    formDialogVisible.value = false
    fetchIOCIntelligence()
  } catch (error) {
    ElMessage.error(isEditing.value ? '更新失败' : '创建失败')
  } finally {
    submitting.value = false
  }
}

// 获取IOC情报数据
const fetchIOCIntelligence = async () => {
  loading.value = true

  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ioc: filterForm.value.ioc,
      ioc_type: filterForm.value.iocType,
      risk_level: filterForm.value.riskLevel,
      type: filterForm.value.type,
      // target_org字段已移除，替换为推送状态
      hit_count_range: filterForm.value.hitCountRange,
      start_date: filterForm.value.startDate,
      end_date: filterForm.value.endDate,
      keyword: filterForm.value.keyword || '',
      order_by: 'created_at',
      order_dir: 'desc'
    }

    // 过滤空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await api.getIOCIntelligence(params)

    if (response.data && response.data.data) {
      iocIntelligence.value = response.data.data.list || []
      total.value = response.data.data.pagination?.total || 0
    } else {
      iocIntelligence.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取IOC情报列表失败:', error)
    ElMessage.error('获取IOC情报列表失败')
    iocIntelligence.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 查询天际友盟情报
const queryTJUNIntelligence = async () => {
  if (!currentIOCIntelligence.value) {
    return
  }

  tjunLoading.value = true
  tjunError.value = ''
  tjunData.value = null

  try {
    const response = await api.queryTJUNIOC({
      ioc: currentIOCIntelligence.value.ioc,
      iocType: currentIOCIntelligence.value.iocType,
      iocId: currentIOCIntelligence.value.id
    })

    if (response.data && response.data.success) {
      tjunData.value = response.data.data

      // 更新当前IOC情报对象的天际友盟数据（用于显示）
      currentIOCIntelligence.value.tjunData = JSON.stringify(response.data.data)
      currentIOCIntelligence.value.tjunQueryStatus = 'success'
      currentIOCIntelligence.value.tjunQueryTime = Math.floor(Date.now() / 1000)
      currentIOCIntelligence.value.tjunErrorMessage = ''

      ElMessage.success('天际友盟情报查询成功')
    } else {
      tjunError.value = response.data?.message || '查询失败'

      // 更新当前IOC情报对象的错误信息
      currentIOCIntelligence.value.tjunQueryStatus = 'failed'
      currentIOCIntelligence.value.tjunQueryTime = Math.floor(Date.now() / 1000)
      currentIOCIntelligence.value.tjunErrorMessage = tjunError.value

      ElMessage.error('天际友盟情报查询失败：' + tjunError.value)
    }
  } catch (error) {
    console.error('天际友盟情报查询失败', error)
    tjunError.value = error.response?.data?.msg || error.message || '查询失败'

    // 更新当前IOC情报对象的错误信息
    currentIOCIntelligence.value.tjunQueryStatus = 'failed'
    currentIOCIntelligence.value.tjunQueryTime = Math.floor(Date.now() / 1000)
    currentIOCIntelligence.value.tjunErrorMessage = tjunError.value

    ElMessage.error('天际友盟情报查询失败：' + tjunError.value)
  } finally {
    tjunLoading.value = false
  }
}

// 查询微步威胁情报
const queryWeibuIntelligence = async () => {
  if (!currentIOCIntelligence.value) {
    return
  }

  weibuLoading.value = true
  weibuError.value = ''
  weibuData.value = null

  try {
    const response = await api.queryWeibuIOC({
      ioc: currentIOCIntelligence.value.ioc,
      iocId: currentIOCIntelligence.value.id
    })

    if (response.data && response.data.success) {
      weibuData.value = response.data.data

      // 更新当前IOC情报对象的微步威胁情报数据（用于显示）
      currentIOCIntelligence.value.weibuData = JSON.stringify(response.data.data)
      currentIOCIntelligence.value.weibuQueryStatus = 'success'
      currentIOCIntelligence.value.weibuQueryTime = Math.floor(Date.now() / 1000)
      currentIOCIntelligence.value.weibuErrorMessage = ''

      ElMessage.success('微步威胁情报查询成功')
    } else {
      weibuError.value = response.data?.message || '查询失败'

      // 更新当前IOC情报对象的错误信息
      currentIOCIntelligence.value.weibuQueryStatus = 'failed'
      currentIOCIntelligence.value.weibuQueryTime = Math.floor(Date.now() / 1000)
      currentIOCIntelligence.value.weibuErrorMessage = weibuError.value

      ElMessage.error('微步威胁情报查询失败：' + weibuError.value)
    }
  } catch (error) {
    console.error('微步威胁情报查询失败', error)
    weibuError.value = error.response?.data?.msg || error.message || '查询失败'

    // 更新当前IOC情报对象的错误信息
    currentIOCIntelligence.value.weibuQueryStatus = 'failed'
    currentIOCIntelligence.value.weibuQueryTime = Math.floor(Date.now() / 1000)
    currentIOCIntelligence.value.weibuErrorMessage = weibuError.value

    ElMessage.error('微步威胁情报查询失败：' + weibuError.value)
  } finally {
    weibuLoading.value = false
  }
}

// 获取地理位置文本
const getLocationText = (ipInfo) => {
  if (ipInfo.country && ipInfo.province && ipInfo.city) {
    return `${ipInfo.country} ${ipInfo.province} ${ipInfo.city}`
  } else if (ipInfo.country && ipInfo.province) {
    return `${ipInfo.country} ${ipInfo.province}`
  } else if (ipInfo.country) {
    return ipInfo.country
  }
  return '未知'
}

// 获取信誉标签类型
const getReputationTagType = (score) => {
  if (score >= 80) return 'danger'
  if (score >= 60) return 'warning'
  if (score >= 40) return 'info'
  return 'success'
}

// 翻译威胁类别
const translateCategory = (category) => {
  const categoryMap = {
    'porn': '色情网站',
    'gambling': '赌博网站',
    'spam': '垃圾邮件',
    'phishing': '钓鱼网址',
    'ransomware': '勒索软件',
    'botnet': '僵尸网络',
    'malware': '恶意软件',
    'c2': 'C&C节点',
    'web_attack': 'WEB攻击',
    'downloader': '恶意软件下载链接',
    'malicious_website': '恶意网站',
    'scanner': '扫描器',
    'tor': 'TOR节点',
    'proxy': 'Proxy代理',
    'digiccy': '数字货币',
    'TI': '关联情报report的IOC',
    'defence_exercise': '攻防演练'
  }
  return categoryMap[category] || category
}

// 获取天际友盟查询状态标签类型
const getTJUNStatusTagType = (status) => {
  switch (status) {
    case 'success': return 'success'
    case 'failed': return 'danger'
    case 'not_queried': return 'info'
    default: return 'info'
  }
}

// 获取天际友盟查询状态文本
const getTJUNStatusText = (status) => {
  switch (status) {
    case 'success': return '查询成功'
    case 'failed': return '查询失败'
    case 'not_queried': return '未查询'
    default: return '未知状态'
  }
}

// 获取微步威胁情报查询状态标签类型
const getWeibuQueryStatusType = (status) => {
  switch (status) {
    case 'success': return 'success'
    case 'failed': return 'danger'
    case 'not_queried': return 'info'
    default: return 'info'
  }
}

// 获取微步威胁情报查询状态文本
const getWeibuQueryStatusText = (status) => {
  switch (status) {
    case 'success': return '查询成功'
    case 'failed': return '查询失败'
    case 'not_queried': return '未查询'
    default: return '未知状态'
  }
}

// 获取严重程度标签类型
const getSeverityType = (severity) => {
  switch (severity) {
    case 'high': return 'danger'
    case 'medium': return 'warning'
    case 'low': return 'info'
    default: return 'info'
  }
}

onMounted(() => {
  fetchIOCIntelligence()
})
</script>

<style scoped>
.ioc-intelligence-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 18px;
  font-weight: bold;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  text-align: right;
}

.push-ioc-info {
  margin-bottom: 20px;
}

.batch-push-info {
  margin-bottom: 20px;
}

.batch-push-header {
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
}

.batch-push-list {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.ioc-value {
  font-weight: bold;
  color: #409eff;
}

.error-message {
  color: #f56c6c;
  white-space: pre-wrap;
}

.ioc-description {
  white-space: pre-wrap;
}

.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

/* IOC情报详情样式 */
.ioc-intelligence-detail {
  padding: 20px;
}

.custom-descriptions {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.custom-descriptions-row {
  display: flex;
  border-bottom: 1px solid #dcdfe6;
  min-height: 40px;
}

.custom-descriptions-row:last-child {
  border-bottom: none;
}

.custom-descriptions-label {
  width: 120px;
  padding: 12px 16px;
  background-color: #fafafa;
  border-right: 1px solid #dcdfe6;
  font-weight: 500;
  color: #606266;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.custom-descriptions-content {
  flex: 1;
  padding: 12px 16px;
  color: #303133;
  display: flex;
  align-items: center;
  word-break: break-all;
}

.ioc-value {
  font-weight: bold;
  color: #409eff;
  font-family: 'Courier New', monospace;
}

.ioc-description {
  white-space: pre-wrap;
  line-height: 1.6;
  align-items: flex-start;
  padding-top: 16px;
  padding-bottom: 16px;
}

.push-time-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.push-time-text {
  font-size: 13px;
  color: #303133;
  line-height: 1.4;
}

.push-status-tag {
  margin-top: 2px;
}

.no-push-text {
  font-size: 13px;
  color: #909399;
}

/* 天际友盟情报卡片样式 */
.detail-card {
  margin-top: 20px;
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.tjun-header-actions {
  display: flex;
  align-items: center;
}

.tjun-query-info {
  margin-bottom: 16px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.tjun-query-time {
  font-size: 12px;
  color: #606266;
}

/* 天际友盟情报样式 */
.tjun-loading {
  padding: 20px;
}

.tjun-error {
  padding: 20px;
}

.tjun-content {
  padding: 10px 0;
}

.tjun-empty {
  padding: 20px;
  text-align: center;
}

.ip-reputation-item {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.ip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.ip-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.reputation-list {
  margin-top: 12px;
}

.reputation-item {
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background-color: #ffffff;
}

.reputation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.reputation-score {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.reputation-tags {
  margin-bottom: 8px;
}

.reputation-time {
  font-size: 12px;
  color: #909399;
}

.no-reputation {
  text-align: center;
  padding: 20px;
}

.tjun-raw-data {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 6px;
  font-size: 12px;
  line-height: 1.5;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 400px;
  overflow-y: auto;
}

/* 微步威胁情报样式 */
.weibu-loading {
  padding: 20px;
}

.weibu-error {
  padding: 20px;
}

.weibu-content {
  padding: 10px 0;
}

.weibu-empty {
  padding: 20px;
  text-align: center;
}

.weibu-raw-data {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 6px;
  font-size: 12px;
  line-height: 1.5;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 400px;
  overflow-y: auto;
}

/* 微步威胁情报特有样式 */
.basic-info {
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-row .label {
  font-weight: 600;
  color: #303133;
  margin-right: 8px;
  min-width: 80px;
}

.tags-section {
  margin-bottom: 16px;
}

.tags-section h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.tag-class {
  margin-bottom: 8px;
}

.tag-type {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.location-section {
  margin-bottom: 16px;
}

.location-section h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.location-info {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
}

.update-time {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
  font-size: 12px;
  color: #909399;
}
</style>
