# IOC情报功能结构优化说明

## 概述

本次优化将IOC情报功能重新组织为三个清晰的功能模块，优化了命名规范，提高了代码的可维护性和可理解性。

## 功能模块划分

### 1. 数据采集模块 (Data Collection Module)
**职责**: 从各种数据接口采集原始数据，处理和清洗数据，存储为源数据

**核心功能**:
- 从CCCC黑科技等数据接口采集原始攻击数据
- 数据清洗和预处理
- 攻击数据合并和去重
- 源数据存储和管理

**相关文件**:
- 后端: `backend/internal/handlers/ioc_data.go` (重命名为数据采集模块)
- 前端: `frontend/src/IOCIntelligenceData.vue` (重命名为IOC源数据管理)
- 路由: `/ioc-data-collection`

### 2. 情报生产模块 (Intelligence Production Module)
**职责**: 从源数据生成IOC情报，应用生产策略和过滤规则，管理IOC情报的CRUD操作

**核心功能**:
- 从源数据生成IOC情报
- 应用生产策略过滤
- IOC情报的增删改查
- 威胁评分计算
- IOC白名单管理

**相关文件**:
- 后端: `backend/internal/handlers/ioc_intelligence.go`
- 前端: `frontend/src/IOCIntelligence.vue` (重命名为IOC情报生产管理)
- 路由: `/ioc-intelligence-production`

### 3. 情报推送模块 (Intelligence Push Module)
**职责**: 管理IOC情报的推送，支持单个和批量推送，推送记录管理

**核心功能**:
- 单个IOC情报推送
- 批量IOC情报推送
- 推送记录管理
- 推送状态跟踪
- 推送统计分析

**相关文件**:
- 后端: `backend/internal/handlers/ioc_push.go`
- 前端: `frontend/src/IOCIntelligencePushManagement.vue` (新创建)
- 路由: `/ioc-intelligence-push`

## API路由重构

### 原有路由结构
```
/admin/ioc-intelligence/*
/admin/ioc-intelligence-data/*
/admin/ioc-whitelist/*
```

### 新的路由结构
```
/ioc/data-collection/*          # 数据采集模块
/ioc/intelligence-production/*  # 情报生产模块
/ioc/intelligence-push/*        # 情报推送模块
```

### 具体API端点

#### 1. 数据采集模块
```
GET    /ioc/data-collection/source-data              # 获取IOC源数据列表
POST   /ioc/data-collection/source-data/generate     # 从数据接口生成源数据
DELETE /ioc/data-collection/source-data/batch        # 批量删除源数据
GET    /ioc/data-collection/source-data/stats        # 源数据统计
```

#### 2. 情报生产模块
```
GET    /ioc/intelligence-production/intelligence                    # 获取IOC情报列表
GET    /ioc/intelligence-production/intelligence/:id               # 获取单个IOC情报
POST   /ioc/intelligence-production/intelligence                   # 创建IOC情报
PUT    /ioc/intelligence-production/intelligence/:id               # 更新IOC情报
DELETE /ioc/intelligence-production/intelligence/:id               # 删除IOC情报
POST   /ioc/intelligence-production/intelligence/generate-from-source # 从源数据生成IOC情报
GET    /ioc/intelligence-production/production-strategy            # 获取生产策略配置
GET    /ioc/intelligence-production/whitelist                      # 获取IOC白名单
GET    /ioc/intelligence-production/stats                          # IOC情报统计
```

#### 3. 情报推送模块
```
POST   /ioc/intelligence-push/single/:id        # 单个IOC情报推送
POST   /ioc/intelligence-push/batch             # 批量IOC情报推送
GET    /ioc/intelligence-push/records           # 获取推送记录
DELETE /ioc/intelligence-push/records/:id       # 删除推送记录
GET    /ioc/intelligence-push/records/stats     # 推送统计
```

## 数据模型优化

### 核心数据模型

1. **IOCIntelligenceData** (IOC源数据模型)
   - 表名: `ioc_source_data` (原: `ioc_intelligence_data`)
   - 用途: 存储从数据接口采集的原始攻击数据

2. **IOCIntelligence** (IOC情报模型)
   - 表名: `ioc_intelligence`
   - 用途: 存储经过生产策略处理的最终IOC情报

3. **IOCIntelligencePushRecord** (IOC情报推送记录模型)
   - 表名: `ioc_intelligence_push_records`
   - 用途: 记录IOC情报推送的详细信息和状态

## 前端组件重构

### 组件重命名和功能调整

1. **IOCIntelligenceData.vue** → **IOC源数据管理**
   - 路由: `/ioc-data-collection`
   - 功能: 管理从数据接口采集的源数据

2. **IOCIntelligence.vue** → **IOC情报生产管理**
   - 路由: `/ioc-intelligence-production`
   - 功能: 管理IOC情报的生产和CRUD操作

3. **IOCIntelligencePushManagement.vue** (新创建)
   - 路由: `/ioc-intelligence-push`
   - 功能: 管理IOC情报的推送和推送记录

### 兼容性处理

为保持向后兼容，添加了路由重定向：
```typescript
{ path: '/ioc-intelligence', redirect: '/ioc-intelligence-production' }
{ path: '/ioc-intelligence-data', redirect: '/ioc-data-collection' }
{ path: '/ip-collection', redirect: '/production-strategy' }
```

## 优化效果

### 1. 结构清晰
- 三个功能模块职责明确，边界清晰
- 代码组织更加合理，便于维护

### 2. 命名规范
- API路由命名更加语义化
- 组件和文件命名与功能模块对应

### 3. 可扩展性
- 每个模块可以独立扩展功能
- 便于后续添加新的数据源或推送渠道

### 4. 用户体验
- 功能模块划分清晰，用户更容易理解
- 操作流程更加直观

## 注意事项

1. **数据库迁移**: 需要将表名从 `ioc_intelligence_data` 更新为 `ioc_source_data`
2. **API兼容性**: 旧的API端点需要逐步迁移到新的路由结构
3. **前端更新**: 需要更新所有相关的API调用以使用新的端点
4. **文档更新**: 需要更新相关的API文档和用户手册

## 后续工作

1. 完善各模块的具体实现逻辑
2. 添加模块间的数据流转监控
3. 优化各模块的性能和错误处理
4. 完善单元测试和集成测试
