package crawlers

import (
	"context"
	"time"
	"vulnerability_push/internal/utils"
)

// 全局采集器管理器
var globalCrawlerManager *CrawlerManager

// 初始化采集器系统
func Init() {
	// 创建管理器
	globalCrawlerManager = NewCrawlerManager()

	// 自动注册所有类型的采集器
	RegisterAllCrawlers()

	utils.Infof("采集器系统初始化完成，已注册所有类型采集器")
}

// 注册所有类型的采集器
func RegisterAllCrawlers() {
	// 注册长亭漏洞库采集器
	RegisterCrawler(NewChaitinCrawler())

	// 注册奇安信威胁情报中心采集器
	RegisterCrawler(NewQianxinCrawler())

	// 注册阿里云漏洞库采集器
	RegisterCrawler(NewAliyunCrawler())

	// 注册微步威胁情报采集器
	RegisterCrawler(NewThreatbookCrawler())

	// 注册知道创宇Seebug漏洞平台采集器
	RegisterCrawler(NewSeebugCrawler())

	// 注册启明星辰漏洞通告采集器
	RegisterCrawler(NewVenustechCrawler())

	// 注册OSCS开源安全情报预警采集器
	RegisterCrawler(NewOSCSCrawler())

	// 注册NVD国家漏洞数据库采集器
	RegisterCrawler(NewNVDCrawler(""))

	utils.Infof("已注册全部8种类型的采集器")
}

// 注册采集器
func RegisterCrawler(crawler Grabber) {
	if globalCrawlerManager != nil {
		globalCrawlerManager.RegisterCrawler(crawler)
	}
}

// 执行一次采集任务
func RunCrawlTask(ctx context.Context, pageLimit int, startDate string, endDate string) ([]*VulnInfo, error) {
	if ctx == nil {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()
	}

	utils.Infof("开始执行采集任务...")
	vulns, err := globalCrawlerManager.RunCrawlTask(ctx, pageLimit, startDate, endDate)
	if err != nil {
		utils.Errorf("采集任务执行失败: %v", err)
		return nil, err
	}

	utils.Infof("采集任务执行完成, 共获取%d个有价值的漏洞信息", len(vulns))
	return vulns, nil
}

// 获取所有采集源信息
func GetAllProviders() []*Provider {
	if globalCrawlerManager == nil {
		return nil
	}
	return globalCrawlerManager.GetAllProviders()
}
