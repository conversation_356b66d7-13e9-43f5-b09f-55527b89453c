package models

import (
	"strings"
	"time"
)

// GetAllModels 获取所有模型，用于数据库迁移
func GetAllModels() []interface{} {
	return []interface{}{
		// 用户相关
		&User{},
		
		// 漏洞相关
		&Vulnerability{},
		
		// 采集器相关
		&Crawler{},
		&CrawlerLog{},
		
		// 推送相关
		&PushChannel{},
		&PushPolicy{},
		&PushRecord{},
		&PushWhitelist{},
		&RssConfig{},

		// 导出相关
		&ExportConfig{},

		// IOC情报相关
		&IOCIntelligence{},
		&IOCIntelligenceData{},
		&IOCIntelligencePushRecord{},
		&IOCWhitelist{},
		&DataInterface{},
		&DataInterfaceLog{},
		&ThreatIntelligenceInterface{}, // 威胁情报接口配置
		&ProductionStrategy{},
		&ProductionStrategyLog{},
		&ProcessedUUID{}, // UUID去重表
	}
}

// ValidateModel 验证模型数据
type ValidateModel interface {
	Validate() error
}

// TimestampModel 时间戳模型接口
type TimestampModel interface {
	GetCreatedTime() time.Time
	GetUpdatedTime() time.Time
}

// StatusModel 状态模型接口
type StatusModel interface {
	IsActive() bool
}

// ConfigModel 配置模型接口
type ConfigModel interface {
	GetConfig() map[string]interface{}
	SetConfig(config map[string]interface{}) error
}

// 通用常量定义

// 用户角色常量
const (
	UserRoleAdmin = "admin"
	UserRoleUser  = "user"
)

// GetAllUserRoles 获取所有用户角色
func GetAllUserRoles() []string {
	return []string{
		UserRoleAdmin,
		UserRoleUser,
	}
}

// IsValidUserRole 检查用户角色是否有效
func IsValidUserRole(role string) bool {
	validRoles := GetAllUserRoles()
	for _, r := range validRoles {
		if r == role {
			return true
		}
	}
	return false
}

// 漏洞严重程度常量
const (
	SeverityCritical = "严重"
	SeverityHigh     = "高危"
	SeverityMedium   = "中危"
	SeverityLow      = "低危"
	SeverityInfo     = "信息"
)

// GetAllSeverities 获取所有严重程度
func GetAllSeverities() []string {
	return []string{
		SeverityCritical,
		SeverityHigh,
		SeverityMedium,
		SeverityLow,
		SeverityInfo,
	}
}

// IsValidSeverity 检查严重程度是否有效
func IsValidSeverity(severity string) bool {
	validSeverities := GetAllSeverities()
	for _, s := range validSeverities {
		if s == severity {
			return true
		}
	}
	return false
}

// GetSeverityScore 获取严重程度分数（用于排序）
func GetSeverityScore(severity string) int {
	switch severity {
	case SeverityCritical:
		return 5
	case SeverityHigh:
		return 4
	case SeverityMedium:
		return 3
	case SeverityLow:
		return 2
	case SeverityInfo:
		return 1
	default:
		return 0
	}
}

// 通用状态常量
const (
	StatusEnabled  = "enabled"
	StatusDisabled = "disabled"
	StatusActive   = "active"
	StatusInactive = "inactive"
	StatusSuccess  = "success"
	StatusFailed   = "failed"
	StatusRunning  = "running"
	StatusPending  = "pending"
)

// GetAllStatuses 获取所有状态
func GetAllStatuses() []string {
	return []string{
		StatusEnabled,
		StatusDisabled,
		StatusActive,
		StatusInactive,
		StatusSuccess,
		StatusFailed,
		StatusRunning,
		StatusPending,
	}
}

// IsValidStatus 检查状态是否有效
func IsValidStatus(status string) bool {
	validStatuses := GetAllStatuses()
	for _, s := range validStatuses {
		if s == status {
			return true
		}
	}
	return false
}

// 数据源类型常量
const (
	SourceTypeChaitin    = "chaitin"
	SourceTypeQianxin    = "qianxin"
	SourceTypeAliyun     = "aliyun"
	SourceTypeThreatbook = "threatbook"
	SourceTypeSeebug     = "seebug"
	SourceTypeVenustech  = "venustech"
	SourceTypeOSCS       = "oscs"
	SourceTypeNVD        = "nvd"
	SourceTypeManual     = "manual"
)

// GetAllSourceTypes 获取所有数据源类型
func GetAllSourceTypes() []string {
	return []string{
		SourceTypeChaitin,
		SourceTypeQianxin,
		SourceTypeAliyun,
		SourceTypeThreatbook,
		SourceTypeSeebug,
		SourceTypeVenustech,
		SourceTypeOSCS,
		SourceTypeNVD,
		SourceTypeManual,
	}
}

// IsValidSourceType 检查数据源类型是否有效
func IsValidSourceType(sourceType string) bool {
	validTypes := GetAllSourceTypes()
	for _, t := range validTypes {
		if t == sourceType {
			return true
		}
	}
	return false
}

// 排序方向常量
const (
	SortOrderAsc  = "asc"
	SortOrderDesc = "desc"
)

// GetAllSortOrders 获取所有排序方向
func GetAllSortOrders() []string {
	return []string{
		SortOrderAsc,
		SortOrderDesc,
	}
}

// IsValidSortOrder 检查排序方向是否有效
func IsValidSortOrder(sortOrder string) bool {
	validOrders := GetAllSortOrders()
	for _, o := range validOrders {
		if o == sortOrder {
			return true
		}
	}
	return false
}

// 导出格式常量
const (
	ExportFormatXLSX = "xlsx"
	ExportFormatCSV  = "csv"
	ExportFormatJSON = "json"
	ExportFormatXML  = "xml"
)

// GetAllExportFormats 获取所有导出格式
func GetAllExportFormats() []string {
	return []string{
		ExportFormatXLSX,
		ExportFormatCSV,
		ExportFormatJSON,
		ExportFormatXML,
	}
}

// IsValidExportFormat 检查导出格式是否有效
func IsValidExportFormat(format string) bool {
	validFormats := GetAllExportFormats()
	for _, f := range validFormats {
		if f == format {
			return true
		}
	}
	return false
}

// ExportConfig 导出配置模型
type ExportConfig struct {
	ID            uint   `gorm:"primaryKey" json:"id"`
	Frequency     string `gorm:"size:20;not null;default:'weekly'" json:"frequency"` // weekly, monthly
	SeveritiesStr string `gorm:"column:severities;type:text" json:"severities_str"`
	WeekDay       int    `gorm:"default:1" json:"weekDay"`   // 0-6 对应周日到周六
	MonthDay      int    `gorm:"default:1" json:"monthDay"`  // 1-31 对应每月几号
	LastRun       int64  `gorm:"default:0" json:"lastRun"`
	CreatedAt     int64  `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt     int64  `gorm:"autoUpdateTime" json:"updatedAt"`
}

// TableName 指定表名
func (ExportConfig) TableName() string {
	return "export_configs"
}

// GetSeverities 获取严重程度列表
func (ec *ExportConfig) GetSeverities() []string {
	if ec.SeveritiesStr == "" {
		return []string{}
	}
	return strings.Split(ec.SeveritiesStr, ",")
}

// SetSeverities 设置严重程度列表
func (ec *ExportConfig) SetSeverities(severities []string) {
	ec.SeveritiesStr = strings.Join(severities, ",")
}

// 分页默认值常量
const (
	DefaultPage     = 1
	DefaultPageSize = 20
	MaxPageSize     = 100
	MinPageSize     = 5
)

// ValidatePageParams 验证分页参数
func ValidatePageParams(page, pageSize int) (int, int) {
	if page < 1 {
		page = DefaultPage
	}
	
	if pageSize < MinPageSize {
		pageSize = DefaultPageSize
	} else if pageSize > MaxPageSize {
		pageSize = MaxPageSize
	}
	
	return page, pageSize
}

// CalculatePagination 计算分页信息
func CalculatePagination(total int64, page, pageSize int) map[string]interface{} {
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	
	return map[string]interface{}{
		"total":       total,
		"page":        page,
		"pageSize":    pageSize,
		"totalPages":  totalPages,
		"hasNext":     page < totalPages,
		"hasPrevious": page > 1,
	}
}
