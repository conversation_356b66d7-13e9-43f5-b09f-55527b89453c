package models

import (
	"time"
)

// User 用户模型
type User struct {
	ID        uint   `gorm:"primaryKey" json:"id"`
	Username  string `gorm:"size:50;not null;uniqueIndex" json:"username"`
	Password  string `gorm:"size:100;not null" json:"-"`
	Email     string `gorm:"size:100;uniqueIndex" json:"email"`
	Role      string `gorm:"size:20;not null;default:user" json:"role"` // admin, user
	Status    bool   `gorm:"default:true" json:"status"`
	APIKey    string `gorm:"size:64;uniqueIndex" json:"apiKey"`
	CreatedAt int64  `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt int64  `gorm:"autoUpdateTime" json:"updatedAt"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// IsAdmin 检查是否为管理员
func (u *User) IsAdmin() bool {
	return u.Role == "admin"
}

// IsActive 检查用户是否激活
func (u *User) IsActive() bool {
	return u.Status
}

// GetCreatedTime 获取创建时间
func (u *User) GetCreatedTime() time.Time {
	return time.Unix(u.CreatedAt, 0)
}

// GetUpdatedTime 获取更新时间
func (u *User) GetUpdatedTime() time.Time {
	return time.Unix(u.UpdatedAt, 0)
}
