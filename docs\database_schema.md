# 漏洞推送系统数据库表结构说明

## 概述

本文档详细描述了漏洞推送系统V2版本的数据库表结构，包含所有表的字段定义、数据类型、约束条件等信息。

## 用户管理相关表

### 1. users - 用户表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 用户ID | uint | - | 是 | 主键，自增 |
| 2 | username | 用户名 | string | 50 | 是 | 唯一索引 |
| 3 | password | 密码 | string | 100 | 是 | 加密存储，不返回给前端 |
| 4 | email | 邮箱 | string | 100 | 否 | 唯一索引 |
| 5 | role | 角色 | string | 20 | 是 | admin/user，默认user |
| 6 | status | 状态 | bool | - | 否 | 默认true |
| 7 | api_key | API密钥 | string | 64 | 否 | 唯一索引 |
| 8 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |
| 9 | updated_at | 更新时间 | int64 | - | 是 | 自动更新时间戳 |

## 漏洞管理相关表

### 2. vulnerabilities - 漏洞表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 漏洞ID | uint | - | 是 | 主键，自增 |
| 2 | name | 漏洞名称 | string | 255 | 是 | - |
| 3 | vuln_id | 漏洞编号 | string | 100 | 是 | 唯一索引，如CVE编号 |
| 4 | severity | 严重程度 | string | 50 | 是 | 严重/高危/中危/低危/信息 |
| 5 | tags | 标签 | string | 500 | 否 | 逗号分隔 |
| 6 | disclosure_date | 披露日期 | string | 50 | 否 | - |
| 7 | push_reason | 推送原因 | text | - | 否 | - |
| 8 | source | 来源 | string | 100 | 否 | - |
| 9 | description | 描述 | text | - | 否 | - |
| 10 | references | 参考链接 | text | - | 否 | - |
| 11 | remediation | 修复建议 | text | - | 否 | - |
| 12 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |

## 采集器相关表

### 3. crawlers - 采集器表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 采集器ID | uint | - | 是 | 主键，自增 |
| 2 | name | 采集器名称 | string | 100 | 是 | - |
| 3 | type | 采集器类型 | string | 50 | 是 | 索引，如chaitin/qianxin等 |
| 4 | description | 描述 | string | 500 | 否 | - |
| 5 | config | 配置信息 | text | - | 否 | JSON格式，不返回给前端 |
| 6 | interval | 采集间隔 | string | 20 | 是 | 默认manual |
| 7 | status | 状态 | bool | - | 否 | 默认true |
| 8 | last_run_at | 最后运行时间 | int64 | - | 否 | 默认0 |
| 9 | next_run_at | 下次运行时间 | int64 | - | 否 | 默认0 |
| 10 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |
| 11 | updated_at | 更新时间 | int64 | - | 是 | 自动更新时间戳 |

### 4. crawler_logs - 采集器日志表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 日志ID | uint | - | 是 | 主键，自增 |
| 2 | crawler_id | 采集器ID | uint | - | 是 | 外键，索引 |
| 3 | status | 执行状态 | string | 20 | 是 | success/failed/running，索引 |
| 4 | count | 采集数量 | int | - | 否 | 默认0 |
| 5 | message | 执行消息 | text | - | 否 | - |
| 6 | started_at | 开始时间 | int64 | - | 否 | 默认0 |
| 7 | ended_at | 结束时间 | int64 | - | 否 | 默认0 |
| 8 | duration | 执行时长 | int | - | 否 | 单位秒，默认0 |
| 9 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |

## 推送相关表

### 5. push_channels - 推送通道表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 通道ID | uint | - | 是 | 主键，自增 |
| 2 | name | 通道名称 | string | 100 | 是 | - |
| 3 | type | 通道类型 | string | 50 | 是 | 索引 |
| 4 | description | 描述 | string | 500 | 否 | - |
| 5 | config | 配置信息 | text | - | 否 | JSON格式，不返回给前端 |
| 6 | status | 状态 | bool | - | 否 | 默认true |
| 7 | is_enabled | 是否启用 | bool | - | 否 | 默认true |
| 8 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |
| 9 | updated_at | 更新时间 | int64 | - | 是 | 自动更新时间戳 |

### 6. push_policies - 推送策略表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 策略ID | uint | - | 是 | 主键，自增 |
| 2 | name | 策略名称 | string | 100 | 是 | - |
| 3 | description | 描述 | string | 500 | 否 | - |
| 4 | channel_ids | 通道ID列表 | text | - | 否 | 逗号分隔 |
| 5 | is_default | 是否默认 | bool | - | 否 | 默认false，索引 |
| 6 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |
| 7 | updated_at | 更新时间 | int64 | - | 是 | 自动更新时间戳 |

### 7. push_records - 推送记录表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 记录ID | uint | - | 是 | 主键，自增 |
| 2 | vulnerability_id | 漏洞ID | uint | - | 是 | 外键，索引 |
| 3 | channel_id | 通道ID | uint | - | 是 | 外键，索引 |
| 4 | policy_id | 策略ID | uint | - | 否 | 外键，索引 |
| 5 | status | 推送状态 | string | 20 | 是 | success/failed/pending，索引 |
| 6 | error_message | 错误信息 | text | - | 否 | - |
| 7 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |
| 8 | pushed_at | 推送时间 | int64 | - | 否 | - |

### 8. push_whitelists - 推送白名单表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 白名单ID | uint | - | 是 | 主键，自增 |
| 2 | policy_id | 策略ID | uint | - | 是 | 外键，索引 |
| 3 | type | 白名单类型 | string | 50 | 是 | 索引 |
| 4 | value | 白名单值 | string | 255 | 是 | - |
| 5 | description | 描述 | string | 500 | 否 | - |
| 6 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |
| 7 | updated_at | 更新时间 | int64 | - | 是 | 自动更新时间戳 |

## RSS配置表

### 9. rss_configs - RSS配置表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 配置ID | uint | - | 是 | 主键，自增 |
| 2 | enabled | 是否启用 | bool | - | 否 | 默认true |
| 3 | require_auth | 是否需要认证 | bool | - | 否 | 默认false |
| 4 | title | RSS标题 | string | 200 | 是 | - |
| 5 | description | RSS描述 | string | 500 | 否 | - |
| 6 | item_count | 条目数量 | int | - | 否 | 默认50 |
| 7 | include_severity | 包含严重程度 | text | - | 否 | 逗号分隔 |
| 8 | exclude_tags | 排除标签 | text | - | 否 | 逗号分隔 |
| 9 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |
| 10 | updated_at | 更新时间 | int64 | - | 是 | 自动更新时间戳 |

## 导出配置表

### 10. export_configs - 导出配置表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 配置ID | uint | - | 是 | 主键，自增 |
| 2 | frequency | 导出频率 | string | 20 | 是 | weekly/monthly，默认weekly |
| 3 | severities | 严重程度 | text | - | 否 | JSON格式存储 |
| 4 | week_day | 周几 | int | - | 否 | 0-6对应周日到周六，默认1 |
| 5 | month_day | 月几号 | int | - | 否 | 1-31，默认1 |
| 6 | last_run | 最后运行时间 | int64 | - | 否 | 默认0 |
| 7 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |
| 8 | updated_at | 更新时间 | int64 | - | 是 | 自动更新时间戳 |

## IOC情报相关表

### 11. ioc_intelligence - IOC情报主表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 情报ID | uint | - | 是 | 主键，自增 |
| 2 | ioc | IOC值 | string | 255 | 是 | 索引 |
| 3 | ioc_type | IOC类型 | string | 20 | 是 | ip/domain/hash/url，索引 |
| 4 | location | 地理位置 | string | 100 | 否 | - |
| 5 | type | 威胁类型 | string | 100 | 是 | - |
| 6 | risk_level | 风险等级 | string | 20 | 是 | 严重/高危/中危/低危，索引 |
| 7 | hit_count | 命中次数 | int | - | 否 | 默认0 |
| 8 | description | 描述 | text | - | 否 | - |
| 9 | tags | 标签 | text | - | 否 | - |
| 10 | push_status | 推送状态 | string | 20 | 否 | not_pushed/pushed/failed，默认not_pushed |
| 11 | is_valid | 是否有效 | bool | - | 否 | 默认true，索引 |
| 12 | validity_days | 有效期天数 | int | - | 否 | 默认30 |
| 13 | expires_at | 过期时间 | int64 | - | 否 | 默认0，0表示永不过期，索引 |
| 14 | last_updated_at | 最后更新时间 | int64 | - | 是 | 自动更新时间戳 |
| 15 | tjun_data | 天际友盟数据 | longtext | - | 否 | JSON格式 |
| 16 | tjun_query_status | 天际友盟查询状态 | string | 20 | 否 | not_queried/success/failed，默认not_queried |
| 17 | tjun_query_time | 天际友盟查询时间 | int64 | - | 否 | 默认0 |
| 18 | tjun_error_message | 天际友盟错误信息 | text | - | 否 | - |
| 19 | weibu_data | 微步威胁情报数据 | longtext | - | 否 | JSON格式 |
| 20 | weibu_query_status | 微步查询状态 | string | 20 | 否 | not_queried/success/failed，默认not_queried |
| 21 | weibu_query_time | 微步查询时间 | int64 | - | 否 | 默认0 |
| 22 | weibu_error_message | 微步错误信息 | text | - | 否 | - |
| 23 | pushed_at | 推送时间 | int64 | - | 否 | 默认0，0表示未推送 |
| 24 | source | 来源 | string | 100 | 否 | - |
| 25 | push_reason | 推送原因 | text | - | 否 | - |
| 26 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |
| 27 | updated_at | 更新时间 | int64 | - | 是 | 自动更新时间戳 |

### 12. ioc_source_data - IOC情报源数据表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 数据ID | uint | - | 是 | 主键，自增 |
| 2 | uuid | 唯一标识 | string | 100 | 是 | 唯一索引 |
| 3 | attack_ip | 攻击IP | string | 45 | 是 | 索引 |
| 4 | victim_ip | 受害IP | string | 45 | 是 | 索引 |
| 5 | victim_ips | 受害IP列表 | text | - | 否 | JSON格式 |
| 6 | source_label | 来源标签 | string | 50 | 否 | 索引 |
| 7 | category | 攻击类别 | string | 100 | 否 | 索引 |
| 8 | attack_count | 攻击次数 | int | - | 是 | 默认0，索引 |
| 9 | first_attack_time | 首次攻击时间 | string | 20 | 否 | - |
| 10 | last_attack_time | 最后攻击时间 | string | 20 | 否 | - |
| 11 | attack_times | 攻击时间列表 | text | - | 否 | JSON格式 |
| 12 | threat_score | 威胁评分 | decimal(4,2) | - | 否 | 默认0 |
| 13 | processed_status | 处理状态 | string | 20 | 否 | unprocessed/processed，默认unprocessed，索引 |
| 14 | processed_at | 处理时间 | int64 | - | 否 | 默认0 |
| 15 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |

### 13. ioc_intelligence_push_records - IOC情报推送记录表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 记录ID | uint | - | 是 | 主键，自增 |
| 2 | ioc_intelligence_id | IOC情报ID | uint | - | 是 | 外键，索引 |
| 3 | channel_id | 通道ID | uint | - | 是 | 外键，索引 |
| 4 | channel_name | 通道名称 | string | 100 | 否 | - |
| 5 | channel_type | 通道类型 | string | 50 | 否 | - |
| 6 | status | 推送状态 | string | 20 | 是 | success/failed |
| 7 | error_message | 错误信息 | text | - | 否 | - |
| 8 | pushed_at | 推送时间 | int64 | - | 是 | 自动创建时间戳 |

### 14. ioc_whitelists - IOC白名单表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 白名单ID | uint | - | 是 | 主键，自增 |
| 2 | ioc | IOC值 | string | 255 | 是 | 索引 |
| 3 | ioc_type | IOC类型 | string | 20 | 是 | ip/domain/hash/url，索引 |
| 4 | reason | 加白原因 | string | 500 | 否 | - |
| 5 | created_by | 创建人 | string | 100 | 否 | - |
| 6 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |
| 7 | updated_at | 更新时间 | int64 | - | 是 | 自动更新时间戳 |

## 数据接口相关表

### 15. data_interfaces - 数据接口配置表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 接口ID | uint | - | 是 | 主键，自增 |
| 2 | name | 接口名称 | string | 100 | 是 | - |
| 3 | type | 接口类型 | string | 50 | 是 | 索引 |
| 4 | description | 描述 | string | 500 | 否 | - |
| 5 | config | 配置信息 | text | - | 否 | JSON格式 |
| 6 | status | 状态 | string | 20 | 是 | enabled/disabled，索引 |
| 7 | interval | 执行间隔 | int | - | 是 | 单位秒，默认3600 |
| 8 | last_run_at | 最后运行时间 | int64 | - | 否 | 默认0 |
| 9 | collection_enabled | 采集启用状态 | bool | - | 否 | 默认true |
| 10 | collection_freq | 采集频率 | string | 20 | 否 | 默认hourly |
| 11 | collection_interval | 采集间隔 | int | - | 否 | 单位分钟，默认60 |
| 12 | time_range_type | 时间范围类型 | string | 20 | 否 | relative/absolute |
| 13 | time_range_value | 时间范围值 | int | - | 否 | - |
| 14 | start_time | 开始时间 | datetime | - | 否 | - |
| 15 | end_time | 结束时间 | datetime | - | 否 | - |
| 16 | last_run_time | 最后执行时间 | datetime | - | 否 | - |
| 17 | last_run_status | 最后执行状态 | string | 20 | 否 | success/failed/running |
| 18 | last_run_message | 最后执行消息 | text | - | 否 | - |
| 19 | total_runs | 总执行次数 | int | - | 否 | 默认0 |
| 20 | success_runs | 成功执行次数 | int | - | 否 | 默认0 |
| 21 | failed_runs | 失败执行次数 | int | - | 否 | 默认0 |
| 22 | last_data_count | 最后数据量 | int | - | 否 | 默认0 |
| 23 | total_data_count | 总数据量 | int | - | 否 | 默认0 |
| 24 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |
| 25 | updated_at | 更新时间 | int64 | - | 是 | 自动更新时间戳 |

### 16. data_interface_logs - 数据接口日志表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 日志ID | uint | - | 是 | 主键，自增 |
| 2 | interface_id | 接口ID | uint | - | 是 | 外键，索引 |
| 3 | status | 执行状态 | string | 20 | 是 | success/failed/running，索引 |
| 4 | message | 执行消息 | text | - | 否 | - |
| 5 | started_at | 开始时间 | int64 | - | 否 | 默认0 |
| 6 | ended_at | 结束时间 | int64 | - | 否 | 默认0 |
| 7 | duration | 执行时长 | int | - | 否 | 单位秒，默认0 |
| 8 | data_count | 数据量 | int | - | 否 | 默认0 |
| 9 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |

## 生产策略相关表

### 17. production_strategies - 生产策略表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 策略ID | uint | - | 是 | 主键，自增 |
| 2 | name | 策略名称 | string | 100 | 是 | - |
| 3 | description | 描述 | string | 500 | 否 | - |
| 4 | status | 状态 | string | 20 | 是 | enabled/disabled，索引 |
| 5 | attack_count_threshold | 攻击次数阈值 | int | - | 否 | 默认3 |
| 6 | threat_score_threshold | 威胁评分阈值 | int | - | 否 | 默认2 |
| 7 | enable_threat_scoring | 启用威胁评分 | bool | - | 否 | 默认true |
| 8 | risk_level_filter | 风险等级过滤 | text | - | 否 | - |
| 9 | default_validity_days | 默认有效期天数 | int | - | 否 | 默认30 |
| 10 | enable_validity_control | 启用有效期控制 | bool | - | 否 | 默认true |
| 11 | enable_tjun_query | 启用天际友盟查询 | bool | - | 否 | 默认true |
| 12 | enable_weibu_query | 启用微步查询 | bool | - | 否 | 默认true |
| 13 | schedule_enabled | 启用定时任务 | bool | - | 否 | 默认false |
| 14 | schedule_interval | 定时任务间隔 | int | - | 否 | 单位分钟，默认60 |
| 15 | last_run_time | 上次运行时间 | int64 | - | 否 | 可为null |
| 16 | next_run_time | 下次运行时间 | int64 | - | 否 | 可为null |
| 17 | time_range_hours | 时间范围小时数 | int | - | 否 | 默认24 |
| 18 | allowed_countries | 允许的国家 | text | - | 否 | - |
| 19 | blocked_countries | 阻止的国家 | text | - | 否 | - |
| 20 | allowed_ip_ranges | 允许的IP范围 | text | - | 否 | - |
| 21 | blocked_ip_ranges | 阻止的IP范围 | text | - | 否 | - |
| 22 | created_by | 创建人 | string | 100 | 否 | - |
| 23 | created_at | 创建时间 | int64 | - | 是 | 自动创建时间戳 |
| 24 | updated_at | 更新时间 | int64 | - | 是 | 自动更新时间戳 |

### 18. production_strategy_logs - 生产策略日志表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 日志ID | uint | - | 是 | 主键，自增 |
| 2 | strategy_id | 策略ID | uint | - | 是 | 外键，索引 |
| 3 | status | 执行状态 | string | 20 | 是 | running/success/failed |
| 4 | message | 执行消息 | text | - | 否 | - |
| 5 | processed_at | 处理时间 | int64 | - | 是 | - |

## 去重相关表

### 19. processed_uuids - UUID去重表

| 序号 | 字段名称 | 中文名称 | 字段类型 | 字段长度 | 是否必填 | 备注 |
|------|----------|----------|----------|----------|----------|------|
| 1 | id | 记录ID | uint | - | 是 | 主键，自增 |
| 2 | uuid | UUID值 | string | 100 | 是 | 唯一索引 |
| 3 | source_type | 数据源类型 | string | 50 | 是 | cccc_black_tech/es_alarm等 |
| 4 | processed_at | 处理时间 | time.Time | - | 是 | - |
| 5 | created_at | 创建时间 | time.Time | - | 是 | 自动创建时间戳 |

## 数据库表统计

### 表数量统计
- **用户管理**: 1个表 (users)
- **漏洞管理**: 1个表 (vulnerabilities)
- **采集器管理**: 2个表 (crawlers, crawler_logs)
- **推送管理**: 4个表 (push_channels, push_policies, push_records, push_whitelists)
- **RSS配置**: 1个表 (rss_configs)
- **导出配置**: 1个表 (export_configs)
- **IOC情报**: 4个表 (ioc_intelligence, ioc_source_data, ioc_intelligence_push_records, ioc_whitelists)
- **数据接口**: 2个表 (data_interfaces, data_interface_logs)
- **生产策略**: 2个表 (production_strategies, production_strategy_logs)
- **去重管理**: 1个表 (processed_uuids)

**总计**: 19个数据库表

### 字段类型说明

| 字段类型 | 说明 | 示例 |
|----------|------|------|
| uint | 无符号整数 | 主键ID |
| string | 字符串 | 名称、状态等 |
| text | 长文本 | 描述、配置信息等 |
| longtext | 超长文本 | JSON数据等 |
| bool | 布尔值 | 状态开关 |
| int | 整数 | 计数、间隔等 |
| int64 | 长整数 | 时间戳 |
| decimal(4,2) | 小数 | 威胁评分 |
| datetime | 日期时间 | 执行时间 |
| time.Time | Go时间类型 | 处理时间 |

### 索引说明

- **主键索引**: 所有表的id字段都是主键
- **唯一索引**: username、email、api_key、vuln_id、uuid等需要唯一性的字段
- **普通索引**: 经常用于查询条件的字段，如type、status、created_at等
- **外键索引**: 关联表的外键字段，如crawler_id、channel_id等

### 约束说明

- **NOT NULL**: 必填字段不能为空
- **DEFAULT**: 字段默认值
- **UNIQUE**: 唯一约束
- **FOREIGN KEY**: 外键约束（通过GORM关联关系实现）

### 时间戳字段说明

- **created_at**: 记录创建时间，使用`gorm:"autoCreateTime"`自动设置
- **updated_at**: 记录更新时间，使用`gorm:"autoUpdateTime"`自动更新
- **其他时间字段**: 业务相关的时间字段，如last_run_at、pushed_at等

## 注意事项

1. **字符编码**: 数据库使用UTF-8编码，支持中文字符
2. **时间存储**: 大部分时间字段使用Unix时间戳(int64)存储
3. **JSON存储**: 配置信息等复杂数据使用JSON格式存储在text字段中
4. **软删除**: 当前版本未使用软删除，直接物理删除记录
5. **数据迁移**: 使用GORM的AutoMigrate功能自动创建和更新表结构
6. **索引优化**: 根据查询需求添加了必要的索引以提高查询性能

## 版本信息

- **文档版本**: V2.0
- **数据库版本**: 基于GORM模型定义
- **最后更新**: 2025年1月
- **维护状态**: 活跃维护中
