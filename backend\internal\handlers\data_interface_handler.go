package handlers

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/internal/models"
	"vulnerability_push/internal/service"
	"vulnerability_push/internal/utils"
)

// DataInterfaceHandler 数据接口处理器
type DataInterfaceHandler struct {
	*BaseHandler
	db        *gorm.DB
	registry  *DataInterfaceRegistry
	manager   *DataInterfaceManager
}

// NewDataInterfaceHandler 创建数据接口处理器
func NewDataInterfaceHandler(db *gorm.DB) *DataInterfaceHandler {
	handler := &DataInterfaceHandler{
		BaseHandler: NewBaseHandler(),
		db:          db,
		registry:    NewDataInterfaceRegistry(),
	}

	// 注册内置接口
	handler.registerBuiltinInterfaces()

	// 创建管理器
	handler.manager = NewDataInterfaceManager(handler)

	return handler
}

// registerBuiltinInterfaces 注册内置数据接口
func (h *DataInterfaceHandler) registerBuiltinInterfaces() {
	// 注册CCCC黑科技接口
	h.registry.Register("cccc_black_tech", NewCCCCBlackTechInterface(h))

	// 注册ES告警接口
	h.registry.Register("es_alarm", NewESAlarmInterface(h))

	// 后续可以在这里注册其他接口
	// h.registry.Register("other_interface", NewOtherInterface(h))
}

// GetDataInterfacesRequest 获取数据接口请求
type GetDataInterfacesRequest struct {
	service.PaginationRequest
	Name   string `form:"name"`
	Type   string `form:"type"`
	Status string `form:"status"`
}

// CreateDataInterfaceRequest 创建数据接口请求
type CreateDataInterfaceRequest struct {
	Name               string                 `json:"name" binding:"required"`
	Type               string                 `json:"type" binding:"required"`
	Description        string                 `json:"description"`
	Config             map[string]interface{} `json:"config"`
	Interval           int                    `json:"interval"`
	CollectionEnabled  bool                   `json:"collection_enabled"`
	CollectionFreq     string                 `json:"collection_freq"`
	CollectionInterval int                    `json:"collection_interval" binding:"omitempty,min=1,max=3600"`
	TimeRangeType      string                 `json:"time_range_type"`
	TimeRangeValue     int                    `json:"time_range_value"`
	StartTime          string                 `json:"start_time"`
	EndTime            string                 `json:"end_time"`
}

// UpdateDataInterfaceRequest 更新数据接口请求
type UpdateDataInterfaceRequest struct {
	Name               string                 `json:"name"`
	Description        string                 `json:"description"`
	Config             map[string]interface{} `json:"config"`
	Interval           *int                   `json:"interval" binding:"omitempty,min=1"`
	Status             string                 `json:"status" binding:"omitempty,oneof=enabled disabled"`
	CollectionEnabled  *bool                  `json:"collection_enabled"`
	CollectionFreq     string                 `json:"collection_freq"`
	CollectionInterval *int                   `json:"collection_interval" binding:"omitempty,min=1,max=3600"`
	TimeRangeType      string                 `json:"time_range_type"`
	TimeRangeValue     *int                   `json:"time_range_value"`
	StartTime          string                 `json:"start_time"`
	EndTime            string                 `json:"end_time"`
}

// GetDataInterfaces 获取数据接口列表
func (h *DataInterfaceHandler) GetDataInterfaces(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GetDataInterfacesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 标准化分页参数
	req.NormalizePagination()

	// 构建查询
	query := h.db.Model(&models.DataInterface{})

	// 应用过滤条件
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取数据接口总数失败: "+err.Error())
		return
	}

	// 应用排序和分页
	var dataInterfaces []models.DataInterface
	offset := (req.Page - 1) * req.PageSize

	if err := query.Order("created_at desc").Offset(offset).Limit(req.PageSize).Find(&dataInterfaces).Error; err != nil {
		h.InternalServerError(c, "获取数据接口列表失败: "+err.Error())
		return
	}

	h.PaginatedSuccess(c, dataInterfaces, total, req.Page, req.PageSize)
}

// GetDataInterfaceDetail 获取数据接口详情
func (h *DataInterfaceHandler) GetDataInterfaceDetail(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var dataInterface models.DataInterface
	if err := h.db.First(&dataInterface, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "数据接口不存在")
		} else {
			h.InternalServerError(c, "查询数据接口失败: "+err.Error())
		}
		return
	}

	h.Success(c, dataInterface)
}

// CreateDataInterface 创建数据接口
func (h *DataInterfaceHandler) CreateDataInterface(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req CreateDataInterfaceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 序列化配置
	configJSON, err := h.marshalJSON(req.Config)
	if err != nil {
		h.InternalServerError(c, "配置序列化失败: "+err.Error())
		return
	}

	// 设置默认值
	if req.Interval <= 0 {
		req.Interval = 3600 // 默认1小时
	}
	if req.CollectionInterval <= 0 {
		req.CollectionInterval = 60 // 默认60分钟
	}
	if req.TimeRangeValue <= 0 {
		req.TimeRangeValue = 3600 // 默认1小时
	}
	if req.TimeRangeType == "" {
		req.TimeRangeType = "relative"
	}
	if req.CollectionFreq == "" {
		req.CollectionFreq = "manual"
	}

	// 创建数据接口
	dataInterface := models.DataInterface{
		Name:               req.Name,
		Type:               req.Type,
		Description:        req.Description,
		Config:             configJSON,
		Status:             "disabled", // 默认禁用
		Interval:           req.Interval,
		CollectionEnabled:  req.CollectionEnabled,
		CollectionFreq:     req.CollectionFreq,
		CollectionInterval: req.CollectionInterval,
		TimeRangeType:      req.TimeRangeType,
		TimeRangeValue:     req.TimeRangeValue,
		StartTime:          req.StartTime,
		EndTime:            req.EndTime,
	}

	if err := h.db.Create(&dataInterface).Error; err != nil {
		h.InternalServerError(c, "创建数据接口失败: "+err.Error())
		return
	}

	h.Success(c, dataInterface)
}

// UpdateDataInterface 更新数据接口
func (h *DataInterfaceHandler) UpdateDataInterface(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var req UpdateDataInterfaceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	var dataInterface models.DataInterface
	if err := h.db.First(&dataInterface, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "数据接口不存在")
		} else {
			h.InternalServerError(c, "查询数据接口失败: "+err.Error())
		}
		return
	}

	// 更新字段
	if req.Name != "" {
		dataInterface.Name = req.Name
	}
	if req.Description != "" {
		dataInterface.Description = req.Description
	}
	if req.Config != nil {
		configJSON, err := h.marshalJSON(req.Config)
		if err != nil {
			h.InternalServerError(c, "配置序列化失败: "+err.Error())
			return
		}
		dataInterface.Config = configJSON
	}
	if req.Interval != nil {
		dataInterface.Interval = *req.Interval
	}
	if req.Status != "" {
		dataInterface.Status = req.Status
	}
	if req.CollectionEnabled != nil {
		dataInterface.CollectionEnabled = *req.CollectionEnabled
	}
	if req.CollectionFreq != "" {
		dataInterface.CollectionFreq = req.CollectionFreq
	}
	if req.CollectionInterval != nil {
		dataInterface.CollectionInterval = *req.CollectionInterval
	}
	if req.TimeRangeType != "" {
		dataInterface.TimeRangeType = req.TimeRangeType
	}
	if req.TimeRangeValue != nil {
		dataInterface.TimeRangeValue = *req.TimeRangeValue
	}
	if req.StartTime != "" {
		dataInterface.StartTime = req.StartTime
	}
	if req.EndTime != "" {
		dataInterface.EndTime = req.EndTime
	}

	if err := h.db.Save(&dataInterface).Error; err != nil {
		h.InternalServerError(c, "更新数据接口失败: "+err.Error())
		return
	}

	h.Success(c, dataInterface)
}

// DeleteDataInterface 删除数据接口
func (h *DataInterfaceHandler) DeleteDataInterface(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var dataInterface models.DataInterface
	if err := h.db.First(&dataInterface, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "数据接口不存在")
		} else {
			h.InternalServerError(c, "查询数据接口失败: "+err.Error())
		}
		return
	}

	if err := h.db.Delete(&dataInterface).Error; err != nil {
		h.InternalServerError(c, "删除数据接口失败: "+err.Error())
		return
	}

	h.SuccessWithMessage(c, "数据接口删除成功", nil)
}

// ToggleDataInterfaceStatus 切换数据接口状态
func (h *DataInterfaceHandler) ToggleDataInterfaceStatus(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var dataInterface models.DataInterface
	if err := h.db.First(&dataInterface, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "数据接口不存在")
		} else {
			h.InternalServerError(c, "查询数据接口失败: "+err.Error())
		}
		return
	}

	// 切换状态
	if dataInterface.Status == "enabled" {
		dataInterface.Status = "disabled"
	} else {
		dataInterface.Status = "enabled"
	}

	if err := h.db.Save(&dataInterface).Error; err != nil {
		h.InternalServerError(c, "更新数据接口状态失败: "+err.Error())
		return
	}

	h.Success(c, dataInterface)
}

// RunDataInterface 手动执行数据接口
func (h *DataInterfaceHandler) RunDataInterface(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var dataInterface models.DataInterface
	if err := h.db.First(&dataInterface, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "数据接口不存在")
		} else {
			h.InternalServerError(c, "查询数据接口失败: "+err.Error())
		}
		return
	}

	if dataInterface.Status != "enabled" {
		h.BadRequest(c, "接口未启用，无法执行")
		return
	}

	// 异步执行接口
	go func() {
		h.executeDataInterface(&dataInterface)
	}()

	h.SuccessWithMessage(c, "接口执行已启动", nil)
}

// GetDataInterfaceLogs 获取数据接口执行日志
func (h *DataInterfaceHandler) GetDataInterfaceLogs(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var req service.PaginationRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 标准化分页参数
	req.NormalizePagination()

	// 构建查询
	query := h.db.Model(&models.DataInterfaceLog{}).Where("interface_id = ?", id)

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取日志总数失败: "+err.Error())
		return
	}

	// 应用排序和分页
	var logs []models.DataInterfaceLog
	offset := (req.Page - 1) * req.PageSize

	if err := query.Order("created_at desc").Offset(offset).Limit(req.PageSize).Find(&logs).Error; err != nil {
		h.InternalServerError(c, "获取日志列表失败: "+err.Error())
		return
	}

	h.PaginatedSuccess(c, logs, total, req.Page, req.PageSize)
}

// GetDataInterfaceStats 获取数据接口统计信息
func (h *DataInterfaceHandler) GetDataInterfaceStats(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 获取接口总数
	var totalInterfaces int64
	if err := h.db.Model(&models.DataInterface{}).Count(&totalInterfaces).Error; err != nil {
		h.InternalServerError(c, "获取接口统计失败: "+err.Error())
		return
	}

	// 获取启用的接口数
	var enabledInterfaces int64
	if err := h.db.Model(&models.DataInterface{}).Where("status = ?", "enabled").Count(&enabledInterfaces).Error; err != nil {
		h.InternalServerError(c, "获取接口统计失败: "+err.Error())
		return
	}

	// 获取今日执行次数
	var todayRuns int64
	if err := h.db.Model(&models.DataInterfaceLog{}).Where("DATE(created_at) = CURDATE()").Count(&todayRuns).Error; err != nil {
		h.InternalServerError(c, "获取接口统计失败: "+err.Error())
		return
	}

	// 获取今日成功次数
	var todaySuccess int64
	if err := h.db.Model(&models.DataInterfaceLog{}).Where("DATE(created_at) = CURDATE() AND status = ?", "success").Count(&todaySuccess).Error; err != nil {
		h.InternalServerError(c, "获取接口统计失败: "+err.Error())
		return
	}

	// 计算成功率
	var successRate float64
	if todayRuns > 0 {
		successRate = float64(todaySuccess) / float64(todayRuns) * 100
	}

	stats := map[string]interface{}{
		"totalInterfaces":   totalInterfaces,
		"enabledInterfaces": enabledInterfaces,
		"todayRuns":         todayRuns,
		"todaySuccess":      todaySuccess,
		"successRate":       successRate,
	}

	h.Success(c, stats)
}

// GetDataInterfaceTypes 获取所有可用的数据接口类型
func (h *DataInterfaceHandler) GetDataInterfaceTypes(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	interfaceInfo := h.registry.GetAllInterfaceInfo()
	h.Success(c, interfaceInfo)
}

// GetDataInterfaceTypeInfo 获取特定接口类型的信息
func (h *DataInterfaceHandler) GetDataInterfaceTypeInfo(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	interfaceType := c.Param("type")
	if interfaceType == "" {
		h.BadRequest(c, "接口类型不能为空")
		return
	}

	info := h.registry.GetInterfaceInfo(interfaceType)
	if info == nil {
		h.NotFound(c, "接口类型不存在")
		return
	}

	h.Success(c, info)
}

// ValidateDataInterfaceConfig 验证数据接口配置
func (h *DataInterfaceHandler) ValidateDataInterfaceConfig(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req struct {
		Type   string                 `json:"type" binding:"required"`
		Config map[string]interface{} `json:"config" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 验证配置
	if err := h.manager.ValidateInterfaceConfig(req.Type, req.Config); err != nil {
		h.BadRequest(c, fmt.Sprintf("配置验证失败: %v", err))
		return
	}

	h.SuccessWithMessage(c, "配置验证通过", nil)
}

// GetRunningDataInterfaces 获取正在运行的数据接口
func (h *DataInterfaceHandler) GetRunningDataInterfaces(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	runningIDs := h.manager.GetRunningInterfaces()

	// 获取详细信息
	var runningInterfaces []models.DataInterface
	if len(runningIDs) > 0 {
		h.db.Where("id IN ?", runningIDs).Find(&runningInterfaces)
	}

	h.Success(c, runningInterfaces)
}

// GetDataInterfaceDetailedStats 获取数据接口详细统计
func (h *DataInterfaceHandler) GetDataInterfaceDetailedStats(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	stats, err := h.manager.GetInterfaceStats(id)
	if err != nil {
		h.InternalServerError(c, fmt.Sprintf("获取统计信息失败: %v", err))
		return
	}

	h.Success(c, stats)
}

// ExecuteDataInterface 执行数据接口采集（公开方法，供调度器调用）
func (h *DataInterfaceHandler) ExecuteDataInterface(dataInterface *models.DataInterface) {
	h.executeDataInterface(dataInterface)
}

// executeDataInterface 执行数据接口采集
func (h *DataInterfaceHandler) executeDataInterface(dataInterface *models.DataInterface) {
	// 使用管理器执行接口
	if err := h.manager.ExecuteInterface(dataInterface); err != nil {
		utils.Errorf("执行数据接口失败: %v", err)
	}
}

// updateExecutionLog 更新执行日志
func (h *DataInterfaceHandler) updateExecutionLog(log *models.DataInterfaceLog, status, message string) {
	log.MarkAsCompleted(status, message, 0) // 默认数据量为0，具体数量在其他地方设置

	if err := h.db.Save(log).Error; err != nil {
		utils.Errorf("更新执行日志失败: %v", err)
	}
}

// updateExecutionLogWithDataCount 更新执行日志（包含数据量）
func (h *DataInterfaceHandler) updateExecutionLogWithDataCount(log *models.DataInterfaceLog, status, message string, dataCount int) {
	log.MarkAsCompleted(status, message, dataCount)

	if err := h.db.Save(log).Error; err != nil {
		utils.Errorf("更新执行日志失败: %v", err)
	}
}

// updateDataInterfaceStats 更新数据接口统计信息
func (h *DataInterfaceHandler) updateDataInterfaceStats(dataInterface *models.DataInterface, status, message string, dataCount int) {
	now := time.Now()

	// 更新最后执行信息
	dataInterface.LastRunTime = &now
	dataInterface.LastRunStatus = status
	dataInterface.LastRunMessage = message

	// 更新数据统计
	if status == "success" {
		dataInterface.LastDataCount = dataCount
		dataInterface.TotalDataCount += dataCount
		dataInterface.SuccessRuns++
	} else {
		dataInterface.FailedRuns++
	}

	// 总执行次数
	dataInterface.TotalRuns++

	if err := h.db.Save(dataInterface).Error; err != nil {
		fmt.Printf("更新数据接口统计信息失败: %v\n", err)
	}
}

// marshalJSON 序列化为JSON字符串
func (h *DataInterfaceHandler) marshalJSON(data interface{}) (string, error) {
	bytes, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}
