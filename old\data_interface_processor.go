package handlers

import (
	"fmt"
	"time"

	"gorm.io/gorm"
	"vulnerability_push/internal/models"
)

// AttackSummary 攻击统计信息
type AttackSummary struct {
	UUID         string            `json:"uuid"`
	SrcIP        string            `json:"src_ip"`
	DstIP        string            `json:"dst_ip"`
	DstIPs       []string          `json:"dst_ips"`       // 受害IP列表（用于合并记录）
	SrcLabel     string            `json:"src_label"`
	DstLabel     string            `json:"dst_label"`
	DstLabels    []string          `json:"dst_labels"`    // 受害标签列表（用于合并记录）
	AttackCount  int               `json:"attack_count"`
	Categories   map[string]int    `json:"categories"`
	Severities   map[string]int    `json:"severities"`
	IOCs         map[string]int    `json:"iocs"`
	FirstSeen    int64             `json:"first_seen"`
	LastSeen     int64             `json:"last_seen"`
	SampleRecord SearchDataRecord  `json:"sample_record"`
	UUIDs        []string          `json:"uuids"`         // UUID列表（用于合并记录）
}

// mergeAttackData 处理攻击数据，分两阶段：1.UUID去重 2.外网IP合并
func (h *DataInterfaceHandler) mergeAttackData(records []SearchDataRecord) (map[string]*AttackSummary, int, int, int) {
	// 第一阶段：UUID去重，创建基础攻击记录
	uuidMap := make(map[string]*AttackSummary)
	internalCount := 0
	whitelistCount := 0
	filteredCount := 0

	fmt.Printf("第一阶段：UUID去重处理\n")
	for _, record := range records {
		// 检查UUID是否为空，如果为空则跳过
		if record.UUID == "" {
			fmt.Printf("跳过UUID为空的记录: SrcIP=%s, DstIP=%s\n", record.SrcIP, record.DstIP)
			continue
		}

		// 统计内网流量
		if h.isInternalTraffic(record) {
			internalCount++
			continue
		}

		// 检查是否涉及目标组织
		if !h.shouldIncludeAttackFlow(record.SrcIP, record.DstIP) {
			filteredCount++
			continue
		}

		// 使用UUID作为唯一键进行去重
		key := record.UUID
		if existing, exists := uuidMap[key]; exists {
			// UUID已存在，更新统计信息
			existing.AttackCount++
			h.updateCategoryCount(existing.Categories, record.Category)
			h.updateSeverityCount(existing.Severities, record.Severity)
			h.updateIOCCount(existing.IOCs, record.IOC)
			
			// 更新时间范围
			if record.Timestamp < existing.FirstSeen {
				existing.FirstSeen = record.Timestamp
			}
			if record.Timestamp > existing.LastSeen {
				existing.LastSeen = record.Timestamp
			}
		} else {
			// 新UUID，创建攻击记录
			summary := &AttackSummary{
				UUID:        record.UUID,
				SrcIP:       record.SrcIP,
				DstIP:       record.DstIP,
				DstIPs:      []string{record.DstIP},
				SrcLabel:    h.getSourceLabel(record.SrcIP),
				DstLabel:    h.getSourceLabel(record.DstIP),
				DstLabels:   []string{h.getSourceLabel(record.DstIP)},
				AttackCount: 1,
				Categories:  make(map[string]int),
				Severities:  make(map[string]int),
				IOCs:        make(map[string]int),
				FirstSeen:   record.Timestamp,
				LastSeen:    record.Timestamp,
				SampleRecord: record,
				UUIDs:       []string{record.UUID},
			}
			
			h.updateCategoryCount(summary.Categories, record.Category)
			h.updateSeverityCount(summary.Severities, record.Severity)
			h.updateIOCCount(summary.IOCs, record.IOC)
			
			uuidMap[key] = summary
		}
	}

	fmt.Printf("UUID去重后记录数: %d\n", len(uuidMap))

	// 第二阶段：外网IP合并处理
	fmt.Printf("第二阶段：外网IP合并处理\n")
	mergedMap := make(map[string]*AttackSummary)

	for _, summary := range uuidMap {
		// 确定合并键：外网IP攻击内网时使用攻击IP，内网攻击外网时使用受害IP
		var mergeKey string
		if h.isPrivateIP(summary.SrcIP) && !h.isPrivateIP(summary.DstIP) {
			// 内网攻击外网：使用受害IP作为合并键
			mergeKey = fmt.Sprintf("internal_to_external_%s", summary.DstIP)
		} else if !h.isPrivateIP(summary.SrcIP) && h.isPrivateIP(summary.DstIP) {
			// 外网攻击内网：使用攻击IP作为合并键
			mergeKey = fmt.Sprintf("external_attack_%s", summary.SrcIP)
		} else {
			// 其他情况：使用攻击IP作为合并键
			mergeKey = fmt.Sprintf("other_attack_%s", summary.SrcIP)
		}

		if existing, exists := mergedMap[mergeKey]; exists {
			// 合并到现有记录
			h.mergeAttackSummaries(existing, summary)
			fmt.Printf("合并攻击记录: %s -> 总攻击次数: %d, 受害IP数: %d\n", 
				mergeKey, existing.AttackCount, len(existing.DstIPs))
		} else {
			// 创建新的合并记录
			mergedMap[mergeKey] = summary
		}
	}

	fmt.Printf("外网IP合并后记录数: %d\n", len(mergedMap))
	return mergedMap, internalCount, whitelistCount, filteredCount
}

// mergeAttackDataWithUUIDDedup 新的去重逻辑：先UUID去重，再攻击流合并
func (h *DataInterfaceHandler) mergeAttackDataWithUUIDDedup(records []SearchDataRecord) (map[string]*AttackSummary, int, int, int) {
	internalCount := 0
	whitelistCount := 0
	filteredCount := 0

	// 第一阶段：UUID去重 - 查询数据库过滤已处理的UUID
	fmt.Printf("第一阶段：UUID去重处理\n")
	var validRecords []SearchDataRecord

	for _, record := range records {
		// 检查UUID是否为空
		if record.UUID == "" {
			fmt.Printf("跳过UUID为空的记录: SrcIP=%s, DstIP=%s\n", record.SrcIP, record.DstIP)
			continue
		}

		// 统计内网流量
		if h.isInternalTraffic(record) {
			internalCount++
			continue
		}

		// 检查是否涉及目标组织
		if !h.shouldIncludeAttackFlow(record.SrcIP, record.DstIP) {
			filteredCount++
			continue
		}

		// 检查UUID是否已处理过
		var existingUUID models.ProcessedUUID
		err := h.db.Where("uuid = ? AND source_type = ?", record.UUID, "cccc_black_tech").First(&existingUUID).Error
		if err == nil {
			// UUID已存在，跳过
			fmt.Printf("UUID已处理过，跳过: %s\n", record.UUID)
			continue
		} else if err != gorm.ErrRecordNotFound {
			// 查询错误，记录日志但继续处理
			fmt.Printf("查询UUID失败: %v\n", err)
		}

		// UUID未处理过，加入有效记录列表
		validRecords = append(validRecords, record)
	}

	fmt.Printf("UUID去重后有效记录数: %d (原始: %d, 内网: %d, 非目标: %d)\n",
		len(validRecords), len(records), internalCount, filteredCount)

	// 第二阶段：攻击流合并 - 根据攻击IP和受害IP合并
	fmt.Printf("第二阶段：攻击流合并处理\n")
	attackFlowMap := make(map[string]*AttackSummary)

	for _, record := range validRecords {
		// 生成攻击流键：攻击IP -> 受害IP
		flowKey := fmt.Sprintf("%s->%s", record.SrcIP, record.DstIP)

		if existing, exists := attackFlowMap[flowKey]; exists {
			// 攻击流已存在，合并数据
			existing.AttackCount++
			existing.UUIDs = append(existing.UUIDs, record.UUID)
			h.updateCategoryCount(existing.Categories, record.Category)
			h.updateSeverityCount(existing.Severities, record.Severity)
			h.updateIOCCount(existing.IOCs, record.IOC)

			// 更新时间范围
			if record.Timestamp < existing.FirstSeen {
				existing.FirstSeen = record.Timestamp
			}
			if record.Timestamp > existing.LastSeen {
				existing.LastSeen = record.Timestamp
			}

			fmt.Printf("合并攻击流: %s, 攻击次数: %d, UUID数: %d\n",
				flowKey, existing.AttackCount, len(existing.UUIDs))
		} else {
			// 新攻击流，创建记录
			summary := &AttackSummary{
				UUID:        record.UUID, // 使用第一个UUID作为主UUID
				SrcIP:       record.SrcIP,
				DstIP:       record.DstIP,
				DstIPs:      []string{record.DstIP},
				SrcLabel:    h.getSourceLabel(record.SrcIP),
				DstLabel:    h.getSourceLabel(record.DstIP),
				DstLabels:   []string{h.getSourceLabel(record.DstIP)},
				AttackCount: 1,
				Categories:  make(map[string]int),
				Severities:  make(map[string]int),
				IOCs:        make(map[string]int),
				FirstSeen:   record.Timestamp,
				LastSeen:    record.Timestamp,
				SampleRecord: record,
				UUIDs:       []string{record.UUID},
			}

			h.updateCategoryCount(summary.Categories, record.Category)
			h.updateSeverityCount(summary.Severities, record.Severity)
			h.updateIOCCount(summary.IOCs, record.IOC)

			attackFlowMap[flowKey] = summary
			fmt.Printf("新攻击流: %s, 攻击IP: %s, 受害IP: %s\n",
				flowKey, record.SrcIP, record.DstIP)
		}
	}

	fmt.Printf("攻击流合并后记录数: %d\n", len(attackFlowMap))
	return attackFlowMap, internalCount, whitelistCount, filteredCount
}

// saveProcessedUUIDs 保存已处理的UUID到去重表
func (h *DataInterfaceHandler) saveProcessedUUIDs(uuids []string, sourceType string) error {
	if len(uuids) == 0 {
		return nil
	}

	// 批量插入UUID记录
	var processedUUIDs []models.ProcessedUUID
	for _, uuid := range uuids {
		processedUUIDs = append(processedUUIDs, models.ProcessedUUID{
			UUID:        uuid,
			SourceType:  sourceType,
			ProcessedAt: time.Now(),
		})
	}

	// 使用批量插入，忽略重复键错误
	err := h.db.Create(&processedUUIDs).Error
	if err != nil {
		fmt.Printf("保存UUID到去重表失败: %v\n", err)
		return err
	}

	fmt.Printf("成功保存 %d 个UUID到去重表\n", len(uuids))
	return nil
}

// mergeAttackSummaries 合并两个攻击统计记录
func (h *DataInterfaceHandler) mergeAttackSummaries(existing, new *AttackSummary) {
	// 合并攻击次数
	existing.AttackCount += new.AttackCount

	// 合并受害IP列表（去重）
	for _, ip := range new.DstIPs {
		if !h.containsString(existing.DstIPs, ip) {
			existing.DstIPs = append(existing.DstIPs, ip)
		}
	}

	// 合并受害标签列表（去重）
	for _, label := range new.DstLabels {
		if !h.containsString(existing.DstLabels, label) {
			existing.DstLabels = append(existing.DstLabels, label)
		}
	}

	// 合并UUID列表
	existing.UUIDs = append(existing.UUIDs, new.UUIDs...)

	// 合并类别统计
	for category, count := range new.Categories {
		existing.Categories[category] += count
	}

	// 合并严重程度统计
	for severity, count := range new.Severities {
		existing.Severities[severity] += count
	}

	// 合并IOC统计
	for ioc, count := range new.IOCs {
		existing.IOCs[ioc] += count
	}

	// 更新时间范围
	if new.FirstSeen < existing.FirstSeen {
		existing.FirstSeen = new.FirstSeen
	}
	if new.LastSeen > existing.LastSeen {
		existing.LastSeen = new.LastSeen
	}

	// 更新主要受害IP（选择攻击次数最多的）
	if len(new.DstIPs) > 0 {
		existing.DstIP = new.DstIPs[0] // 简化处理，使用第一个
	}
}

// saveAttackSummaryToIOC 保存攻击统计到IOC情报源数据库
func (h *DataInterfaceHandler) saveAttackSummaryToIOC(summary *AttackSummary) error {
	// 简化保存逻辑，统一处理所有攻击流
	return h.saveAttackFlow(summary)
}

// saveAttackFlow 保存攻击流到IOC情报源数据库（简化版本）
func (h *DataInterfaceHandler) saveAttackFlow(summary *AttackSummary) error {
	// 确定来源标签
	sourceLabel := ""
	if summary.DstLabel == "通信中心" || summary.DstLabel == "交通运输部" {
		sourceLabel = summary.DstLabel
	} else if summary.SrcLabel == "通信中心" || summary.SrcLabel == "交通运输部" {
		sourceLabel = summary.SrcLabel
	}

	// 获取主要攻击类别
	var mainCategory string
	maxCount := 0
	for category, count := range summary.Categories {
		if count > maxCount {
			maxCount = count
			mainCategory = category
		}
	}

	// 计算威胁评分
	threatScore := h.calculateThreatScore(summary.Severities, summary.AttackCount)

	// 创建IOC情报源数据记录
	iocData := models.IOCIntelligenceData{
		UUID:             summary.UUID, // 使用主UUID
		AttackIP:         summary.SrcIP,
		VictimIP:         summary.DstIP,
		SourceLabel:      sourceLabel,
		Category:         mainCategory,
		AttackCount:      summary.AttackCount,
		FirstAttackTime:  h.formatTimestamp(summary.FirstSeen),
		LastAttackTime:   h.formatTimestamp(summary.LastSeen),
		ThreatScore:      threatScore,
	}

	// 保存到数据库
	if err := h.db.Create(&iocData).Error; err != nil {
		return fmt.Errorf("保存IOC情报源数据失败: %v", err)
	}

	fmt.Printf("保存攻击流: UUID=%s, AttackIP=%s, VictimIP=%s, AttackCount=%d, FirstTime=%s, LastTime=%s\n",
		iocData.UUID, iocData.AttackIP, iocData.VictimIP, iocData.AttackCount,
		iocData.FirstAttackTime, iocData.LastAttackTime)

	return nil
}




