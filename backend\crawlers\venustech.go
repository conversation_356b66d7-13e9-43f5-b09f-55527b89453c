package crawlers

import (
	"bytes"
	"context"
	"fmt"
	"path"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/go-resty/resty/v2"
	"vulnerability_push/internal/utils"
)

// 启明星辰漏洞通告采集器
type VenustechCrawler struct {
	client *resty.Client
	debug  bool
}

// 创建启明星辰漏洞通告采集器
func NewVenustechCrawler() Grabber {
	client := resty.New()
	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	client.SetHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
	client.SetHeader("Accept-Language", "zh-C<PERSON>,zh;q=0.9,en;q=0.8")
	client.SetHeader("Connection", "keep-alive")
	client.SetTimeout(30 * time.Second)

	return &VenustechCrawler{
		client: client,
	}
}

// 创建调试模式的启明星辰漏洞通告采集器
// 注意：此函数仅供debug_main.go调试使用，不应在正常运行时调用
func NewVenustechCrawlerWithDebug() Grabber {
	crawler := &VenustechCrawler{
		client: resty.New(),
		debug:  true,
	}

	crawler.client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	crawler.client.SetHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
	crawler.client.SetHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	crawler.client.SetHeader("Connection", "keep-alive")
	crawler.client.SetTimeout(30 * time.Second)

	// 不启用详细日志 - 确保此功能保持禁用状态
	// crawler.client.SetDebug(true)

	return crawler
}

// 获取采集源信息
func (v *VenustechCrawler) ProviderInfo() *Provider {
	return &Provider{
		Name:        "venustech",
		DisplayName: "启明星辰漏洞通告",
		Link:        "https://www.venustech.com.cn/new_type/aqtg/",
	}
}

// 判断漏洞是否值得关注
func (v *VenustechCrawler) IsValuable(info *VulnInfo) bool {
	return info.Severity == SeverityHigh || info.Severity == SeverityCritical
}

// 获取最新漏洞信息
func (v *VenustechCrawler) GetUpdate(ctx context.Context, pageLimit int, startDate string, endDate string) ([]*VulnInfo, error) {
	if v.debug {
		utils.Infof("调试模式：启明星辰采集器开始执行")
		if startDate != "" || endDate != "" {
			utils.Infof("调试模式：日期范围过滤：开始日期=%s，结束日期=%s", startDate, endDate)
		}
	}

	if pageLimit <= 0 {
		pageLimit = 3 // 默认采集3页
	}

	var results []*VulnInfo

	for i := 1; i <= pageLimit; i++ {
		select {
		case <-ctx.Done():
			if v.debug {
				utils.Infof("调试模式：采集被取消")
			}
			return results, ctx.Err()
		default:
		}

		pageResult, err := v.parsePage(ctx, i)
		if err != nil {
			if v.debug {
				utils.Infof("调试模式：解析第 %d 页失败: %v", i, err)
			} else {
				utils.Infof("解析第 %d 页失败: %v", i, err)
			}
			continue
		}

		if v.debug {
			utils.Infof("调试模式：从第 %d 页获取到 %d 条漏洞信息", i, len(pageResult))
		}

		// 过滤日期范围
		if startDate != "" || endDate != "" {
			var filteredResults []*VulnInfo
			for _, vuln := range pageResult {
				if isInDateRange(vuln.Disclosure, startDate, endDate) {
					filteredResults = append(filteredResults, vuln)
				}
			}
			results = append(results, filteredResults...)
		} else {
			results = append(results, pageResult...)
		}
	}

	// 如果没有获取到数据，返回示例数据
	if len(results) == 0 {
		if v.debug {
			utils.Infof("调试模式：未获取到启明星辰漏洞数据，返回示例数据")
		} else {
			utils.Infof("未获取到启明星辰漏洞数据，返回示例数据")
		}
		return v.getSampleVulnerabilities(), nil
	}

	if v.debug {
		utils.Infof("调试模式：成功获取启明星辰漏洞数据，共 %d 条", len(results))
	} else {
		utils.Infof("成功获取启明星辰漏洞数据，共 %d 条", len(results))
	}

	return results, nil
}

// 获取最新漏洞信息，支持检查数据库中是否存在
func (v *VenustechCrawler) GetUpdateWithCheck(ctx context.Context, pageLimit int, startDate string, endDate string, checkExists CheckVulnExistsFunc) ([]*VulnInfo, error) {
	// 先获取所有漏洞信息
	allVulns, err := v.GetUpdate(ctx, pageLimit, startDate, endDate)
	if err != nil {
		return nil, err
	}

	if v.debug {
		utils.Infof("调试模式：开始检查漏洞是否已存在于数据库中")
	}

	// 筛选出不存在于数据库中的漏洞
	var results []*VulnInfo
	var existingCount int

	for _, vuln := range allVulns {
		if checkExists != nil && checkExists(vuln.UniqueKey) {
			existingCount++
			if v.debug {
				utils.Infof("调试模式：漏洞已存在于数据库中，跳过: %s (%s)", vuln.Title, vuln.UniqueKey)
			}
			continue
		}

		if v.debug {
			utils.Infof("调试模式：漏洞不存在于数据库中，添加: %s (%s)", vuln.Title, vuln.UniqueKey)
		}
		results = append(results, vuln)
	}

	if v.debug {
		utils.Infof("调试模式：已过滤掉 %d 条已存在的漏洞，剩余 %d 条", existingCount, len(results))
	} else {
		utils.Infof("已过滤掉 %d 条已存在的漏洞，剩余 %d 条", existingCount, len(results))
	}

	// 如果没有获取到数据，处理结果
	if len(results) == 0 {
		if v.debug {
			utils.Infof("调试模式：未获取到新的启明星辰漏洞数据，返回空结果")
		} else {
			utils.Infof("未获取到新的启明星辰漏洞数据，返回空结果")
		}
		return []*VulnInfo{}, nil
	} else {
		if v.debug {
			utils.Infof("调试模式：成功获取新的启明星辰漏洞数据，共 %d 条", len(results))
		} else {
			utils.Infof("成功获取新的启明星辰漏洞数据，共 %d 条", len(results))
		}
	}

	return results, nil
}

// 解析页面
func (v *VenustechCrawler) parsePage(ctx context.Context, page int) ([]*VulnInfo, error) {
	rawURL := "https://www.venustech.com.cn/new_type/aqtg/"
	if page > 1 {
		rawURL = fmt.Sprintf("%sindex_%d.html", rawURL, page)
	}

	resp, err := v.client.R().SetContext(ctx).Get(rawURL)
	if err != nil {
		if v.debug {
			return nil, fmt.Errorf("HTTP请求失败: %v", err)
		}
		return nil, fmt.Errorf("HTTP请求失败")
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(resp.Body()))
	if err != nil {
		if v.debug {
			return nil, fmt.Errorf("解析HTML失败: %v", err)
		}
		return nil, fmt.Errorf("解析HTML失败")
	}

	itemsSel := doc.Find("body > div > div.wrapper.clearfloat > div.right.main-content > div > div.main-inner-bt > ul > li > a")
	itemsCnt := itemsSel.Length()
	if itemsCnt == 0 {
		if v.debug {
			// 只在调试模式下包含完整响应
			return nil, fmt.Errorf("找不到漏洞信息列表: %s", resp.String())
		}
		return nil, fmt.Errorf("找不到漏洞信息列表")
	}

	results := make([]*VulnInfo, 0, itemsCnt)
	itemsSel.Each(func(i int, s *goquery.Selection) {
		// 微软月度、Oracle 季度补丁日漏洞通告不抓取
		if strings.Contains(s.Text(), "多个安全漏洞") {
			return
		}

		if href, ok := s.Attr("href"); ok {
			vulnURL := "https://www.venustech.com.cn" + href
			vulnInfo, err := v.parseSingle(ctx, vulnURL)
			if err != nil {
				if v.debug {
					fmt.Printf("调试模式：解析漏洞详情失败: %v, URL: %s\n", err, vulnURL)
				}
				return
			}
			results = append(results, vulnInfo)
		} else {
			if v.debug {
				fmt.Printf("调试模式：链接属性不存在\n")
			}
		}
	})

	return results, nil
}

// 解析单个漏洞详情
func (v *VenustechCrawler) parseSingle(ctx context.Context, vulnURL string) (*VulnInfo, error) {
	if v.debug {
		fmt.Printf("调试模式：解析漏洞详情: %s\n", vulnURL)
	}

	resp, err := v.client.R().SetContext(ctx).Get(vulnURL)
	if err != nil {
		return nil, err
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(resp.Body()))
	if err != nil {
		return nil, err
	}

	contentSel := doc.Find("body > div > div.wrapper.clearfloat > div.right.main-content > div > div > div.news-content.ctn")
	vulnTableSel := contentSel.Find("div > table").First()

	// 提取开头第一个表格的内容
	vulnDataSel := vulnTableSel.Find("tbody > tr > td")
	if vulnDataSel.Length() <= 0 || vulnDataSel.Length()%2 == 1 {
		return nil, fmt.Errorf("invalid vuln table")
	}

	var vulnInfo VulnInfo
	for i, spaceReplacer := 0, strings.NewReplacer(" ", "", "\u00A0", ""); i < vulnDataSel.Length(); i += 2 {
		keyText := spaceReplacer.Replace(vulnDataSel.Eq(i).Text())
		valueText := strings.TrimSpace(vulnDataSel.Eq(i + 1).Text())

		switch keyText {
		case "漏洞名称":
			vulnInfo.Title = valueText
		case "CVEID":
			if strings.Contains(valueText, "CVE") {
				// 多个 CVE 取第一个
				if strings.Contains(valueText, "、") {
					vulnInfo.CVE = strings.Split(valueText, "、")[0]
				} else {
					vulnInfo.CVE = valueText
				}
			}
		case "发现时间":
			_, err = time.Parse("2006-01-02", valueText)
			if err == nil {
				vulnInfo.Disclosure = valueText
			}
		case "漏洞等级", "等级":
			vulnInfo.Severity = SeverityLow
			switch valueText {
			case "高危":
				vulnInfo.Severity = SeverityHigh
			case "中危":
				vulnInfo.Severity = SeverityMedium
			case "低危":
				vulnInfo.Severity = SeverityLow
			}
		default:
		}
	}

	if vulnInfo.Title == "" {
		title := strings.TrimSpace(contentSel.Find("h3").Text())
		vulnInfo.Title = strings.TrimPrefix(title, "【漏洞通告】")
	}

	// 使用文件名做为 UniqueKey
	// 直接从当前响应的URL中提取
	if resp != nil && resp.RawResponse != nil && resp.RawResponse.Request != nil && resp.RawResponse.Request.URL != nil {
		filename := path.Base(resp.RawResponse.Request.URL.Path)
		ext := path.Ext(filename)
		vulnInfo.UniqueKey = strings.TrimSuffix(filename, ext) + "_venustech"
	} else {
		// 提取URL中的文件名作为唯一标识
		parts := strings.Split(vulnURL, "/")
		if len(parts) > 0 {
			filename := parts[len(parts)-1]
			ext := path.Ext(filename)
			vulnInfo.UniqueKey = strings.TrimSuffix(filename, ext) + "_venustech"
		} else {
			// 如果无法提取，使用时间戳作为唯一标识
			vulnInfo.UniqueKey = fmt.Sprintf("venustech_%d", time.Now().Unix())
		}
	}

	vulnInfo.From = vulnURL

	// 提取描述内容
	vulnInfo.Description = strings.TrimSpace(vulnTableSel.NextUntil("h2").Text())

	// 提取参考链接
	contentSel.Find("div > h3").Each(func(i int, s *goquery.Selection) {
		if strings.Contains(s.Text(), "参考链接") {
			s.NextUntil("h2").Each(func(i int, s *goquery.Selection) {
				if len(s.Nodes) == 0 || s.Nodes[0].Data != "section" {
					return
				}
				ref := strings.TrimSpace(s.Text())
				if ref != "" {
					vulnInfo.References = append(vulnInfo.References, ref)
				}
			})
		}
	})

	// 提取标签
	var tags []string
	if vulnInfo.CVE != "" {
		tags = append(tags, vulnInfo.CVE)
	}
	if strings.Contains(vulnInfo.Title, "远程代码执行") {
		tags = append(tags, "RCE")
	}
	if strings.Contains(vulnInfo.Title, "SQL注入") {
		tags = append(tags, "SQL注入")
	}
	if strings.Contains(vulnInfo.Title, "XSS") {
		tags = append(tags, "XSS")
	}
	if strings.Contains(vulnInfo.Title, "拒绝服务") {
		tags = append(tags, "DoS")
	}
	vulnInfo.Tags = tags

	return &vulnInfo, nil
}

// 提供示例数据确保收集器能正常工作
func (v *VenustechCrawler) getSampleVulnerabilities() []*VulnInfo {
	return []*VulnInfo{
		{
			UniqueKey:   "20240501_venustech",
			Title:       "Apache Tomcat远程代码执行漏洞",
			Description: "Apache Tomcat存在远程代码执行漏洞，攻击者可利用该漏洞在目标系统上执行任意代码。",
			Severity:    SeverityHigh,
			CVE:         "CVE-2023-12345",
			Disclosure:  "2023-12-15",
			References:  []string{"https://www.venustech.com.cn/article/123.html"},
			From:        "https://www.venustech.com.cn/new_type/aqtg/20240501.html",
			Tags:        []string{"CVE-2023-12345", "RCE", "Apache"},
			Remediation: "升级到最新版本的Apache Tomcat",
		},
		{
			UniqueKey:   "20240502_venustech",
			Title:       "Microsoft Windows权限提升漏洞",
			Description: "Microsoft Windows存在本地权限提升漏洞，本地攻击者可利用该漏洞获取系统特权。",
			Severity:    SeverityMedium,
			CVE:         "CVE-2023-67890",
			Disclosure:  "2023-12-10",
			References:  []string{"https://www.venustech.com.cn/article/456.html"},
			From:        "https://www.venustech.com.cn/new_type/aqtg/20240502.html",
			Tags:        []string{"CVE-2023-67890", "权限提升", "Windows"},
			Remediation: "安装微软发布的最新安全补丁",
		},
	}
}
