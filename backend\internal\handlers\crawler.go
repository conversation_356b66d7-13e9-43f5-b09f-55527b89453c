package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/crawlers"
	"vulnerability_push/internal/models"
	"vulnerability_push/internal/utils"
)

// CrawlerHandler 采集器处理器
type CrawlerHandler struct {
	*BaseHandler
	db *gorm.DB
}

// NewCrawlerHandler 创建采集器处理器
func NewCrawlerHandler(db *gorm.DB) *CrawlerHandler {
	return &CrawlerHandler{
		BaseHandler: NewBaseHandler(),
		db:          db,
	}
}

// GetCrawlersRequest 获取采集器列表请求
type GetCrawlersRequest struct {
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"pageSize,default=20"`
	Keyword  string `form:"keyword"`
	Type     string `form:"type"`
	Status   *bool  `form:"status"`
}

// CreateCrawlerRequest 创建采集器请求
type CreateCrawlerRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Type        string                 `json:"type" binding:"required"`
	Description string                 `json:"description"`
	Config      map[string]interface{} `json:"config"`
	Interval    string                 `json:"interval,default=manual"` // 采集间隔类型
	Status      bool                   `json:"status,default=true"`
}

// UpdateCrawlerRequest 更新采集器请求
type UpdateCrawlerRequest struct {
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Config      map[string]interface{} `json:"config"`
	Interval    string                 `json:"interval"` // 采集间隔类型
	Status      *bool                  `json:"status"`   // 使用指针以区分false和未设置
}

// RunCrawlerRequest 运行采集器请求
type RunCrawlerRequest struct {
	PageLimit int    `json:"pageLimit,default=10"`
	StartDate string `json:"startDate"`
	EndDate   string `json:"endDate"`
}

// GetCrawlers 获取采集器列表
func (h *CrawlerHandler) GetCrawlers(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GetCrawlersRequest
	if err := h.BindQuery(c, &req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 验证和修正分页参数
	req.Page, req.PageSize = models.ValidatePageParams(req.Page, req.PageSize)

	// 构建查询
	query := h.db.Model(&models.Crawler{})

	// 关键词搜索
	if req.Keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 类型过滤
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}

	// 状态过滤
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取采集器总数失败: "+err.Error())
		return
	}

	// 获取数据
	var crawlers []models.Crawler
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(req.PageSize).Find(&crawlers).Error; err != nil {
		h.InternalServerError(c, "获取采集器列表失败: "+err.Error())
		return
	}

	// 构建响应数据
	var responseData []map[string]interface{}
	for _, crawler := range crawlers {
		data := map[string]interface{}{
			"id":          crawler.ID,
			"name":        crawler.Name,
			"type":        crawler.Type,
			"description": crawler.Description,
			"config":      crawler.GetConfig(),
			"interval":    crawler.Interval,
			"status":      crawler.Status,
			"lastRunAt":   crawler.LastRunAt,
			"createdAt":   crawler.CreatedAt,
			"updatedAt":   crawler.UpdatedAt,
		}
		responseData = append(responseData, data)
	}

	// 直接返回数组，匹配V1版本的响应格式
	h.Success(c, responseData)
}

// GetCrawler 获取单个采集器
func (h *CrawlerHandler) GetCrawler(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.BadRequest(c, "无效的采集器ID")
		return
	}

	var crawler models.Crawler
	if err := h.db.First(&crawler, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "采集器不存在")
			return
		}
		h.InternalServerError(c, "获取采集器失败: "+err.Error())
		return
	}

	responseData := map[string]interface{}{
		"id":          crawler.ID,
		"name":        crawler.Name,
		"type":        crawler.Type,
		"description": crawler.Description,
		"config":      crawler.GetConfig(),
		"interval":    crawler.Interval,
		"status":      crawler.Status,
		"lastRunAt":   crawler.LastRunAt,
		"createdAt":   crawler.CreatedAt,
		"updatedAt":   crawler.UpdatedAt,
	}

	h.Success(c, responseData)
}

// CreateCrawler 创建采集器
func (h *CrawlerHandler) CreateCrawler(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req CreateCrawlerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 验证采集器类型
	if !models.IsValidCrawlerType(req.Type) {
		h.BadRequest(c, "无效的采集器类型")
		return
	}

	// 检查名称是否重复
	var count int64
	if err := h.db.Model(&models.Crawler{}).Where("name = ?", req.Name).Count(&count).Error; err != nil {
		h.InternalServerError(c, "检查采集器名称失败: "+err.Error())
		return
	}
	if count > 0 {
		h.BadRequest(c, "采集器名称已存在")
		return
	}

	// 创建采集器
	crawler := models.Crawler{
		Name:        req.Name,
		Type:        req.Type,
		Description: req.Description,
		Interval:    req.Interval,
		Status:      req.Status,
	}

	// 设置配置
	if req.Config != nil {
		if err := crawler.SetConfig(req.Config); err != nil {
			h.BadRequest(c, "配置格式错误: "+err.Error())
			return
		}
	}

	if err := h.db.Create(&crawler).Error; err != nil {
		h.InternalServerError(c, "创建采集器失败: "+err.Error())
		return
	}

	responseData := map[string]interface{}{
		"id":          crawler.ID,
		"name":        crawler.Name,
		"type":        crawler.Type,
		"description": crawler.Description,
		"config":      crawler.GetConfig(),
		"interval":    crawler.Interval,
		"status":      crawler.Status,
		"lastRunAt":   crawler.LastRunAt,
		"createdAt":   crawler.CreatedAt,
		"updatedAt":   crawler.UpdatedAt,
	}

	h.SuccessWithMessage(c, "创建采集器成功", responseData)
}

// UpdateCrawler 更新采集器
func (h *CrawlerHandler) UpdateCrawler(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.BadRequest(c, "无效的采集器ID")
		return
	}

	var req UpdateCrawlerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 获取采集器
	var crawler models.Crawler
	if err := h.db.First(&crawler, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "采集器不存在")
			return
		}
		h.InternalServerError(c, "获取采集器失败: "+err.Error())
		return
	}

	// 更新字段
	if req.Name != "" {
		// 检查名称是否重复（排除自己）
		var count int64
		if err := h.db.Model(&models.Crawler{}).Where("name = ? AND id != ?", req.Name, crawler.ID).Count(&count).Error; err != nil {
			h.InternalServerError(c, "检查采集器名称失败: "+err.Error())
			return
		}
		if count > 0 {
			h.BadRequest(c, "采集器名称已存在")
			return
		}
		crawler.Name = req.Name
	}

	if req.Description != "" {
		crawler.Description = req.Description
	}

	if req.Interval != "" {
		// 验证采集间隔类型
		validIntervals := []string{
			models.IntervalCustom,
			models.IntervalHourly,
			models.IntervalDaily,
			models.IntervalWeekly,
			models.IntervalMonthly,
			models.IntervalManual,
		}
		isValid := false
		for _, valid := range validIntervals {
			if req.Interval == valid {
				isValid = true
				break
			}
		}
		if !isValid {
			h.BadRequest(c, "无效的采集间隔类型")
			return
		}
		crawler.Interval = req.Interval

		// 如果采集器是启用状态且不是手动模式，更新下次运行时间
		if crawler.Status && crawler.Interval != models.IntervalManual {
			crawler.UpdateNextRunTime()
		}
	}

	if req.Status != nil {
		crawler.Status = *req.Status
	}

	// 更新配置
	if req.Config != nil {
		if err := crawler.SetConfig(req.Config); err != nil {
			h.BadRequest(c, "配置格式错误: "+err.Error())
			return
		}
	}

	if err := h.db.Save(&crawler).Error; err != nil {
		h.InternalServerError(c, "更新采集器失败: "+err.Error())
		return
	}

	responseData := map[string]interface{}{
		"id":          crawler.ID,
		"name":        crawler.Name,
		"type":        crawler.Type,
		"description": crawler.Description,
		"config":      crawler.GetConfig(),
		"interval":    crawler.Interval,
		"status":      crawler.Status,
		"lastRunAt":   crawler.LastRunAt,
		"createdAt":   crawler.CreatedAt,
		"updatedAt":   crawler.UpdatedAt,
	}

	h.SuccessWithMessage(c, "更新采集器成功", responseData)
}

// GetCrawlerIntervals 获取采集间隔选项
func (h *CrawlerHandler) GetCrawlerIntervals(c *gin.Context) {
	intervals := []map[string]string{
		{"value": models.IntervalCustom, "label": "自定义间隔"},
		{"value": models.IntervalHourly, "label": "每小时"},
		{"value": models.IntervalDaily, "label": "每天"},
		{"value": models.IntervalWeekly, "label": "每周"},
		{"value": models.IntervalMonthly, "label": "每月"},
		{"value": models.IntervalManual, "label": "手动"},
	}

	h.Success(c, intervals)
}

// DeleteCrawler 删除采集器
func (h *CrawlerHandler) DeleteCrawler(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.BadRequest(c, "无效的采集器ID")
		return
	}

	// 检查采集器是否存在
	var crawler models.Crawler
	if err := h.db.First(&crawler, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "采集器不存在")
			return
		}
		h.InternalServerError(c, "获取采集器失败: "+err.Error())
		return
	}

	// 删除采集器（级联删除日志）
	if err := h.db.Delete(&crawler).Error; err != nil {
		h.InternalServerError(c, "删除采集器失败: "+err.Error())
		return
	}

	h.SuccessWithMessage(c, "删除采集器成功", nil)
}

// RunCrawler 运行采集器
func (h *CrawlerHandler) RunCrawler(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.BadRequest(c, "无效的采集器ID")
		return
	}

	var req RunCrawlerRequest
	// 尝试绑定JSON，如果失败（比如空请求体），使用默认值
	if err := c.ShouldBindJSON(&req); err != nil {
		// 如果是EOF错误（空请求体），使用默认值
		if err.Error() == "EOF" {
			req = RunCrawlerRequest{
				PageLimit: 0, // 设置为0，后面会从采集器配置中读取
				StartDate: "",
				EndDate:   "",
			}
		} else {
			h.ValidationError(c, err)
			return
		}
	}

	// 获取采集器
	var crawler models.Crawler
	if err := h.db.First(&crawler, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "采集器不存在")
			return
		}
		h.InternalServerError(c, "获取采集器失败: "+err.Error())
		return
	}

	// 检查采集器是否启用
	if !crawler.Status {
		h.BadRequest(c, "采集器已禁用")
		return
	}

	// 创建采集日志
	crawlerLog := models.CrawlerLog{
		CrawlerID: crawler.ID,
	}
	crawlerLog.MarkAsStarted()

	if err := h.db.Create(&crawlerLog).Error; err != nil {
		h.InternalServerError(c, "创建采集日志失败: "+err.Error())
		return
	}

	// 异步执行采集任务
	go h.runCrawlerTask(&crawler, &crawlerLog, req.PageLimit, req.StartDate, req.EndDate)

	h.SuccessWithMessage(c, "采集任务已启动", map[string]interface{}{
		"logId": crawlerLog.ID,
	})
}

// runCrawlerTask 执行采集任务
func (h *CrawlerHandler) runCrawlerTask(crawler *models.Crawler, log *models.CrawlerLog, pageLimit int, startDate, endDate string) {
	defer func() {
		if r := recover(); r != nil {
			log.MarkAsFailed(fmt.Sprintf("采集任务异常: %v", r))
			h.db.Save(log)
		}
	}()

	// 如果API请求中没有指定pageLimit，从采集器配置中读取
	if pageLimit <= 0 {
		// 尝试从采集器配置中获取pageLimit
		if crawler.Config != "" {
			var config map[string]interface{}
			if err := json.Unmarshal([]byte(crawler.Config), &config); err == nil {
				if pl, exists := config["pageLimit"]; exists {
					if plInt, ok := pl.(float64); ok {
						pageLimit = int(plInt)
					}
				}
			}
		}

		// 如果配置中也没有，使用默认值
		if pageLimit <= 0 {
			pageLimit = 10
		}
	}

	// 根据采集器类型创建对应的采集器实例
	var grabber crawlers.Grabber
	switch crawler.Type {
	case models.CrawlerTypeChaitin:
		grabber = crawlers.NewChaitinCrawler()
	case models.CrawlerTypeQianxin:
		grabber = crawlers.NewQianxinCrawler()
	case models.CrawlerTypeAliyun:
		grabber = crawlers.NewAliyunCrawler()
	case models.CrawlerTypeThreatbook:
		grabber = crawlers.NewThreatbookCrawler()
	case models.CrawlerTypeSeebug:
		grabber = crawlers.NewSeebugCrawler()
	case models.CrawlerTypeVenustech:
		grabber = crawlers.NewVenustechCrawler()
	case models.CrawlerTypeOSCS:
		grabber = crawlers.NewOSCSCrawler()
	case models.CrawlerTypeNVD:
		// NVD需要API密钥
		config := crawler.GetConfig()
		apiKey := ""
		if key, exists := config["apiKey"]; exists {
			if keyStr, ok := key.(string); ok {
				apiKey = keyStr
			}
		}
		grabber = crawlers.NewNVDCrawler(apiKey)
	default:
		log.MarkAsFailed("不支持的采集器类型: " + crawler.Type)
		h.db.Save(log)
		return
	}

	// 定义检查漏洞是否存在的函数
	checkVulnExists := func(vulnID string) bool {
		var count int64
		h.db.Model(&models.Vulnerability{}).Where("vuln_id = ?", vulnID).Count(&count)
		exists := count > 0
		if exists {
			utils.Debugf("[采集器] 漏洞 %s 已存在于数据库中", vulnID)
		}
		return exists
	}

	// 执行采集
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	// 记录采集开始
	utils.Infof("[采集器] 开始执行采集器 %s (ID: %d), 类型: %s, 页数限制: %d",
		crawler.Name, crawler.ID, crawler.Type, pageLimit)

	vulns, err := grabber.GetUpdateWithCheck(ctx, pageLimit, startDate, endDate, checkVulnExists)
	if err != nil {
		utils.Errorf("[采集器] 采集器 %s (ID: %d) 执行失败: %v", crawler.Name, crawler.ID, err)
		log.MarkAsFailed("采集失败: " + err.Error())
		h.db.Save(log)
		return
	}

	utils.Infof("[采集器] 采集器 %s (ID: %d) 获取到 %d 条漏洞数据",
		crawler.Name, crawler.ID, len(vulns))

	// 记录采集到的漏洞总数
	totalCount := len(vulns)

	// 处理采集到的漏洞
	var newCount int
	var errorCount int
	for i, vulnInfo := range vulns {
		utils.Debugf("[采集器] 处理第 %d 个漏洞: %s (UniqueKey: %s)", i+1, vulnInfo.Title, vulnInfo.UniqueKey)

		// 转换为models.VulnInfo
		modelVulnInfo := &models.VulnInfo{
			UniqueKey:   vulnInfo.UniqueKey,
			Title:       vulnInfo.Title,
			Description: vulnInfo.Description,
			Severity:    vulnInfo.Severity,
			CVE:         vulnInfo.CVE,
			CWE:         vulnInfo.CWE,
			VulnType:    vulnInfo.VulnType,
			Score:       vulnInfo.Score,
			Disclosure:  vulnInfo.Disclosure,
			References:  vulnInfo.References,
			From:        vulnInfo.From,
			Tags:        vulnInfo.Tags,
			Remediation: vulnInfo.Remediation,
		}

		// 创建漏洞记录
		vuln := models.Vulnerability{}
		vuln.SetFromVulnInfo(modelVulnInfo)

		if err := h.db.Create(&vuln).Error; err != nil {
			// 记录错误但继续处理其他漏洞
			utils.Errorf("[采集器] 保存漏洞失败: %s, 错误: %v", vulnInfo.UniqueKey, err)
			errorCount++
			continue
		}

		utils.Debugf("[采集器] 成功保存漏洞: %s", vulnInfo.UniqueKey)
		newCount++
	}

	// 更新采集器最后运行时间
	crawler.UpdateLastRunTime()
	h.db.Save(crawler)

	// 标记日志为成功，提供更详细的信息
	var message string
	if totalCount == 0 {
		message = "采集完成，未获取到新的漏洞数据"
	} else if newCount == 0 {
		message = fmt.Sprintf("采集完成，获取到 %d 条漏洞但均已存在于数据库中", totalCount)
	} else {
		message = fmt.Sprintf("采集完成，共获取 %d 条漏洞，新增 %d 条", totalCount, newCount)
		if errorCount > 0 {
			message += fmt.Sprintf("，%d 条保存失败", errorCount)
		}
	}

	log.MarkAsSuccess(newCount, message)
	h.db.Save(log)
}

// GetCrawlerLogsRequest 获取采集器日志请求
type GetCrawlerLogsRequest struct {
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"pageSize,default=20"`
	Status   string `form:"status"`
}

// GetCrawlerLogs 获取采集器日志
func (h *CrawlerHandler) GetCrawlerLogs(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.BadRequest(c, "无效的采集器ID")
		return
	}

	var req GetCrawlerLogsRequest
	if err := h.BindQuery(c, &req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 验证和修正分页参数
	req.Page, req.PageSize = models.ValidatePageParams(req.Page, req.PageSize)

	// 检查采集器是否存在
	var crawler models.Crawler
	if err := h.db.First(&crawler, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "采集器不存在")
			return
		}
		h.InternalServerError(c, "获取采集器失败: "+err.Error())
		return
	}

	// 构建查询
	query := h.db.Model(&models.CrawlerLog{}).Where("crawler_id = ?", uint(id))

	// 状态过滤
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取日志总数失败: "+err.Error())
		return
	}

	// 获取数据
	var logs []models.CrawlerLog
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(req.PageSize).Find(&logs).Error; err != nil {
		h.InternalServerError(c, "获取日志列表失败: "+err.Error())
		return
	}

	// 构建响应数据
	var responseData []map[string]interface{}
	for _, log := range logs {
		data := map[string]interface{}{
			"id":        log.ID,
			"crawlerId": log.CrawlerID,
			"status":    log.Status,
			"count":     log.Count,
			"message":   log.Message,
			"startedAt": log.StartedAt,
			"endedAt":   log.EndedAt,
			"duration":  log.Duration,
			"createdAt": log.CreatedAt,
		}
		responseData = append(responseData, data)
	}

	// 构建分页信息
	pagination := models.CalculatePagination(total, req.Page, req.PageSize)

	h.Success(c, map[string]interface{}{
		"list":       responseData,
		"pagination": pagination,
	})
}

// GetCrawlerTypes 获取所有采集器类型
func (h *CrawlerHandler) GetCrawlerTypes(c *gin.Context) {
	types := models.GetAllCrawlerTypes()

	// 构建类型信息
	var typeInfos []map[string]interface{}
	for _, t := range types {
		info := map[string]interface{}{
			"type": t,
			"name": h.getCrawlerTypeName(t),
		}
		typeInfos = append(typeInfos, info)
	}

	h.Success(c, typeInfos)
}

// getCrawlerTypeName 获取采集器类型名称
func (h *CrawlerHandler) getCrawlerTypeName(crawlerType string) string {
	switch crawlerType {
	case models.CrawlerTypeChaitin:
		return "长亭漏洞库"
	case models.CrawlerTypeQianxin:
		return "奇安信威胁情报中心"
	case models.CrawlerTypeAliyun:
		return "阿里云漏洞库"
	case models.CrawlerTypeThreatbook:
		return "微步威胁情报"
	case models.CrawlerTypeSeebug:
		return "知道创宇Seebug"
	case models.CrawlerTypeVenustech:
		return "启明星辰漏洞通告"
	case models.CrawlerTypeOSCS:
		return "OSCS开源安全情报"
	case models.CrawlerTypeNVD:
		return "NVD国家漏洞数据库"
	default:
		return crawlerType
	}
}

// GetAllCrawlerLogs 获取所有采集器日志
func (h *CrawlerHandler) GetAllCrawlerLogs(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GetCrawlerLogsRequest
	if err := h.BindQuery(c, &req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 验证和修正分页参数
	req.Page, req.PageSize = models.ValidatePageParams(req.Page, req.PageSize)

	// 构建查询
	query := h.db.Model(&models.CrawlerLog{}).Preload("Crawler")

	// 状态过滤
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取日志总数失败: "+err.Error())
		return
	}

	// 获取数据
	var logs []models.CrawlerLog
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(req.PageSize).Find(&logs).Error; err != nil {
		h.InternalServerError(c, "获取日志列表失败: "+err.Error())
		return
	}

	// 构建响应数据
	var responseData []map[string]interface{}
	for _, log := range logs {
		data := map[string]interface{}{
			"id":          log.ID,
			"crawlerId":   log.CrawlerID,
			"crawlerName": log.Crawler.Name,
			"status":      log.Status,
			"count":       log.Count,
			"message":     log.Message,
			"startedAt":   log.StartedAt,
			"endedAt":     log.EndedAt,
			"duration":    log.Duration,
			"createdAt":   log.CreatedAt,
		}
		responseData = append(responseData, data)
	}

	// 构建分页信息
	pagination := models.CalculatePagination(total, req.Page, req.PageSize)

	h.Success(c, map[string]interface{}{
		"list":       responseData,
		"pagination": pagination,
	})
}

// DeleteCrawlerLogs 删除采集器日志
func (h *CrawlerHandler) DeleteCrawlerLogs(c *gin.Context) {
	crawlerIDStr := c.Query("crawlerId")

	if crawlerIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "缺少采集器ID参数",
		})
		return
	}

	crawlerID, err := strconv.ParseUint(crawlerIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "采集器ID格式错误",
		})
		return
	}

	// 检查采集器是否存在
	var crawler models.Crawler
	if err := h.db.First(&crawler, crawlerID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "采集器不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询采集器失败",
		})
		return
	}

	// 删除指定采集器的所有日志
	result := h.db.Where("crawler_id = ?", crawlerID).Delete(&models.CrawlerLog{})
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除日志失败: " + result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": fmt.Sprintf("成功删除 %d 条日志记录", result.RowsAffected),
		"data": gin.H{
			"deletedCount": result.RowsAffected,
		},
	})
}
