package handlers

import (
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/internal/models"
)

// PushIOCIntelligenceRequest 推送IOC情报请求
type PushIOCIntelligenceRequest struct {
	ChannelID *int `json:"channelId"`
	PolicyID  *int `json:"policyId"`
}

// BatchPushIOCIntelligenceRequest 批量推送IOC情报请求
type BatchPushIOCIntelligenceRequest struct {
	IDs       []int `json:"ioc_ids" binding:"required"`
	ChannelID *int  `json:"channel_id"`
	PolicyID  *int  `json:"policy_id"`
}

// PushIOCIntelligence 推送IOC情报
func (h *IOCHandler) PushIOCIntelligence(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		return
	}

	var req PushIOCIntelligenceRequest
	// 尝试从JSON请求体绑定
	if err := c.ShouldBindJSON(&req); err != nil {
		// 如果JSON绑定失败，尝试从查询参数获取
		channelIDStr := c.Query("channel_id")
		policyIDStr := c.Query("policy_id")

		if channelIDStr != "" {
			if channelID, err := strconv.Atoi(channelIDStr); err == nil {
				req.ChannelID = &channelID
			}
		}
		if policyIDStr != "" {
			if policyID, err := strconv.Atoi(policyIDStr); err == nil {
				req.PolicyID = &policyID
			}
		}
	}

	// 获取IOC情报
	var iocIntelligence models.IOCIntelligence
	if err := h.db.First(&iocIntelligence, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "IOC情报不存在")
		} else {
			h.InternalServerError(c, "获取IOC情报失败: "+err.Error())
		}
		return
	}

	// 获取推送参数
	channelIDStr := c.Query("channel_id")
	policyIDStr := c.Query("policy_id")

	// 如果指定了策略ID，使用策略推送
	if policyIDStr != "" || req.PolicyID != nil {
		var policyID uint
		if req.PolicyID != nil {
			policyID = uint(*req.PolicyID)
		} else {
			fmt.Sscanf(policyIDStr, "%d", &policyID)
		}

		var policy models.PushPolicy
		if err := h.db.First(&policy, policyID).Error; err != nil {
			h.NotFound(c, "推送策略不存在")
			return
		}

		// 使用策略推送
		if err := h.pushIOCIntelligenceUsingPolicy(&iocIntelligence, &policy); err != nil {
			h.InternalServerError(c, "推送失败: "+err.Error())
			return
		}

		h.Success(c, gin.H{"message": "推送成功"})
		return
	}

	// 如果指定了通道ID，则直接推送到该通道
	if channelIDStr != "" || req.ChannelID != nil {
		var channelID uint
		if req.ChannelID != nil {
			channelID = uint(*req.ChannelID)
		} else {
			fmt.Sscanf(channelIDStr, "%d", &channelID)
		}

		var channel models.PushChannel
		if err := h.db.First(&channel, channelID).Error; err != nil {
			h.NotFound(c, "推送通道不存在")
			return
		}

		if !channel.Status {
			h.BadRequest(c, "推送通道已禁用")
			return
		}

		// 创建推送记录
		record := models.IOCIntelligencePushRecord{
			IOCIntelligenceID: iocIntelligence.ID,
			ChannelID:         channel.ID,
			ChannelName:       channel.Name,
			ChannelType:       channel.Type,
			Status:            "success",
			PushedAt:          time.Now().Unix(),
		}

		// 转换为push包中的类型
		pushIOCIntel := h.convertToIOCIntelligencePush(&iocIntelligence)
		pushChannel := h.convertToPushChannel(&channel)
		pushRecord := h.convertToIOCIntelligencePushRecord(&record)

		// 根据通道类型推送
		var pushErr error
		switch channel.Type {
		case "wechat_bot":
			pushErr = h.pushIOCIntelligenceToWechatBot(pushIOCIntel, pushChannel, pushRecord)
		case "dingding":
			pushErr = h.pushIOCIntelligenceToDingDing(pushIOCIntel, pushChannel, pushRecord)
		case "webhook":
			pushErr = h.pushIOCIntelligenceToWebhook(pushIOCIntel, pushChannel, pushRecord)
		case "lark":
			pushErr = h.pushIOCIntelligenceToLark(pushIOCIntel, pushChannel, pushRecord)
		default:
			pushErr = fmt.Errorf("不支持的通道类型: %s", channel.Type)
		}

		// 更新推送记录状态
		if pushErr != nil {
			record.Status = "failed"
			record.ErrorMessage = pushErr.Error()
		}

		// 保存推送记录
		if err := h.db.Create(&record).Error; err != nil {
			// 如果表不存在，尝试创建表
			if strings.Contains(err.Error(), "doesn't exist") {
				h.db.AutoMigrate(&models.IOCIntelligencePushRecord{})
				// 重新尝试保存
				if err := h.db.Create(&record).Error; err != nil {
					fmt.Printf("保存推送记录失败: %v\n", err)
				}
			} else {
				fmt.Printf("保存推送记录失败: %v\n", err)
			}
		}

		// 更新IOC情报的推送状态
		if pushErr != nil {
			// 推送失败，标记为失败状态
			iocIntelligence.MarkAsPushFailed()
			h.db.Save(&iocIntelligence)
			h.InternalServerError(c, "推送失败: "+pushErr.Error())
			return
		} else {
			// 推送成功，标记为已推送状态
			iocIntelligence.MarkAsPushed()
			h.db.Save(&iocIntelligence)
		}

		h.Success(c, gin.H{"message": "推送成功"})
		return
	}

	// 默认推送（推送到所有启用的通道）
	if err := h.pushIOCIntelligenceToDefault(&iocIntelligence); err != nil {
		// 推送失败，标记为失败状态
		iocIntelligence.MarkAsPushFailed()
		h.db.Save(&iocIntelligence)
		h.InternalServerError(c, "推送失败: "+err.Error())
		return
	}

	// 推送成功，标记为已推送状态
	iocIntelligence.MarkAsPushed()
	h.db.Save(&iocIntelligence)
	h.Success(c, gin.H{"message": "推送成功"})
}

// BatchPushIOCIntelligence 批量推送IOC情报
func (h *IOCHandler) BatchPushIOCIntelligence(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 添加调试日志
	body, _ := c.GetRawData()
	fmt.Printf("批量推送请求体: %s\n", string(body))

	// 重新设置请求体，因为GetRawData会消耗它
	c.Request.Body = io.NopCloser(strings.NewReader(string(body)))

	var req BatchPushIOCIntelligenceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		fmt.Printf("JSON绑定错误: %v\n", err)
		h.ValidationError(c, err)
		return
	}

	fmt.Printf("解析后的请求: IDs=%v, ChannelID=%v, PolicyID=%v\n", req.IDs, req.ChannelID, req.PolicyID)

	// 如果请求体中没有推送配置，则从查询参数获取（向后兼容）
	if req.ChannelID == nil {
		if channelIDStr := c.Query("channel_id"); channelIDStr != "" {
			if channelID, err := strconv.Atoi(channelIDStr); err == nil {
				req.ChannelID = &channelID
			}
		}
	}
	if req.PolicyID == nil {
		if policyIDStr := c.Query("policy_id"); policyIDStr != "" {
			if policyID, err := strconv.Atoi(policyIDStr); err == nil {
				req.PolicyID = &policyID
			}
		}
	}

	if len(req.IDs) == 0 {
		h.ValidationError(c, fmt.Errorf("请选择要推送的IOC情报"))
		return
	}

	// 获取IOC情报列表
	var iocIntelligences []models.IOCIntelligence
	if err := h.db.Where("id IN ?", req.IDs).Find(&iocIntelligences).Error; err != nil {
		h.InternalServerError(c, "获取IOC情报失败: "+err.Error())
		return
	}

	if len(iocIntelligences) == 0 {
		h.NotFound(c, "未找到指定的IOC情报")
		return
	}

	// 使用批量合并推送
	var err error

	if req.PolicyID != nil {
		// 推送到指定策略
		var policy models.PushPolicy
		if dbErr := h.db.First(&policy, *req.PolicyID).Error; dbErr != nil {
			err = fmt.Errorf("推送策略不存在")
		} else {
			err = h.batchPushIOCIntelligenceUsingPolicy(iocIntelligences, &policy)
		}
	} else if req.ChannelID != nil {
		// 推送到指定通道
		var channel models.PushChannel
		if dbErr := h.db.First(&channel, *req.ChannelID).Error; dbErr != nil {
			err = fmt.Errorf("推送通道不存在")
		} else if !channel.Status {
			err = fmt.Errorf("推送通道已禁用")
		} else {
			// 批量推送到指定通道
			err = h.batchPushIOCIntelligenceToChannel(iocIntelligences, &channel)
		}
	} else {
		// 默认批量推送
		err = h.batchPushIOCIntelligenceToDefault(iocIntelligences)
	}

	if err != nil {
		h.InternalServerError(c, "批量推送失败: "+err.Error())
		return
	}

	h.Success(c, gin.H{
		"message": fmt.Sprintf("批量推送成功 (%d条)", len(iocIntelligences)),
	})
}
