# 用户管理API修复文档

## 问题描述

用户管理页面中的"查看API密钥"和"重置API密钥"功能出现错误：
- 错误1: `{"code": 404, "msg": "用户不存在"}`
- 错误2: `{"code": 500, "msg": "重置API密钥失败: 用户不存在"}`

## 问题原因

1. **数据不一致**：前端显示的用户ID与数据库中的实际用户ID不匹配
   - 前端请求用户ID为1的用户详情
   - 数据库中实际用户ID为2

2. **后端API未实现**：用户管理相关的API方法还是TODO状态，返回硬编码的临时数据
   - `GetUsers` 方法返回硬编码的用户ID为1
   - `GetUser` 方法调用真实服务，查找数据库中的用户
   - 导致前后端数据不一致

## 修复方案

### 1. 修复GetUsers方法
将TODO状态的GetUsers方法改为调用真实的用户服务：

```go
// 修复前（硬编码数据）
users := []map[string]interface{}{
    {
        "id":        1,  // 硬编码ID为1
        "username":  "admin",
        "email":     "<EMAIL>",
        "role":      "admin",
        "status":    true,
        "apiKey":    "admin_api_key_placeholder",
        "createdAt": 1640995200,
    },
}

// 修复后（调用真实服务）
result, err := h.userService.GetUsers(c.Request.Context(), &service.GetUsersRequest{
    PaginationRequest: service.PaginationRequest{
        Page:     req.Page,
        PageSize: req.PageSize,
    },
    Username: req.Username,
    Role:     req.Role,
    Status:   req.Status,
})
```

### 2. 修复CreateUser方法
```go
// 修复前（TODO状态）
// TODO: 调用用户服务创建用户

// 修复后（调用真实服务）
user, err := h.userService.CreateUser(c.Request.Context(), &service.CreateUserRequest{
    Username: req.Username,
    Password: req.Password,
    Email:    req.Email,
    Role:     req.Role,
})
```

### 3. 修复UpdateUser方法
```go
// 修复前（TODO状态）
// TODO: 调用用户服务更新用户

// 修复后（调用真实服务）
err = h.userService.UpdateUser(c.Request.Context(), id, &service.UpdateUserRequest{
    Email:  req.Email,
    Role:   req.Role,
    Status: req.Status,
})
```

### 4. 修复DeleteUser方法
```go
// 修复前（TODO状态）
// TODO: 调用用户服务删除用户

// 修复后（调用真实服务）
err = h.userService.DeleteUser(c.Request.Context(), id)
```

## 修复的文件

### backend/internal/handlers/user.go
- ✅ 修复 `GetUsers` 方法：调用真实用户服务
- ✅ 修复 `CreateUser` 方法：调用真实用户服务
- ✅ 修复 `UpdateUser` 方法：调用真实用户服务
- ✅ 修复 `DeleteUser` 方法：调用真实用户服务

## 数据库状态确认

通过数据库查询确认：
- 用户表中存在ID为2的admin用户
- 用户名：admin
- 邮箱：<EMAIL>
- 角色：admin
- 状态：启用
- API密钥：7da1e983c6f23f... (64位十六进制字符串)

## 预期结果

修复后的功能：

1. **用户列表显示**：显示数据库中的真实用户数据（ID为2）
2. **查看API密钥**：能够正确获取用户的真实API密钥
3. **重置API密钥**：能够成功重置用户的API密钥
4. **用户CRUD操作**：创建、更新、删除用户功能正常工作

## 测试步骤

1. 重启后端服务
2. 登录前端管理界面
3. 进入用户管理页面
4. 验证用户列表显示正确的用户ID（应为2）
5. 点击"查看API密钥"按钮，应显示真实的API密钥
6. 点击"重置API密钥"按钮，应成功重置并显示新的API密钥

## 注意事项

1. **数据一致性**：确保前后端使用相同的用户ID
2. **API密钥安全**：API密钥应该是64位十六进制字符串
3. **权限验证**：所有用户管理操作都需要管理员权限
4. **错误处理**：适当处理用户不存在、权限不足等错误情况

## 相关API端点

- `GET /api/admin/users` - 获取用户列表
- `GET /api/admin/users/:id` - 获取用户详情
- `POST /api/admin/users` - 创建用户
- `PUT /api/admin/users/:id` - 更新用户
- `DELETE /api/admin/users/:id` - 删除用户
- `PATCH /api/admin/users/:id/reset-api-key` - 重置API密钥
