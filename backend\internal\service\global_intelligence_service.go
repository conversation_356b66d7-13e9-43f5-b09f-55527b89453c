package service

import (
	"time"

	"gorm.io/gorm"
)

// 全局服务实例
var (
	globalTJUNService  *TJUNIntelligenceService
	globalWeibuService *WeibuIntelligenceService
)

// InitGlobalServices 初始化全局服务
func InitGlobalServices(db *gorm.DB) error {
	// 初始化天际友盟服务
	tjunConfig := GetDefaultTJUNConfig()
	globalTJUNService = NewTJUNIntelligenceService(db, tjunConfig)

	// 初始化微步服务
	globalWeibuService = NewWeibuIntelligenceService(db)

	return nil
}

// GetGlobalTJUNServiceDirect 获取全局天际友盟服务（直接访问）
func GetGlobalTJUNServiceDirect() *TJUNIntelligenceService {
	return globalTJUNService
}

// GetGlobalWeibuServiceDirect 获取全局微步服务（直接访问）
func GetGlobalWeibuServiceDirect() *WeibuIntelligenceService {
	return globalWeibuService
}

// TJUNIntelligenceResult 天际友盟情报查询结果（兼容性结构）
type TJUNIntelligenceResult struct {
	Success      bool        `json:"success"`
	Data         interface{} `json:"data"`
	ErrorMessage string      `json:"errorMessage,omitempty"`
	QueryTime    int64       `json:"queryTime"`
}

// SerializeData 序列化数据为JSON字符串
func (r *TJUNIntelligenceResult) SerializeData() (string, error) {
	if r.Data == nil {
		return "", nil
	}

	// 这里可以使用json.Marshal来序列化数据
	// 为了简化，暂时返回空字符串
	return "", nil
}

// GetQueryStatus 获取查询状态
func (r *TJUNIntelligenceResult) GetQueryStatus() string {
	if r.Success {
		return "success"
	}
	return "failed"
}

// QueryIOCIntelligenceGlobal 使用全局服务查询IOC情报（兼容性函数）
func QueryIOCIntelligenceGlobal(ioc, iocType string) *TJUNIntelligenceResult {
	if globalTJUNService == nil {
		return &TJUNIntelligenceResult{
			Success:      false,
			ErrorMessage: "天际友盟服务不可用",
			QueryTime:    time.Now().Unix(),
		}
	}

	// 调用服务查询
	req := TJUNQueryRequest{
		IOC:     ioc,
		IOCType: iocType,
	}

	response, err := globalTJUNService.QueryIOC(req)
	if err != nil {
		return &TJUNIntelligenceResult{
			Success:      false,
			ErrorMessage: err.Error(),
			QueryTime:    time.Now().Unix(),
		}
	}

	return &TJUNIntelligenceResult{
		Success:   response.Success,
		Data:      response.Data,
		QueryTime: time.Now().Unix(),
	}
}

// QueryWeibuIPReputationGlobal 使用全局服务查询微步IP信誉（兼容性函数）
func QueryWeibuIPReputationGlobal(ip string) *WeibuIntelligenceResult {
	if globalWeibuService == nil {
		return &WeibuIntelligenceResult{
			Success:      false,
			ErrorMessage: "微步威胁情报服务不可用",
			QueryTime:    time.Now().Unix(),
		}
	}

	// 调用服务查询
	req := WeibuQueryRequest{
		IOC: ip,
	}

	response, err := globalWeibuService.QueryIOC(req)
	if err != nil {
		return &WeibuIntelligenceResult{
			Success:      false,
			ErrorMessage: err.Error(),
			QueryTime:    time.Now().Unix(),
		}
	}

	return &WeibuIntelligenceResult{
		Success:   response.Success,
		Data:      response.Data,
		QueryTime: time.Now().Unix(),
	}
}
