<template>
  <div class="data-interface-container">
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="接口名称">
          <el-input
            v-model="filterForm.name"
            placeholder="输入接口名称"
            clearable
            prefix-icon="Search"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="接口类型">
          <el-select
            v-model="filterForm.type"
            placeholder="全部"
            clearable
            style="width: 180px;"
          >
            <el-option label="CCCC黑科技" value="cccc_black_tech" />
            <el-option label="ES告警数据" value="es_alarm" />
            <el-option label="REST API" value="rest" />
            <el-option label="GraphQL" value="graphql" />
            <el-option label="WebSocket" value="websocket" />
            <el-option label="Webhook" value="webhook" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="filterForm.status"
            placeholder="全部"
            clearable
          >
            <el-option label="启用" value="enabled" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">
            搜索
          </el-button>
          <el-button @click="resetFilter" :icon="RefreshLeft">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="hover" class="table-card">
      <div class="table-header">
        <div class="table-title">数据接口列表</div>
        <div class="table-actions">
          <el-button type="primary" @click="refreshList">
            <el-icon><Refresh /></el-icon> 刷新
          </el-button>
          <el-button type="success" v-if="isAdmin" @click="handleCreate">
            <el-icon><Plus /></el-icon> 新建接口
          </el-button>
          <el-button 
            type="danger" 
            v-if="isAdmin" 
            @click="handleBatchDelete"
            :disabled="multipleSelection.length === 0"
          >
            <el-icon><Delete /></el-icon> 批量删除
          </el-button>
        </div>
      </div>

      <el-table
        :data="dataInterfaces"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="接口名称" min-width="150" />
        <el-table-column prop="type" label="接口类型" width="120">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)">
              {{ getTypeLabel(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="采集配置" width="120">
          <template #default="scope">
            <el-tag v-if="scope.row.collection_enabled" type="success">
              {{ getFreqLabel(scope.row.collection_freq) }}
            </el-tag>
            <el-tag v-else type="info">手动</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="最后执行" width="150">
          <template #default="scope">
            <div v-if="scope.row.last_run_time">
              <div>{{ formatDate(scope.row.last_run_time) }}</div>
              <el-tag size="small" :type="getRunStatusTagType(scope.row.last_run_status)">
                {{ getRunStatusLabel(scope.row.last_run_status) }}
              </el-tag>
            </div>
            <span v-else class="text-gray-400">未执行</span>
          </template>
        </el-table-column>
        <el-table-column label="数据统计" width="120">
          <template #default="scope">
            <div class="text-sm">
              <div>总计: {{ scope.row.total_data_count || 0 }}</div>
              <div>最近: {{ scope.row.last_data_count || 0 }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button
              :type="scope.row.status === 'enabled' ? 'warning' : 'success'"
              size="small"
              @click="handleToggleStatus(scope.row)"
            >
              {{ scope.row.status === 'enabled' ? '禁用' : '启用' }}
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="handleRun(scope.row)"
              :disabled="scope.row.status !== 'enabled'"
            >
              <el-icon><Connection /></el-icon> 执行
            </el-button>
            <el-button type="info" size="small" @click="handleViewLogs(scope.row)">
              <el-icon><Document /></el-icon> 日志
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(scope.row)">
              <el-icon><Edit /></el-icon> 编辑
            </el-button>
            <el-button type="danger" size="small" v-if="isAdmin" @click="handleDelete(scope.row)">
              <el-icon><Delete /></el-icon> 删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 日志查看对话框 -->
    <el-dialog
      v-model="logDialogVisible"
      :title="`${currentInterface?.name} - 执行日志`"
      width="1000px"
      @close="resetLogDialog"
    >
      <div class="log-dialog-content">
        <!-- 日志筛选 -->
        <div class="log-filter-container">
          <el-form :inline="true" :model="logFilterForm" class="log-filter-form">
            <el-form-item label="状态">
              <el-select
                v-model="logFilterForm.status"
                placeholder="全部状态"
                clearable
                style="width: 120px;"
                @change="loadInterfaceLogs"
              >
                <el-option label="成功" value="success" />
                <el-option label="失败" value="failed" />
                <el-option label="运行中" value="running" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadInterfaceLogs" :icon="Refresh">
                刷新
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 日志列表 -->
        <el-table
          :data="interfaceLogs"
          v-loading="logLoading"
          stripe
          style="width: 100%"
          max-height="400px"
        >
          <el-table-column prop="status" label="状态" width="80">
            <template #default="scope">
              <el-tag :type="getLogStatusTagType(scope.row.status)" size="small">
                {{ getLogStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="startedAt" label="开始时间" width="160">
            <template #default="scope">
              <span v-if="scope.row.startedAt">{{ formatTimestamp(scope.row.startedAt) }}</span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="endedAt" label="结束时间" width="160">
            <template #default="scope">
              <span v-if="scope.row.endedAt">{{ formatTimestamp(scope.row.endedAt) }}</span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="耗时" width="80">
            <template #default="scope">
              <span v-if="scope.row.duration">{{ formatDurationSeconds(scope.row.duration) }}</span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="dataCount" label="数据量" width="80">
            <template #default="scope">
              {{ scope.row.dataCount || 0 }}
            </template>
          </el-table-column>
          <el-table-column prop="message" label="执行消息" min-width="200" show-overflow-tooltip />
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button
                v-if="scope.row.error_details"
                type="danger"
                size="small"
                @click="showErrorDetails(scope.row)"
              >
                错误详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 日志分页 -->
        <div class="log-pagination-container">
          <el-pagination
            v-model:current-page="logCurrentPage"
            v-model:page-size="logPageSize"
            :page-sizes="[10, 20, 50]"
            :total="logTotal"
            layout="total, sizes, prev, pager, next"
            @size-change="handleLogSizeChange"
            @current-change="handleLogCurrentChange"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 错误详情对话框 -->
    <el-dialog
      v-model="errorDialogVisible"
      title="错误详情"
      width="600px"
    >
      <el-input
        v-model="errorDetails"
        type="textarea"
        :rows="10"
        readonly
        placeholder="无错误详情"
      />
    </el-dialog>

    <!-- 新建/编辑接口对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="接口名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入接口名称" />
        </el-form-item>
        <el-form-item label="接口类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择接口类型" style="width: 100%" @change="handleTypeChange">
            <el-option label="CCCC黑科技" value="cccc_black_tech" />
            <el-option label="ES告警数据" value="es_alarm" />
            <el-option label="REST API" value="rest" />
            <el-option label="GraphQL" value="graphql" />
            <el-option label="WebSocket" value="websocket" />
            <el-option label="Webhook" value="webhook" />
          </el-select>
        </el-form-item>

        <!-- CCCC黑科技接口配置 -->
        <template v-if="form.type === 'cccc_black_tech'">
          <el-form-item label="主机地址" prop="config.host">
            <el-input v-model="form.config.host" placeholder="例如: **********:5443" />
          </el-form-item>
          <el-form-item label="用户密钥" prop="config.user_key">
            <el-input v-model="form.config.user_key" placeholder="请输入用户密钥" type="password" show-password />
          </el-form-item>

          <el-form-item label="定时采集">
            <el-switch v-model="form.collection_enabled" />
          </el-form-item>

          <!-- 采集频率配置 -->
          <template v-if="form.collection_enabled">
            <el-form-item label="采集频率" prop="collection_freq">
              <el-select v-model="form.collection_freq" placeholder="请选择采集频率" style="width: 100%">
                <el-option label="每小时" value="hourly" />
                <el-option label="每天" value="daily" />
                <el-option label="每周" value="weekly" />
                <el-option label="自定义" value="custom" />
              </el-select>
            </el-form-item>

            <!-- 自定义采集间隔 -->
            <el-form-item v-if="form.collection_freq === 'custom'" label="采集间隔（分钟）" prop="collection_interval">
              <div style="display: flex; align-items: center; gap: 10px;">
                <el-input-number
                  v-model="form.collection_interval"
                  :min="1"
                  :max="1440"
                  placeholder="输入分钟数"
                  style="flex: 1; max-width: 200px;"
                />
                <span style="color: #666;">分钟</span>
                <span style="color: #999; font-size: 12px;">（1-1440分钟）</span>
              </div>
            </el-form-item>

            <!-- 时间范围配置 -->
            <el-form-item label="时间范围类型" prop="time_range_type">
              <el-radio-group v-model="form.time_range_type">
                <el-radio label="relative">相对时间</el-radio>
                <el-radio label="absolute">绝对时间</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 相对时间配置 -->
            <template v-if="form.time_range_type === 'relative'">
              <el-form-item label="时间范围" prop="time_range_mode">
                <el-radio-group v-model="form.time_range_mode">
                  <el-radio label="preset">预设时间</el-radio>
                  <el-radio label="custom">自定义时间</el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- 预设时间选择 -->
              <el-form-item v-if="form.time_range_mode === 'preset'" label="预设时间" prop="time_range_value">
                <el-select v-model="form.time_range_value" placeholder="请选择时间范围" style="width: 100%">
                  <el-option label="最近1小时" :value="3600" />
                  <el-option label="最近6小时" :value="21600" />
                  <el-option label="最近12小时" :value="43200" />
                  <el-option label="最近24小时" :value="86400" />
                  <el-option label="最近3天" :value="259200" />
                  <el-option label="最近7天" :value="604800" />
                </el-select>
              </el-form-item>

              <!-- 自定义时间输入 -->
              <el-form-item v-if="form.time_range_mode === 'custom'" label="时间范围（分钟）" prop="time_range_minutes">
                <div style="display: flex; align-items: center; gap: 10px;">
                  <el-input-number
                    v-model="form.time_range_minutes"
                    :min="1"
                    :max="10080"
                    placeholder="输入分钟数"
                    style="flex: 1; max-width: 200px;"
                    @change="updateTimeRangeFromMinutes"
                  />
                  <span style="color: #666;">分钟</span>
                  <span style="color: #999; font-size: 12px;">（1-10080分钟，即最多7天）</span>
                </div>
              </el-form-item>
            </template>

            <!-- 绝对时间配置 -->
            <el-form-item v-if="form.time_range_type === 'absolute'" label="时间范围" prop="time_range_absolute">
              <el-date-picker
                v-model="form.time_range_absolute"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </template>
        </template>

        <!-- ES告警接口配置 -->
        <template v-if="form.type === 'es_alarm'">
          <el-form-item label="ES地址" prop="config.es_url">
            <el-input v-model="form.config.es_url" placeholder="例如: http://127.0.0.1:9200" />
          </el-form-item>

          <el-form-item label="定时采集">
            <el-switch v-model="form.collection_enabled" />
          </el-form-item>

          <el-form-item v-if="form.collection_enabled" label="采集频率" prop="collection_freq">
            <el-select v-model="form.collection_freq" placeholder="请选择采集频率" style="width: 100%">
              <el-option label="每小时" value="hourly" />
              <el-option label="每天" value="daily" />
              <el-option label="每周" value="weekly" />
              <el-option label="自定义" value="custom" />
            </el-select>
          </el-form-item>

          <el-form-item v-if="form.collection_enabled && form.collection_freq === 'custom'" label="采集间隔" prop="collection_interval">
            <el-input-number
              v-model="form.collection_interval"
              :min="1"
              :max="3600"
              placeholder="请输入分钟数"
              style="width: 200px"
            />
            <span style="margin-left: 8px; color: #909399;">分钟（1-3600分钟，即最长2.5天）</span>
          </el-form-item>

          <el-form-item label="时间范围类型">
            <el-radio-group v-model="form.time_range_type">
              <el-radio label="relative">相对时间</el-radio>
              <el-radio label="absolute">绝对时间</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="form.time_range_type === 'relative'" label="时间范围">
            <el-radio-group v-model="form.time_range_mode" style="margin-bottom: 10px;">
              <el-radio label="preset">预设时间</el-radio>
              <el-radio label="custom">自定义分钟数</el-radio>
            </el-radio-group>

            <el-select
              v-if="form.time_range_mode === 'preset'"
              v-model="form.time_range_value"
              placeholder="请选择时间范围"
              style="width: 100%"
            >
              <el-option label="最近1小时" :value="3600" />
              <el-option label="最近6小时" :value="21600" />
              <el-option label="最近12小时" :value="43200" />
              <el-option label="最近24小时" :value="86400" />
              <el-option label="最近3天" :value="259200" />
              <el-option label="最近7天" :value="604800" />
            </el-select>

            <div v-if="form.time_range_mode === 'custom'" style="display: flex; align-items: center; gap: 10px;">
              <el-input-number
                v-model="form.time_range_minutes"
                :min="1"
                :max="10080"
                placeholder="输入分钟数"
                style="flex: 1"
                @change="updateTimeRangeFromMinutes"
              />
              <span style="color: #666;">分钟</span>
              <span style="color: #999; font-size: 12px;">（1-10080分钟，即最多7天）</span>
            </div>
          </el-form-item>

          <el-form-item v-if="form.time_range_type === 'absolute'" label="时间范围">
            <el-date-picker
              v-model="form.time_range_absolute"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>

        </template>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入接口描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 接口测试对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="接口测试"
      width="800px"
    >
      <div class="test-container">
        <el-form :inline="true">
          <el-form-item label="请求头">
            <el-input
              v-model="testHeaders"
              type="textarea"
              :rows="3"
              placeholder='{"Content-Type": "application/json"}'
              style="width: 400px"
            />
          </el-form-item>
          <el-form-item label="请求体">
            <el-input
              v-model="testBody"
              type="textarea"
              :rows="5"
              placeholder="请输入请求体（JSON格式）"
              style="width: 400px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="executeTest" :loading="testLoading">
              发送请求
            </el-button>
          </el-form-item>
        </el-form>
        
        <el-divider>响应结果</el-divider>
        
        <div class="test-result">
          <el-input
            v-model="testResult"
            type="textarea"
            :rows="10"
            readonly
            placeholder="测试结果将显示在这里"
          />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="testDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, RefreshLeft, Refresh, Plus, Delete, Edit, Connection, Document, VideoPlay, VideoPause } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import api from './api'

// 数据接口列表
const dataInterfaces = ref([])

const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const multipleSelection = ref([])

// 筛选表单
const filterForm = ref({
  name: '',
  type: '',
  status: ''
})

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const form = ref({
  name: '',
  type: '',
  description: '',
  config: {
    host: '',
    user_key: '',
    es_url: ''
  },
  collection_enabled: false,
  collection_freq: 'hourly',
  collection_interval: 60, // 自定义采集间隔（分钟）
  time_range_type: 'relative',
  time_range_mode: 'preset', // 时间范围模式：preset（预设）或 custom（自定义）
  time_range_value: 3600,
  time_range_minutes: 60, // 自定义分钟数
  time_range_absolute: []
})

// 测试对话框相关
const testDialogVisible = ref(false)
const testLoading = ref(false)
const testHeaders = ref('{"Content-Type": "application/json"}')
const testBody = ref('')
const testResult = ref('')
const currentTestInterface = ref(null)

// 日志对话框相关
const logDialogVisible = ref(false)
const logLoading = ref(false)
const interfaceLogs = ref([])
const logCurrentPage = ref(1)
const logPageSize = ref(20)
const logTotal = ref(0)
const currentInterface = ref(null)

// 日志筛选表单
const logFilterForm = ref({
  status: ''
})

// 错误详情对话框
const errorDialogVisible = ref(false)
const errorDetails = ref('')



// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入接口名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择接口类型', trigger: 'change' }
  ],
  'config.host': [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  'config.user_key': [
    { required: true, message: '请输入用户密钥', trigger: 'blur' }
  ],
  'config.es_url': [
    { required: true, message: '请输入ES地址', trigger: 'blur' }
  ],
  collection_freq: [
    { required: true, message: '请选择采集频率', trigger: 'change' }
  ],
  collection_interval: [
    { required: true, message: '请输入采集间隔', trigger: 'blur' },
    { type: 'number', min: 1, max: 3600, message: '采集间隔必须在1-3600分钟之间', trigger: 'blur' }
  ],
  time_range_value: [
    { required: true, message: '请输入时间范围值', trigger: 'blur' },
    { type: 'number', min: 1, message: '时间范围值必须大于0', trigger: 'blur' }
  ]
}

// 计算属性
const isAdmin = computed(() => {
  const userRole = localStorage.getItem('userRole')
  return userRole === 'admin'
})

// 方法
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 格式化时间戳
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '-'
  return dayjs.unix(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

const getTypeLabel = (type) => {
  const labels = {
    'cccc_black_tech': 'CCCC黑科技',
    'es_alarm': 'ES告警数据',
    'rest': 'REST API',
    'graphql': 'GraphQL',
    'websocket': 'WebSocket',
    'webhook': 'Webhook'
  }
  return labels[type] || type
}

const getTypeTagType = (type) => {
  const types = {
    'cccc_black_tech': 'primary',
    'es_alarm': 'warning',
    'rest': 'success',
    'graphql': 'warning',
    'websocket': 'info',
    'webhook': 'info'
  }
  return types[type] || 'default'
}

const getFreqLabel = (freq) => {
  const labels = {
    'hourly': '每小时',
    'daily': '每天',
    'weekly': '每周',
    'custom': '自定义'
  }
  return labels[freq] || freq
}

const getRunStatusLabel = (status) => {
  const labels = {
    'success': '成功',
    'failed': '失败',
    'running': '运行中'
  }
  return labels[status] || status
}

const getRunStatusTagType = (status) => {
  const types = {
    'success': 'success',
    'failed': 'danger',
    'running': 'warning'
  }
  return types[status] || 'info'
}

const getMethodTagType = (method) => {
  const types = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger',
    'PATCH': 'info'
  }
  return types[method] || 'default'
}

const getStatusLabel = (status) => {
  const labels = {
    'enabled': '启用',
    'disabled': '禁用',
    'maintenance': '维护中'
  }
  return labels[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    'enabled': 'success',
    'disabled': 'danger',
    'maintenance': 'warning'
  }
  return types[status] || 'default'
}

// 日志状态相关方法
const getLogStatusLabel = (status) => {
  const labels = {
    'success': '成功',
    'failed': '失败',
    'running': '运行中'
  }
  return labels[status] || status
}

const getLogStatusTagType = (status) => {
  const types = {
    'success': 'success',
    'failed': 'danger',
    'running': 'warning'
  }
  return types[status] || 'default'
}

// 格式化执行时长（毫秒）
const formatDuration = (duration) => {
  if (!duration) return '-'

  if (duration < 1000) {
    return `${duration}ms`
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }
}

// 格式化执行时长（秒）
const formatDurationSeconds = (duration) => {
  if (!duration) return '-'

  if (duration < 60) {
    return `${duration}s`
  } else if (duration < 3600) {
    const minutes = Math.floor(duration / 60)
    const seconds = duration % 60
    return `${minutes}m ${seconds}s`
  } else {
    const hours = Math.floor(duration / 3600)
    const minutes = Math.floor((duration % 3600) / 60)
    const seconds = duration % 60
    return `${hours}h ${minutes}m ${seconds}s`
  }
}



// 加载数据接口列表
const loadDataInterfaces = async () => {
  loading.value = true
  try {
    const response = await api.getDataInterfaces({
      page: currentPage.value,
      page_size: pageSize.value,
      ...filterForm.value
    })

    dataInterfaces.value = response.data.data.list
    total.value = response.data.data.total
  } catch (error) {
    console.error('加载数据接口列表失败:', error)
    ElMessage.error('加载数据接口列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadDataInterfaces()
}

const resetFilter = () => {
  filterForm.value = {
    name: '',
    type: '',
    status: ''
  }
  handleSearch()
}

const refreshList = () => {
  loadDataInterfaces()
}

const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadDataInterfaces()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadDataInterfaces()
}

const handleCreate = () => {
  dialogTitle.value = '新建数据接口'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑数据接口'
  // 解析配置
  let config = {
    host: '',
    user_key: '',
    es_url: ''
  }
  if (row.config) {
    try {
      config = { ...config, ...JSON.parse(row.config) }
    } catch (e) {
      console.error('解析配置失败:', e)
    }
  }

  // 计算时间范围模式和分钟数
  const timeRangeType = row.time_range_type || 'relative' // 默认相对时间
  const timeRangeValue = row.time_range_value || 3600 // 默认1小时
  const timeRangeMinutes = Math.floor(timeRangeValue / 60)

  // 预设值对应的秒数：1小时=3600, 6小时=21600, 12小时=43200, 24小时=86400, 3天=259200, 7天=604800
  const presetValues = [3600, 21600, 43200, 86400, 259200, 604800]
  const isPresetValue = presetValues.includes(timeRangeValue)

  form.value = {
    ...row,
    config,
    time_range_type: timeRangeType, // 确保正确设置时间范围类型
    time_range_mode: isPresetValue ? 'preset' : 'custom',
    time_range_value: timeRangeValue, // 确保使用正确的秒数值
    time_range_minutes: timeRangeMinutes,
    time_range_absolute: row.start_time && row.end_time ? [row.start_time, row.end_time] : []
  }
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除接口 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await api.deleteDataInterface(row.id)

    ElMessage.success('删除成功')
    loadDataInterfaces()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleBatchDelete = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请选择要删除的接口')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${multipleSelection.value.length} 个接口吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用实际的批量删除API
    // const ids = multipleSelection.value.map(item => item.id)
    // await api.delete('/api/data-interfaces/batch', { data: { ids } })
    
    ElMessage.success('批量删除成功')
    loadDataInterfaces()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 切换接口状态
const handleToggleStatus = async (row) => {
  try {
    await api.toggleDataInterfaceStatus(row.id)
    ElMessage.success(`接口已${row.status === 'enabled' ? '禁用' : '启用'}`)
    loadDataInterfaces()
  } catch (error) {
    console.error('切换状态失败:', error)
    ElMessage.error('切换状态失败')
  }
}

// 执行接口
const handleRun = async (row) => {
  try {
    await api.runDataInterface(row.id)
    ElMessage.success('接口执行已启动')
    // 延迟刷新列表以显示最新状态
    setTimeout(() => {
      loadDataInterfaces()
    }, 1000)
  } catch (error) {
    console.error('执行接口失败:', error)
    ElMessage.error('执行接口失败')
  }
}

// 查看执行日志
const handleViewLogs = async (row) => {
  try {
    currentInterface.value = row
    logDialogVisible.value = true
    logCurrentPage.value = 1
    logFilterForm.value.status = ''
    await loadInterfaceLogs()
  } catch (error) {
    console.error('查看日志失败:', error)
    ElMessage.error('查看日志失败')
  }
}

// 加载接口日志
const loadInterfaceLogs = async () => {
  if (!currentInterface.value) return

  logLoading.value = true
  try {
    const params = {
      page: logCurrentPage.value,
      page_size: logPageSize.value
    }

    // 如果有状态筛选，添加到参数中
    if (logFilterForm.value.status) {
      params.status = logFilterForm.value.status
    }

    const response = await api.getDataInterfaceLogs(currentInterface.value.id, params)
    interfaceLogs.value = response.data.data.list || []
    logTotal.value = response.data.data.pagination?.total || 0
  } catch (error) {
    console.error('获取日志失败:', error)
    ElMessage.error('获取日志失败')
    interfaceLogs.value = []
    logTotal.value = 0
  } finally {
    logLoading.value = false
  }
}

// 日志分页处理
const handleLogSizeChange = (val) => {
  logPageSize.value = val
  logCurrentPage.value = 1
  loadInterfaceLogs()
}

const handleLogCurrentChange = (val) => {
  logCurrentPage.value = val
  loadInterfaceLogs()
}

// 重置日志对话框
const resetLogDialog = () => {
  interfaceLogs.value = []
  logTotal.value = 0
  logCurrentPage.value = 1
  logPageSize.value = 20
  logFilterForm.value.status = ''
  currentInterface.value = null
}

// 显示错误详情
const showErrorDetails = (log) => {
  errorDetails.value = log.error_details || '无错误详情'
  errorDialogVisible.value = true
}

// 处理接口类型变化
const handleTypeChange = (type) => {
  if (type === 'cccc_black_tech') {
    form.value.config = {
      host: '**********:5443',
      user_key: ''
    }
    form.value.collection_freq = 'hourly'
    form.value.time_range_type = 'relative'
    form.value.time_range_value = 3600
  } else if (type === 'es_alarm') {
    form.value.config = {
      es_url: 'http://127.0.0.1:9200'
    }
    form.value.collection_freq = 'hourly'
    form.value.time_range_type = 'relative'
    form.value.time_range_value = 3600
  }
}

const handleTest = (row) => {
  currentTestInterface.value = row
  testHeaders.value = '{"Content-Type": "application/json"}'
  testBody.value = ''
  testResult.value = ''
  testDialogVisible.value = true
}

const executeTest = async () => {
  if (!currentTestInterface.value) return
  
  testLoading.value = true
  try {
    let headers = {}
    try {
      headers = JSON.parse(testHeaders.value)
    } catch (e) {
      ElMessage.error('请求头格式错误，请输入有效的JSON')
      return
    }
    
    let body = null
    if (testBody.value.trim()) {
      try {
        body = JSON.parse(testBody.value)
      } catch (e) {
        ElMessage.error('请求体格式错误，请输入有效的JSON')
        return
      }
    }
    
    // 这里应该调用实际的测试API
    // const response = await api.request({
    //   method: currentTestInterface.value.method,
    //   url: currentTestInterface.value.url,
    //   headers,
    //   data: body
    // })
    
    // 模拟测试结果
    const mockResponse = {
      status: 200,
      statusText: 'OK',
      headers: {
        'content-type': 'application/json'
      },
      data: {
        success: true,
        message: '接口测试成功',
        timestamp: new Date().toISOString()
      }
    }
    
    testResult.value = JSON.stringify(mockResponse, null, 2)
    ElMessage.success('接口测试完成')
  } catch (error) {
    console.error('接口测试失败:', error)
    testResult.value = JSON.stringify({
      error: '接口测试失败',
      message: error.message || '未知错误'
    }, null, 2)
    ElMessage.error('接口测试失败')
  } finally {
    testLoading.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 准备提交数据
    const submitData = { ...form.value }

    // 处理时间范围
    if (submitData.time_range_type === 'absolute' && submitData.time_range_absolute.length === 2) {
      submitData.start_time = submitData.time_range_absolute[0]
      submitData.end_time = submitData.time_range_absolute[1]
    }
    delete submitData.time_range_absolute

    if (form.value.id) {
      await api.updateDataInterface(form.value.id, submitData)
    } else {
      await api.createDataInterface(submitData)
    }

    ElMessage.success(form.value.id ? '更新成功' : '创建成功')
    dialogVisible.value = false
    loadDataInterfaces()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 更新时间范围值（从分钟数）
const updateTimeRangeFromMinutes = (minutes) => {
  if (minutes && minutes > 0) {
    form.value.time_range_value = minutes * 60 // 转换为秒
  }
}

const resetForm = () => {
  form.value = {
    name: '',
    type: '',
    description: '',
    config: {
      host: '',
      user_key: ''
    },
    collection_enabled: false,
    collection_freq: 'hourly',
    collection_interval: 60,
    time_range_type: 'relative',
    time_range_mode: 'preset',
    time_range_value: 3600,
    time_range_minutes: 60,
    time_range_absolute: []
  }
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 生命周期
onMounted(() => {
  loadDataInterfaces()
})
</script>

<style scoped>
.data-interface-container {
  padding: 20px;
}

.filter-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-form {
  margin: 0;
}

.table-card {
  border-radius: 8px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.test-container {
  padding: 10px 0;
}

.test-result {
  margin-top: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 日志对话框样式 */
.log-dialog-content {
  max-height: 600px;
}

.log-filter-container {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 15px;
}

.log-filter-form {
  margin: 0;
}

.log-pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.text-gray-400 {
  color: #9ca3af;
}

.form-help-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.text-sm {
  font-size: 12px;
  line-height: 1.4;
}

/* 操作按钮间距 */
.el-table .el-button + .el-button {
  margin-left: 6px;
}


</style>
