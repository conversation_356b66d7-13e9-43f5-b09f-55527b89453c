package handlers

import (
	"net"
	"strconv"
	"strings"
)

// isPrivateIP 判断是否为私有IP地址
func (h *DataInterfaceHandler) isPrivateIP(ipStr string) bool {
	if ipStr == "" {
		return false
	}

	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}

	// 检查IPv4私有地址范围
	if ipv4 := ip.To4(); ipv4 != nil {
		// 10.0.0.0/8
		if ipv4[0] == 10 {
			return true
		}
		// **********/12
		if ipv4[0] == 172 && ipv4[1] >= 16 && ipv4[1] <= 31 {
			return true
		}
		// ***********/16
		if ipv4[0] == 192 && ipv4[1] == 168 {
			return true
		}
		// *********/8 (loopback)
		if ipv4[0] == 127 {
			return true
		}
	}

	return false
}

// isInternalTraffic 判断是否为内网流量
func (h *DataInterfaceHandler) isInternalTraffic(record SearchDataRecord) bool {
	return h.isPrivateIP(record.SrcIP) && h.isPrivateIP(record.DstIP)
}

// getSourceLabel 根据IP地址获取来源标签
func (h *DataInterfaceHandler) getSourceLabel(ip string) string {
	if ip == "" {
		return "未知"
	}

	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return "未知"
	}

	if ipv4 := parsedIP.To4(); ipv4 != nil {
		// 检查10段 - 交通运输部
		if ipv4[0] == 10 {
			return "交通运输部"
		}

		// 检查192段 - 通信中心
		if ipv4[0] == 192 && ipv4[1] == 168 {
			return "通信中心"
		}

		// 检查172段
		if ipv4[0] == 172 && ipv4[1] >= 16 && ipv4[1] <= 31 {
			return "内网设备"
		}
	}

	// 外网地址
	if !h.isPrivateIP(ip) {
		return "外网"
	}

	return "内网"
}

// shouldIncludeAttackFlow 判断是否应该包含此攻击流
func (h *DataInterfaceHandler) shouldIncludeAttackFlow(srcIP, dstIP string) bool {
	srcLabel := h.getSourceLabel(srcIP)
	dstLabel := h.getSourceLabel(dstIP)

	// 只包含涉及目标组织（通信中心或交通运输部）的攻击流
	targetLabels := []string{"通信中心", "交通运输部"}
	
	for _, target := range targetLabels {
		if srcLabel == target || dstLabel == target {
			return true
		}
	}

	return false
}

// containsString 检查字符串切片是否包含指定字符串
func (h *DataInterfaceHandler) containsString(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// updateCategoryCount 更新攻击类别计数
func (h *DataInterfaceHandler) updateCategoryCount(categories map[string]int, category string) {
	if category == "" {
		return
	}
	
	// 映射攻击类别代码到中文名称
	categoryMap := map[string]string{
		"20404": "漏洞利用攻击",
		"20402": "Web攻击",
		"20403": "恶意软件",
		"20401": "网络扫描",
		"20405": "异常流量",
		"20406": "信息收集",
	}
	
	if mappedCategory, exists := categoryMap[category]; exists {
		categories[mappedCategory]++
	} else {
		categories[category]++
	}
}

// updateSeverityCount 更新严重程度计数
func (h *DataInterfaceHandler) updateSeverityCount(severities map[string]int, severity string) {
	if severity == "" {
		return
	}
	
	// 映射严重程度代码到中文名称
	severityMap := map[string]string{
		"1": "低危",
		"2": "中危", 
		"3": "高危",
		"4": "严重",
	}
	
	if mappedSeverity, exists := severityMap[severity]; exists {
		severities[mappedSeverity]++
	} else {
		severities[severity]++
	}
}

// updateIOCCount 更新IOC计数
func (h *DataInterfaceHandler) updateIOCCount(iocs map[string]int, ioc string) {
	if ioc == "" {
		return
	}
	
	// 清理IOC字符串
	cleanIOC := strings.TrimSpace(ioc)
	if cleanIOC != "" {
		iocs[cleanIOC]++
	}
}

// calculateThreatScore 计算威胁评分
func (h *DataInterfaceHandler) calculateThreatScore(severities map[string]int, attackCount int) float64 {
	// 基础分数：根据最高严重程度计算
	var baseScore float64 = 0
	for severity := range severities {
		var score float64
		switch severity {
		case "高危":
			score = 8.0
		case "中危":
			score = 5.0
		case "低危":
			score = 2.0
		case "信息":
			score = 1.0
		default:
			score = 3.0
		}
		if score > baseScore {
			baseScore = score
		}
	}

	// 频次系数
	var frequencyMultiplier float64 = 1.0
	if attackCount >= 100 {
		frequencyMultiplier = 1.8
	} else if attackCount >= 50 {
		frequencyMultiplier = 1.6
	} else if attackCount >= 20 {
		frequencyMultiplier = 1.4
	} else if attackCount >= 10 {
		frequencyMultiplier = 1.2
	} else if attackCount >= 5 {
		frequencyMultiplier = 1.1
	}

	// 计算最终评分
	finalScore := baseScore * frequencyMultiplier

	// 限制评分范围在1-10之间
	if finalScore > 10.0 {
		finalScore = 10.0
	} else if finalScore < 1.0 {
		finalScore = 1.0
	}

	return finalScore
}

// getCategoryPriority 获取攻击类别的优先级（数值越大优先级越高）
func (h *DataInterfaceHandler) getCategoryPriority(category string) int {
	priorities := map[string]int{
		"漏洞利用攻击": 10,
		"Web攻击":    8,
		"恶意软件":     7,
		"网络扫描":     5,
		"异常流量":     3,
		"信息收集":     2,
	}

	if priority, exists := priorities[category]; exists {
		return priority
	}
	return 1 // 默认优先级
}

// formatTimestamp 将时间戳格式化为字符串
func (h *DataInterfaceHandler) formatTimestamp(timestamp int64) string {
	if timestamp == 0 {
		return ""
	}
	return strconv.FormatInt(timestamp, 10)
}

// parseTimestamp 将字符串解析为时间戳
func (h *DataInterfaceHandler) parseTimestamp(timestampStr string) int64 {
	if timestampStr == "" {
		return 0
	}
	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return 0
	}
	return timestamp
}
