# 威胁情报管理平台 V2

## 项目简介

威胁情报管理平台V2是一个现代化的安全情报管理系统，集成了漏洞情报采集、IOC情报管理、智能推送等功能。系统采用前后端分离架构，支持多种数据源和推送渠道，为安全团队提供全面的威胁情报管理解决方案。

## 核心功能模块

### 🔍 漏洞情报管理
- **多源采集**: 支持8种主流漏洞情报源自动采集
- **智能分析**: 漏洞严重程度评估和分类标签
- **灵活搜索**: 支持关键词、严重程度、来源等多维度筛选
- **批量操作**: 支持批量推送、删除、导出等操作
- **手动管理**: 支持手动添加、编辑漏洞信息

### 🛡️ IOC情报系统
- **数据采集模块**: 支持多种数据接口，包括CCCC黑科技等
- **情报生产模块**: 基于生产策略自动生成IOC情报
- **情报推送模块**: 支持单个和批量推送IOC情报
- **白名单管理**: 灵活的IOC白名单过滤机制
- **天际友盟集成**: 集成天际友盟威胁情报查询

### 📡 智能推送系统
- **多渠道支持**: 企业微信、钉钉、飞书、自定义Webhook
- **策略配置**: 灵活的推送策略和模板配置
- **白名单匹配**: 基于关键词的自动推送触发
- **推送记录**: 完整的推送历史和状态跟踪
- **RSS订阅**: 提供标准RSS订阅接口

### ⚙️ 系统管理
- **用户管理**: 支持多用户和权限控制
- **采集器管理**: 可配置的定时采集任务
- **日志系统**: 统一的日志管理和监控
- **配置管理**: 灵活的系统配置和参数调整

## 技术架构

### 后端技术栈
- **语言**: Go 1.23+
- **框架**: Gin Web框架
- **数据库**: MySQL 5.7+ / SQLite 3
- **ORM**: GORM v2
- **认证**: JWT Token
- **日志**: 结构化日志系统

### 前端技术栈
- **框架**: Vue 3 + TypeScript
- **UI组件**: Element Plus
- **构建工具**: Vite
- **图表**: ECharts
- **路由**: Vue Router 4

### 系统要求
- Go 1.23+
- MySQL 5.7+ 或 SQLite 3
- Node.js 16+ (前端开发)
- 内存: 建议2GB+
- 存储: 建议10GB+

## 快速开始

### 🚀 一键启动（推荐）

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd Vulnerability_push_V2
   ```

2. **运行启动脚本**
   - **Linux/macOS**: `./start.sh`
   - **Windows**: 双击 `start.bat`

3. **访问系统**
   - 后端API: http://localhost:8080
   - 前端界面: http://localhost:3000 (开发模式)
   - 默认管理员: `admin` (密码在首次启动时显示)

### 🔧 手动部署

#### 后端部署

```bash
# 1. 进入后端目录
cd backend

# 2. 安装依赖
go mod tidy

# 3. 构建应用
go build -o vulnerability_push_server .

# 4. 启动服务
./vulnerability_push_server
```

#### 前端部署

**开发模式**
```bash
# 1. 进入前端目录
cd frontend

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev
```

**生产部署**
```bash
# 1. 构建生产版本
npm run build

# 2. 部署dist目录到Web服务器
# 或使用内置服务器（后端会自动服务前端文件）
```

## 📋 系统配置

### 配置文件说明

系统配置文件为 `backend/config.yaml`，首次启动会自动创建默认配置。

```yaml
# 服务器配置
server:
  host: 0.0.0.0          # 监听地址
  port: 8080             # 监听端口
  mode: release          # 运行模式: debug/release/test
  readTimeout: 30        # 读取超时(秒)
  writeTimeout: 30       # 写入超时(秒)
  maxBodySize: 33554432  # 最大请求体大小(32MB)

# 数据库配置
database:
  type: mysql            # 数据库类型: mysql/sqlite
  mysql:
    host: localhost      # MySQL服务器地址
    port: 3306          # MySQL端口
    user: root          # 数据库用户名
    password: ""        # 数据库密码
    database: vuln_push_v2  # 数据库名称
    charset: utf8mb4    # 字符集
    loc: Local          # 时区
  migration:
    autoBackupTables: true    # 自动备份表
    dropLegacyTables: false   # 删除旧表
    detailedLogs: true        # 详细日志
    migrationTimeout: 300     # 迁移超时(秒)

# 日志配置
log:
  level: info            # 日志级别: debug/info/warn/error/fatal
  file: ./logs/app.log   # 日志文件路径
  console: true          # 控制台输出
  showFile: true         # 显示文件名和行号

# 安全配置
security:
  jwtSecret: ""          # JWT密钥(自动生成)
  jwtExpireHour: 24      # JWT过期时间(小时)
  apiKeyLength: 32       # API密钥长度
  passwordSalt: ""       # 密码盐值(自动生成)
```

### 环境变量支持

系统支持通过环境变量覆盖配置文件设置：

```bash
# 数据库配置
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=root
export DB_PASSWORD=your_password
export DB_NAME=vuln_push_v2

# 服务器配置
export SERVER_HOST=0.0.0.0
export SERVER_PORT=8080
export SERVER_MODE=release
```

### 首次登录

- **默认管理员账户**: `admin`
- **初始密码**: 首次启动时在控制台显示
- **密码重置**: 管理员可在用户管理中重置密码

## 🔄 漏洞采集系统

### 支持的数据源

系统内置8种主流漏洞情报源，启动时自动初始化：

| 数据源 | 描述 | 类型 |
|--------|------|------|
| 长亭漏洞库 | 长亭科技漏洞情报 | 公开源 |
| 奇安信威胁情报中心 | 奇安信威胁情报 | 公开源 |
| 阿里云漏洞库 | 阿里云安全漏洞库 | 公开源 |
| 微步威胁情报 | 微步在线威胁情报 | 公开源 |
| 知道创宇Seebug | Seebug漏洞平台 | 公开源 |
| 启明星辰漏洞通告 | 启明星辰安全通告 | 公开源 |
| OSCS开源安全 | 开源软件安全情报 | 公开源 |
| NVD国家漏洞数据库 | 美国国家漏洞数据库 | 官方源 |

### 采集策略配置

**采集周期选项**:
- 🔧 **手动采集**: 仅通过"立即采集"按钮触发
- ⏰ **定时采集**: 每小时/每天/每周/每月
- 🎯 **自定义间隔**: 最小5分钟间隔的自定义周期

**采集流程**:
1. **数据获取** → 从配置的情报源获取最新数据
2. **数据解析** → 标准化处理和格式转换
3. **去重检查** → 避免重复数据入库
4. **数据存储** → 存储到数据库并建立索引
5. **自动推送** → 根据白名单策略自动推送

### 采集器管理

- **状态监控**: 实时查看采集器运行状态
- **日志查看**: 详细的采集日志和错误信息
- **手动控制**: 支持启动、停止、重启采集任务
- **配置调整**: 灵活调整采集参数和周期

### 扩展开发

系统支持自定义采集器开发，实现 `Grabber` 接口：

```go
type Grabber interface {
    // 获取采集源信息
    ProviderInfo() *Provider
    // 采集漏洞信息
    GetUpdate(ctx context.Context, pageLimit int, startDate, endDate string) ([]VulnInfo, error)
    // 带去重检查的采集
    GetUpdateWithCheck(ctx context.Context, pageLimit int, startDate, endDate string, checkExists CheckVulnExistsFunc) ([]VulnInfo, error)
}
```

## 📡 IOC情报系统

### 三大核心模块

#### 1. 数据采集模块
- **数据接口管理**: 支持多种外部数据接口
- **CCCC黑科技集成**: 内置CCCC黑科技数据接口
- **定时采集**: 可配置的自动数据采集
- **数据预处理**: 原始数据清洗和标准化

#### 2. 情报生产模块
- **生产策略**: 基于规则的IOC情报生成
- **威胁评分**: 智能威胁等级评估
- **地理位置**: 集成GeoIP地理位置信息
- **天际友盟**: 集成天际友盟威胁情报查询
- **白名单过滤**: 灵活的IOC白名单管理

#### 3. 情报推送模块
- **单个推送**: 支持单条IOC情报推送
- **批量推送**: 高效的批量推送功能
- **推送记录**: 完整的推送历史跟踪
- **推送统计**: 详细的推送效果分析

### IOC数据流程

```
外部数据源 → 数据接口 → 源数据存储 → 生产策略处理 → IOC情报生成 → 推送分发
```

## 📤 智能推送系统

### 支持的推送渠道

| 渠道类型 | 配置要求 | 功能特性 |
|----------|----------|----------|
| 企业微信机器人 | Webhook URL + Token | 支持富文本、@提醒 |
| 钉钉机器人 | Access Token | 支持Markdown格式 |
| 飞书机器人 | Access Token | 支持卡片消息 |
| 自定义Webhook | URL + 请求配置 | 灵活的HTTP请求 |

### 推送策略配置

- **白名单匹配**: 基于关键词的自动推送触发
- **严重程度过滤**: 按漏洞严重程度推送
- **推送模板**: 可自定义的消息模板
- **推送频率**: 防止推送频率过高的限制机制
- **推送记录**: 完整的推送历史和状态跟踪

### RSS订阅服务

提供标准RSS 2.0格式的订阅服务：

```
GET /api/rss
```

**支持功能**:
- 按严重程度过滤
- 按标签排除
- 限制返回条目数量
- 可选的认证保护

## 📊 日志与监控

### 统一日志系统

- **分级日志**: DEBUG、INFO、WARN、ERROR、FATAL
- **多输出**: 同时输出到文件和控制台
- **自动分割**: 按日期自动分割日志文件
- **结构化**: 包含时间戳、级别、文件位置等信息
- **请求追踪**: 支持请求ID追踪完整调用链

## 🔐 安全特性

### 用户认证与授权
- **JWT认证**: 基于JWT Token的无状态认证
- **角色权限**: 管理员和普通用户权限分离
- **API密钥**: 支持API密钥认证方式
- **密码安全**: 密码加盐哈希存储

### 数据安全
- **敏感词过滤**: Base64编码存储敏感词列表
- **SQL注入防护**: 使用ORM防止SQL注入
- **XSS防护**: 前端输入验证和转义
- **HTTPS支持**: 支持SSL/TLS加密传输

### 系统安全
- **请求限制**: 防止恶意请求和DDoS攻击
- **日志审计**: 完整的操作日志记录
- **配置保护**: 敏感配置信息加密存储

## 📈 性能优化

### 数据库优化
- **索引优化**: 关键字段建立索引
- **查询优化**: 分页查询和条件过滤
- **连接池**: 数据库连接池管理
- **事务控制**: 合理的事务边界控制

### 缓存策略
- **内存缓存**: 热点数据内存缓存
- **查询缓存**: 频繁查询结果缓存
- **静态资源**: 前端资源缓存优化

### 并发处理
- **协程池**: Go协程池管理
- **异步处理**: 耗时操作异步执行
- **批量操作**: 支持批量数据处理

## 🔄 系统更新日志

### V2.0.0 (2025年1月) - 架构重构版本

**🏗️ 架构升级**:
- 采用前后端分离架构设计
- 重构为模块化的分层架构
- 引入依赖注入和接口设计模式
- 统一的错误处理和响应格式

**🆕 新增功能**:
- IOC情报管理系统完整实现
- 天际友盟威胁情报集成
- 生产策略配置和自动化处理
- 批量操作和导出功能增强
- 用户权限管理系统

**🔧 系统优化**:
- 数据库结构优化和迁移系统
- 统一的日志系统和监控
- 性能优化和并发处理改进
- 前端界面重新设计和优化

**🐛 问题修复**:
- 修复采集器周期设置问题
- 优化推送系统稳定性
- 解决内存泄漏和性能问题
- 修复多处UI和交互问题

## 🚀 部署指南

### Docker部署（推荐）

```bash
# 1. 构建镜像
docker build -t vuln-push-v2 .

# 2. 运行容器
docker run -d \
  --name vuln-push-v2 \
  -p 8080:8080 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  vuln-push-v2
```

### 生产环境部署

```bash
# 1. 编译生产版本
cd backend
go build -ldflags="-s -w" -o vulnerability_push_server .

# 2. 构建前端
cd ../frontend
npm run build

# 3. 配置反向代理（Nginx示例）
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 系统服务配置

```ini
# /etc/systemd/system/vuln-push.service
[Unit]
Description=Vulnerability Push V2 Service
After=network.target

[Service]
Type=simple
User=vuln-push
WorkingDirectory=/opt/vuln-push-v2
ExecStart=/opt/vuln-push-v2/vulnerability_push_server
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

## 📖 使用指南

### 系统初始化

1. **首次启动**
   - 系统会自动创建数据库表结构
   - 生成默认管理员账户和随机密码
   - 初始化8个漏洞采集器配置
   - 创建默认推送策略和通道

2. **基础配置**
   - 修改管理员密码
   - 配置数据库连接信息
   - 设置推送通道参数
   - 配置采集器定时任务

### 漏洞管理操作

**查看漏洞列表**:
- 支持关键词、严重程度、来源等多维度筛选
- 可按时间、严重程度等字段排序
- 支持分页浏览和批量操作

**漏洞详情查看**:
- 完整的漏洞信息展示
- 相关链接和修复建议
- 推送历史和状态跟踪

**批量操作**:
- 批量推送选中漏洞
- 批量删除过期漏洞
- 批量导出漏洞数据

### IOC情报管理

**数据接口配置**:
```bash
# 配置CCCC黑科技接口
{
  "host": "your-host.com",
  "user_key": "your-api-key",
  "time_range": 3600
}
```

**生产策略设置**:
- 攻击次数阈值: 默认3次
- 威胁评分阈值: 默认2分
- 风险等级过滤: 严重、高危、中危
- 地理位置过滤: 支持国家和IP段过滤

**推送管理**:
- 单个IOC推送到指定通道
- 批量IOC推送到多个通道
- 推送记录查看和统计分析

### 敏感词管理

系统使用Base64编码存储敏感词，保护敏感信息：

```bash
# 编码敏感词文件
cd backend
go run . --encode-sensitive-words sensitive_words.txt

# 添加新的敏感词
go run . --encode-sensitive-words sensitive_words.txt "新敏感词1" "新敏感词2"
```

**敏感词文件格式**:
```
# 注释行以#开头
dGVzdA==  # Base64编码的敏感词
YWRtaW4=  # 另一个编码的敏感词
```

### API接口使用

**认证方式**:
```bash
# JWT Token认证
curl -H "Authorization: Bearer <token>" http://localhost:8080/api/vulnerabilities

# API Key认证
curl -H "X-API-Key: <api-key>" http://localhost:8080/api/vulnerabilities
```

**RSS订阅**:
```bash
# 获取RSS订阅
curl http://localhost:8080/api/rss

# 带参数的RSS订阅
curl "http://localhost:8080/api/rss?severity=严重&limit=50"
```

### 系统监控

**日志查看**:
```bash
# 查看应用日志
tail -f backend/logs/app.log

# 查看采集器日志
tail -f backend/logs/crawler.log

# 查看推送日志
tail -f backend/logs/push.log
```

**性能监控**:
- 数据库连接池状态
- 内存使用情况
- 采集器运行状态
- 推送成功率统计

## 🤝 贡献指南

### 开发环境搭建

```bash
# 1. Fork项目到个人仓库
# 2. 克隆到本地
git clone https://github.com/your-username/Vulnerability_push_V2.git

# 3. 创建开发分支
git checkout -b feature/your-feature

# 4. 安装依赖
cd backend && go mod tidy
cd ../frontend && npm install

# 5. 启动开发环境
# 后端
cd backend && go run .
# 前端
cd frontend && npm run dev
```

### 代码规范

- **Go代码**: 遵循Go官方代码规范，使用gofmt格式化
- **Vue代码**: 遵循Vue 3官方风格指南
- **提交信息**: 使用约定式提交格式
- **测试覆盖**: 新功能需要包含单元测试

### 提交流程

1. 确保代码通过所有测试
2. 更新相关文档
3. 提交Pull Request
4. 等待代码审查
5. 合并到主分支

## 📄 许可证

本项目采用 **MIT许可证**，详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

感谢所有为本项目做出贡献的开发者和用户。

## 📞 支持与反馈

- **问题反馈**: 请在GitHub Issues中提交
- **功能建议**: 欢迎在Issues中讨论新功能
- **安全问题**: 请通过私有渠道报告安全漏洞

---

**威胁情报管理平台V2** - 让威胁情报管理更简单、更高效！