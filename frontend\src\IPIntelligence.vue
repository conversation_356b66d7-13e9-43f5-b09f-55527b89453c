<template>
  <div class="ip-intelligence-container">
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="IP地址">
          <el-input
            v-model="filterForm.ip"
            placeholder="输入IP地址"
            clearable
            prefix-icon="Search"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="风险等级">
          <el-select 
            v-model="filterForm.riskLevel" 
            placeholder="全部" 
            clearable
            style="width: 180px;"
          >
            <el-option label="严重" value="严重" />
            <el-option label="高危" value="高危" />
            <el-option label="中危" value="中危" />
            <el-option label="低危" value="低危" />
            <el-option label="信息" value="信息" />
          </el-select>
        </el-form-item>
        <el-form-item label="情报类型">
          <el-select
            v-model="filterForm.type"
            placeholder="选择类型"
            clearable
            filterable
          >
            <el-option label="僵尸网络" value="僵尸网络" />
            <el-option label="恶意扫描" value="恶意扫描" />
            <el-option label="网络攻击" value="网络攻击" />
            <el-option label="勒索软件" value="勒索软件" />
            <el-option label="钓鱼网站" value="钓鱼网站" />
            <el-option label="挖矿木马" value="挖矿木马" />
            <el-option label="代理服务" value="代理服务" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="命中次数">
          <el-select
            v-model="filterForm.hitCountRange"
            placeholder="选择范围"
            clearable
          >
            <el-option label="低(< 50)" value="low" />
            <el-option label="中(50-100)" value="medium" />
            <el-option label="高(100-300)" value="high" />
            <el-option label="极高(> 300)" value="veryhigh" />
          </el-select>
        </el-form-item>
        <el-form-item label="被攻击单位">
          <el-input
            v-model="filterForm.targetOrg"
            placeholder="输入单位名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="发现日期">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon> 搜索
          </el-button>
          <el-button @click="resetFilter">
            <el-icon><RefreshLeft /></el-icon> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="hover" class="table-card">
      <div class="table-header">
        <div class="table-title">IP情报列表</div>
        <div class="table-actions">
          <el-button type="primary" @click="refreshList">
            <el-icon><Refresh /></el-icon> 刷新
          </el-button>
          <el-button type="success" v-if="isAdmin" @click="handleCreate">
            <el-icon><Plus /></el-icon> 添加情报
          </el-button>
          <el-button 
            type="danger" 
            v-if="isAdmin" 
            @click="handleBatchDelete"
            :disabled="multipleSelection.length === 0"
          >
            <el-icon><Delete /></el-icon> 批量删除
          </el-button>
          <el-button 
            type="primary" 
            @click="handleExport"
            :disabled="multipleSelection.length === 0"
          >
            <el-icon><Download /></el-icon> 导出Excel
          </el-button>
        </div>
      </div>

      <el-table
        :data="ipIntelligence"
        border
        style="width: 100%"
        v-loading="loading"
        row-key="id"
        stripe
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          textAlign: 'center',
          fontWeight: '500',
          writingMode: 'horizontal-tb',
          whiteSpace: 'nowrap'
        }"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="ip" label="IP地址" width="150" show-overflow-tooltip sortable />
        <el-table-column prop="location" label="地理位置" width="150" show-overflow-tooltip />
        <el-table-column prop="type" label="情报类型" width="120">
          <template #default="scope">
            <el-tag size="small" effect="plain">{{ scope.row.type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="riskLevel" label="风险等级" width="100">
          <template #default="scope">
            <el-tag
              :type="getRiskLevelType(scope.row.riskLevel)"
              effect="dark"
            >
              {{ scope.row.riskLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="hitCount" label="命中次数" width="120" sortable>
          <template #default="scope">
            <el-tag
              :type="getHitCountType(scope.row.hitCount)"
              effect="plain"
            >
              {{ scope.row.hitCount }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="targetOrg" label="被攻击单位" width="180" show-overflow-tooltip />
        <el-table-column prop="description" label="威胁描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="source" label="情报来源" width="120" show-overflow-tooltip />
        <el-table-column prop="discoveryDate" label="发现日期" width="120" sortable="custom" />
        <el-table-column label="操作" width="260" fixed="right">
          <template #default="scope">
            <div class="operation-buttons">
              <el-tooltip content="查看详情" placement="top">
                <el-button size="small" type="primary" circle @click="handleDetail(scope.row)">
                  <el-icon><InfoFilled /></el-icon>
                </el-button>
              </el-tooltip>

              <el-tooltip content="推送" placement="top">
                <el-button size="small" type="success" circle @click="handlePush(scope.row)">
                  <el-icon><Promotion /></el-icon>
                </el-button>
              </el-tooltip>

              <el-tooltip v-if="isAdmin" content="编辑" placement="top">
                <el-button size="small" type="warning" circle @click="handleEdit(scope.row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>

              <el-tooltip v-if="isAdmin" content="删除" placement="top">
                <el-button size="small" type="danger" circle @click="handleDelete(scope.row)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="入库时间" width="180" sortable>
          <template #default="scope">
            {{ formatDate(scope.row.createdAt, 'YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>

    <!-- IP情报添加/编辑对话框 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="isEditing ? '编辑IP情报' : '添加IP情报'"
      width="800px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="ipForm"
        :rules="formRules"
        label-width="120px"
        label-position="left"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="IP地址" prop="ip">
              <el-input
                v-model="ipForm.ip"
                placeholder="请输入IP地址"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地理位置" prop="location">
              <el-input
                v-model="ipForm.location"
                placeholder="请输入地理位置"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="情报类型" prop="type">
              <el-select
                v-model="ipForm.type"
                placeholder="请选择情报类型"
                style="width: 100%"
              >
                <el-option label="僵尸网络" value="僵尸网络" />
                <el-option label="恶意扫描" value="恶意扫描" />
                <el-option label="网络攻击" value="网络攻击" />
                <el-option label="勒索软件" value="勒索软件" />
                <el-option label="钓鱼网站" value="钓鱼网站" />
                <el-option label="挖矿木马" value="挖矿木马" />
                <el-option label="代理服务" value="代理服务" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="风险等级" prop="riskLevel">
              <el-select
                v-model="ipForm.riskLevel"
                placeholder="请选择风险等级"
                style="width: 100%"
              >
                <el-option label="严重" value="严重" />
                <el-option label="高危" value="高危" />
                <el-option label="中危" value="中危" />
                <el-option label="低危" value="低危" />
                <el-option label="信息" value="信息" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="命中次数" prop="hitCount">
              <el-input-number
                v-model="ipForm.hitCount"
                :min="0"
                :max="999999"
                style="width: 100%"
                placeholder="请输入命中次数"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发现日期" prop="discoveryDate">
              <el-date-picker
                v-model="ipForm.discoveryDate"
                type="date"
                placeholder="请选择发现日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="被攻击单位" prop="targetOrg">
          <el-input
            v-model="ipForm.targetOrg"
            placeholder="请输入被攻击单位"
            clearable
          />
        </el-form-item>

        <el-form-item label="情报来源" prop="source">
          <el-input
            v-model="ipForm.source"
            placeholder="请输入情报来源"
            clearable
          />
        </el-form-item>

        <el-form-item label="威胁描述" prop="description">
          <el-input
            v-model="ipForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入威胁描述"
          />
        </el-form-item>

        <el-form-item label="标签" prop="tags">
          <el-input
            v-model="ipForm.tags"
            placeholder="请输入标签，多个标签用逗号分隔"
            clearable
          />
        </el-form-item>

        <el-form-item label="推送原因" prop="pushReason">
          <el-input
            v-model="ipForm.pushReason"
            type="textarea"
            :rows="3"
            placeholder="请输入推送原因"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="formDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEditing ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- IP情报推送对话框 -->
    <el-dialog
      v-model="pushDialogVisible"
      title="推送IP情报"
      width="500px"
      destroy-on-close
    >
      <div v-if="currentPushIP" class="push-dialog-content">
        <div class="ip-info">
          <h4>IP情报信息</h4>
          <p><strong>IP地址:</strong> {{ currentPushIP.ip }}</p>
          <p><strong>风险等级:</strong>
            <el-tag :type="getRiskLevelType(currentPushIP.riskLevel)" effect="dark">
              {{ currentPushIP.riskLevel }}
            </el-tag>
          </p>
          <p><strong>情报类型:</strong> {{ currentPushIP.type }}</p>
        </div>

        <el-divider />

        <div class="push-options">
          <h4>推送选项</h4>

          <el-form label-width="100px">
            <el-form-item label="推送通道">
              <el-select
                v-model="selectedChannelId"
                placeholder="选择推送通道（可选）"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="channel in pushChannels"
                  :key="channel.id"
                  :label="`${channel.name} (${channel.type})`"
                  :value="channel.id"
                  :disabled="!channel.status"
                />
              </el-select>
              <div class="form-tip">不选择通道将使用默认策略推送</div>
            </el-form-item>

            <el-form-item label="推送策略">
              <el-select
                v-model="selectedPolicyId"
                placeholder="选择推送策略（可选）"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="policy in pushPolicies"
                  :key="policy.id"
                  :label="policy.name"
                  :value="policy.id"
                />
              </el-select>
              <div class="form-tip">不选择策略将使用默认策略推送</div>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="pushDialogVisible = false">取消</el-button>
          <el-button
            type="success"
            @click="pushWithDefaultPolicy"
            :loading="pushing"
          >
            使用默认策略推送
          </el-button>
          <el-button
            type="primary"
            @click="executePush"
            :loading="pushing"
            :disabled="!selectedChannelId && !selectedPolicyId"
          >
            推送
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- IP情报详情抽屉 -->
    <el-drawer
      v-model="detailDrawerVisible"
      title="IP情报详情"
      size="60%"
      direction="rtl"
      destroy-on-close
    >
      <div v-if="currentIPIntelligence" class="ip-intelligence-detail">
        <div class="custom-descriptions">
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">IP地址</div>
            <div class="custom-descriptions-content">{{ currentIPIntelligence.ip }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">地理位置</div>
            <div class="custom-descriptions-content">{{ currentIPIntelligence.location }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">风险等级</div>
            <div class="custom-descriptions-content">
              <el-tag :type="getRiskLevelType(currentIPIntelligence.riskLevel)" effect="dark">
                {{ currentIPIntelligence.riskLevel }}
              </el-tag>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">情报类型</div>
            <div class="custom-descriptions-content">
              <el-tag size="small" effect="plain">{{ currentIPIntelligence.type }}</el-tag>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">命中次数</div>
            <div class="custom-descriptions-content">
              <el-tag :type="getHitCountType(currentIPIntelligence.hitCount)" effect="plain">
                {{ currentIPIntelligence.hitCount }}
              </el-tag>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">被攻击单位</div>
            <div class="custom-descriptions-content">{{ currentIPIntelligence.targetOrg || '未知' }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">威胁描述</div>
            <div class="custom-descriptions-content">{{ currentIPIntelligence.description }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">情报来源</div>
            <div class="custom-descriptions-content">{{ currentIPIntelligence.source }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">发现日期</div>
            <div class="custom-descriptions-content">{{ currentIPIntelligence.discoveryDate }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">入库时间</div>
            <div class="custom-descriptions-content">
              {{ formatDate(currentIPIntelligence.createdAt, 'YYYY-MM-DD HH:mm:ss') }}
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">更新时间</div>
            <div class="custom-descriptions-content">
              {{ formatDate(currentIPIntelligence.updatedAt, 'YYYY-MM-DD HH:mm:ss') }}
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, RefreshLeft, Refresh, Plus, Delete, Download, InfoFilled, Edit, Promotion } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import api from './api'

// IP情报数据
const ipIntelligence = ref([])

const router = useRouter()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)
const multipleSelection = ref([])
const detailDrawerVisible = ref(false)
const currentIPIntelligence = ref(null)
const dateRange = ref(null)

// 表单相关
const formDialogVisible = ref(false)
const isEditing = ref(false)
const submitting = ref(false)
const formRef = ref(null)

// 推送相关
const pushDialogVisible = ref(false)
const currentPushIP = ref(null)
const pushChannels = ref([])
const pushPolicies = ref([])
const selectedChannelId = ref('')
const selectedPolicyId = ref('')
const pushing = ref(false)

// IP情报表单数据
const ipForm = ref({
  ip: '',
  location: '',
  type: '',
  riskLevel: '',
  hitCount: 1,
  targetOrg: '',
  description: '',
  source: '',
  discoveryDate: '',
  tags: '',
  pushReason: ''
})

// 表单验证规则
const formRules = {
  ip: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    {
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/,
      message: '请输入有效的IP地址',
      trigger: 'blur'
    }
  ],
  type: [
    { required: true, message: '请选择情报类型', trigger: 'change' }
  ],
  riskLevel: [
    { required: true, message: '请选择风险等级', trigger: 'change' }
  ],
  source: [
    { required: true, message: '请输入情报来源', trigger: 'blur' }
  ],
  hitCount: [
    { required: true, message: '请输入命中次数', trigger: 'blur' },
    { type: 'number', min: 0, message: '命中次数不能小于0', trigger: 'blur' }
  ]
}

const filterForm = ref({
  ip: '',
  riskLevel: '',
  type: '',
  hitCountRange: '',
  targetOrg: '',
  startDate: '',
  endDate: '',
  keyword: ''
})

const isAdmin = computed(() => {
  // 从localStorage获取用户信息或其他方式判断是否为管理员
  return true // 示例，实际应根据用户权限判断
})

// 处理日期范围变化
const handleDateRangeChange = (val) => {
  if (val) {
    filterForm.value.startDate = val[0]
    filterForm.value.endDate = val[1]
  } else {
    filterForm.value.startDate = ''
    filterForm.value.endDate = ''
  }
}

// 根据风险等级获取对应的标签类型
const getRiskLevelType = (level) => {
  const map = {
    '严重': 'danger',
    '高危': 'warning',
    '中危': 'warning',
    '低危': 'success',
    '信息': 'info'
  }
  return map[level] || 'info'
}

// 根据命中次数获取对应的标签类型
const getHitCountType = (count) => {
  if (count >= 300) return 'danger'
  if (count >= 100) return 'warning'
  if (count >= 50) return 'success'
  return 'info'
}

// 格式化日期
const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return '-'
  return dayjs(date).format(format)
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchIPIntelligence()
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.value = {
    ip: '',
    riskLevel: '',
    type: '',
    hitCountRange: '',
    targetOrg: '',
    startDate: '',
    endDate: '',
    keyword: ''
  }
  dateRange.value = null
  handleSearch()
}

// 刷新列表
const refreshList = () => {
  fetchIPIntelligence()
}

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

// 处理表格排序变化
const handleSortChange = (column) => {
  // 实现排序逻辑
  fetchIPIntelligence()
}

// 处理每页显示数量变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchIPIntelligence()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchIPIntelligence()
}

// 查看详情
const handleDetail = (row) => {
  currentIPIntelligence.value = row
  detailDrawerVisible.value = true
}

// 处理创建
const handleCreate = () => {
  isEditing.value = false
  resetForm()
  formDialogVisible.value = true
}

// 处理编辑
const handleEdit = (row) => {
  isEditing.value = true
  // 填充表单数据
  ipForm.value = {
    id: row.id,
    ip: row.ip || '',
    location: row.location || '',
    type: row.type || '',
    riskLevel: row.riskLevel || '',
    hitCount: row.hitCount || 1,
    targetOrg: row.targetOrg || '',
    description: row.description || '',
    source: row.source || '',
    discoveryDate: row.discoveryDate || '',
    tags: row.tags || '',
    pushReason: row.pushReason || ''
  }
  formDialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  ipForm.value = {
    ip: '',
    location: '',
    type: '',
    riskLevel: '',
    hitCount: 1,
    targetOrg: '',
    description: '',
    source: '',
    discoveryDate: '',
    tags: '',
    pushReason: ''
  }
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const formData = { ...ipForm.value }

    // 如果没有设置发现日期，使用当前日期
    if (!formData.discoveryDate) {
      formData.discoveryDate = dayjs().format('YYYY-MM-DD')
    }

    if (isEditing.value) {
      // 更新IP情报
      await api.updateIPIntelligence(formData.id, formData)
      ElMessage.success('更新成功')
    } else {
      // 创建IP情报
      await api.createIPIntelligence(formData)
      ElMessage.success('创建成功')
    }

    formDialogVisible.value = false
    fetchIPIntelligence()
  } catch (error) {
    if (error.response && error.response.data && error.response.data.msg) {
      ElMessage.error(error.response.data.msg)
    } else {
      ElMessage.error(isEditing.value ? '更新失败' : '创建失败')
    }
    console.error('提交表单失败:', error)
  } finally {
    submitting.value = false
  }
}

// 处理推送
const handlePush = async (row) => {
  currentPushIP.value = row

  try {
    // 获取推送通道和策略
    const [channelsRes, policiesRes] = await Promise.all([
      api.getPushChannels(),
      api.getPushPolicies()
    ])

    pushChannels.value = channelsRes.data?.data || []
    pushPolicies.value = policiesRes.data?.data || []

    selectedChannelId.value = ''
    selectedPolicyId.value = ''
    pushDialogVisible.value = true
  } catch (error) {
    console.error('获取推送配置失败:', error)
    ElMessage.error('获取推送配置失败')
  }
}

// 执行推送
const executePush = async () => {
  if (!currentPushIP.value) return

  try {
    pushing.value = true

    const params = {}
    if (selectedChannelId.value) {
      params.channel_id = selectedChannelId.value
    }
    if (selectedPolicyId.value) {
      params.policy_id = selectedPolicyId.value
    }

    await api.pushIPIntelligence(currentPushIP.value.id, params.channel_id, params.policy_id)
    ElMessage.success('推送成功')
    pushDialogVisible.value = false
  } catch (error) {
    console.error('推送失败:', error)
    if (error.response && error.response.data && error.response.data.msg) {
      ElMessage.error(error.response.data.msg)
    } else {
      ElMessage.error('推送失败')
    }
  } finally {
    pushing.value = false
  }
}

// 使用默认策略推送
const pushWithDefaultPolicy = async () => {
  if (!currentPushIP.value) return

  try {
    pushing.value = true
    await api.pushIPIntelligence(currentPushIP.value.id)
    ElMessage.success('推送成功')
    pushDialogVisible.value = false
  } catch (error) {
    console.error('推送失败:', error)
    if (error.response && error.response.data && error.response.data.msg) {
      ElMessage.error(error.response.data.msg)
    } else {
      ElMessage.error('推送失败')
    }
  } finally {
    pushing.value = false
  }
}

// 处理删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除IP为 ${row.ip} 的情报记录吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.deleteIPIntelligence(row.id)
    ElMessage.success('删除成功')
    fetchIPIntelligence()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除IP情报失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${multipleSelection.value.length} 条情报记录吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = multipleSelection.value.map(item => item.id)
    await api.batchDeleteIPIntelligence(ids)
    ElMessage.success('批量删除成功')
    fetchIPIntelligence()
    multipleSelection.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除IP情报失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 导出Excel
const handleExport = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    return
  }
  // 实现导出逻辑
  ElMessage.info('导出功能将在后续开发')
}

// 获取IP情报数据
const fetchIPIntelligence = async () => {
  loading.value = true

  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ip: filterForm.value.ip,
      risk_level: filterForm.value.riskLevel,
      type: filterForm.value.type,
      target_org: filterForm.value.targetOrg,
      hit_count_range: filterForm.value.hitCountRange,
      start_date: filterForm.value.startDate,
      end_date: filterForm.value.endDate,
      keyword: filterForm.value.keyword || '',
      order_by: 'created_at',
      order_dir: 'desc'
    }

    // 过滤空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await api.getIPIntelligence(params)

    if (response.data && response.data.data) {
      ipIntelligence.value = response.data.data.items || []
      total.value = response.data.data.total || 0
    } else {
      ipIntelligence.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取IP情报列表失败:', error)
    ElMessage.error('获取IP情报列表失败')
    ipIntelligence.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchIPIntelligence()
})
</script>

<style scoped>
.ip-intelligence-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 18px;
  font-weight: bold;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.operation-buttons {
  display: flex;
  justify-content: space-around;
}

.custom-descriptions {
  padding: 20px;
}

.custom-descriptions-row {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.custom-descriptions-label {
  font-weight: bold;
  width: 100px;
  color: #606266;
}

.custom-descriptions-content {
  flex: 1;
}

.push-dialog-content {
  padding: 10px 0;
}

.ip-info {
  margin-bottom: 20px;
}

.ip-info h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.ip-info p {
  margin: 8px 0;
  color: #606266;
  line-height: 1.5;
}

.push-options h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style> 