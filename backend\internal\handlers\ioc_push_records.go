package handlers

import (
	"time"

	"github.com/gin-gonic/gin"

	"vulnerability_push/internal/models"
)

// GetIOCIntelligencePushRecordsRequest 获取IOC情报推送记录请求
type GetIOCIntelligencePushRecordsRequest struct {
	Page        int    `form:"page"`
	PageSize    int    `form:"page_size"`
	Keyword     string `form:"keyword"`
	Status      string `form:"status"`
	ChannelType string `form:"channel_type"`
	StartTime   string `form:"start_time"`
	EndTime     string `form:"end_time"`
}

// GetIOCIntelligencePushRecords 获取IOC情报推送记录列表
func (h *IOCHandler) GetIOCIntelligencePushRecords(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GetIOCIntelligencePushRecordsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询
	query := h.db.Model(&models.IOCIntelligencePushRecord{}).
		Preload("IOCIntelligence").
		Preload("Channel")

	// 应用过滤条件
	if req.Keyword != "" {
		// 关键词搜索，匹配IOC值或通道名称
		query = query.Joins("LEFT JOIN ioc_intelligence ON ioc_intelligence_push_records.ioc_intelligence_id = ioc_intelligence.id").
			Where("ioc_intelligence.ioc LIKE ? OR ioc_intelligence_push_records.channel_name LIKE ?",
				"%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	if req.ChannelType != "" {
		query = query.Where("channel_type = ?", req.ChannelType)
	}

	if req.StartTime != "" {
		if startTime, err := time.Parse("2006-01-02", req.StartTime); err == nil {
			query = query.Where("pushed_at >= ?", startTime.Unix())
		}
	}

	if req.EndTime != "" {
		if endTime, err := time.Parse("2006-01-02", req.EndTime); err == nil {
			// 结束时间包含当天，所以加一天
			endTime = endTime.AddDate(0, 0, 1)
			query = query.Where("pushed_at < ?", endTime.Unix())
		}
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取推送记录总数失败: "+err.Error())
		return
	}

	// 应用排序和分页
	var records []models.IOCIntelligencePushRecord
	offset := (req.Page - 1) * req.PageSize

	if err := query.Order("pushed_at DESC").Offset(offset).Limit(req.PageSize).Find(&records).Error; err != nil {
		h.InternalServerError(c, "获取推送记录列表失败: "+err.Error())
		return
	}

	// 转换为响应格式
	var responseRecords []map[string]interface{}
	for _, record := range records {
		recordData := map[string]interface{}{
			"id":                  record.ID,
			"iocIntelligenceId":   record.IOCIntelligenceID,
			"channelId":           record.ChannelID,
			"channelName":         record.ChannelName,
			"channelType":         record.ChannelType,
			"status":              record.Status,
			"errorMessage":        record.ErrorMessage,
			"pushedAt":            record.PushedAt,
			"pushedAtFormatted":   time.Unix(record.PushedAt, 0).Format("2006-01-02 15:04:05"),
		}

		// 添加IOC情报信息（如果预加载成功）
		if record.IOCIntelligence != nil {
			recordData["iocIntelligence"] = map[string]interface{}{
				"id":        record.IOCIntelligence.ID,
				"ioc":       record.IOCIntelligence.IOC,
				"iocType":   record.IOCIntelligence.IOCType,
				"riskLevel": record.IOCIntelligence.RiskLevel,
				"type":      record.IOCIntelligence.Type,
			}
		}

		// 添加推送通道信息（如果预加载成功）
		if record.Channel != nil {
			recordData["channel"] = map[string]interface{}{
				"id":   record.Channel.ID,
				"name": record.Channel.Name,
				"type": record.Channel.Type,
			}
		}

		responseRecords = append(responseRecords, recordData)
	}

	h.PaginatedSuccess(c, responseRecords, total, req.Page, req.PageSize)
}

// DeleteIOCIntelligencePushRecord 删除IOC情报推送记录
func (h *IOCHandler) DeleteIOCIntelligencePushRecord(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	// TODO: 实现IOC情报推送记录删除逻辑
	_ = id

	h.SuccessWithMessage(c, "删除IOC情报推送记录成功", nil)
}
