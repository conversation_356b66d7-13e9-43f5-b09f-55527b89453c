<template>
  <div class="vulnerability-list">
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="关键词">
          <el-input
            v-model="filterForm.keyword"
            placeholder="漏洞名称/编号/描述/标签"
            clearable
            prefix-icon="Search"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="危害等级">
          <el-select 
            v-model="filterForm.severity" 
            placeholder="全部" 
            clearable
            style="width: 180px;"
          >
            <el-option label="严重" value="严重" />
            <el-option label="高危" value="高危" />
            <el-option label="中危" value="中危" />
            <el-option label="低危" value="低危" />
            <el-option label="信息" value="信息" />
          </el-select>
        </el-form-item>
        <el-form-item label="来源">
          <el-select
            v-model="filterForm.source"
            placeholder="选择来源"
            clearable
            filterable
            :loading="sourcesLoading"
          >
            <el-option
              v-for="source in availableSources"
              :key="source"
              :label="source"
              :value="source"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="入库日期">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon> 搜索
          </el-button>
          <el-button @click="resetFilter">
            <el-icon><RefreshLeft /></el-icon> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="hover" class="table-card">
      <div class="table-header">
        <div class="table-title">漏洞列表</div>
        <div class="table-actions">
          <el-button type="primary" @click="refreshList">
            <el-icon><Refresh /></el-icon> 刷新
          </el-button>
          <el-button type="info" @click="navigateToIPIntelligence">
            <el-icon><Monitor /></el-icon> IP情报
          </el-button>
          <el-button type="success" v-if="isAdmin" @click="handleCreate">
            <el-icon><Plus /></el-icon> 添加漏洞
          </el-button>
          <el-button 
            type="success" 
            v-if="isAdmin" 
            @click="handleBatchPush"
            :disabled="multipleSelection.length === 0"
          >
            <el-icon><Position /></el-icon> 批量推送
          </el-button>
          <el-button 
            type="danger" 
            v-if="isAdmin" 
            @click="handleBatchDelete"
            :disabled="multipleSelection.length === 0"
          >
            <el-icon><Delete /></el-icon> 批量删除
          </el-button>
          <el-button 
            type="primary" 
            @click="handleExport"
            :disabled="multipleSelection.length === 0"
          >
            <el-icon><Download /></el-icon> 导出Excel
          </el-button>
        </div>
      </div>

      <el-table
        :data="vulnerabilities"
        border
        style="width: 100%"
        v-loading="loading"
        row-key="id"
        stripe
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          textAlign: 'center',
          fontWeight: '500',
          writingMode: 'horizontal-tb',
          whiteSpace: 'nowrap'
        }"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="vulnId" label="漏洞编号" width="150" show-overflow-tooltip sortable />
        <el-table-column prop="name" label="漏洞名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="severity" label="危害等级" width="100">
          <template #default="scope">
            <el-tag
              :type="getSeverityType(scope.row.severity)"
              effect="dark"
            >
              {{ scope.row.severity }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="标签" width="200">
          <template #default="scope">
            <div class="tag-container">
              <el-tag
                v-for="tag in scope.row.tags"
                :key="tag"
                size="small"
                effect="plain"
                class="tag-item"
              >
                {{ tag }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="disclosureDate" label="披露日期" width="120" sortable="custom" />
        <el-table-column label="信息来源" width="150">
          <template #default="scope">
            <el-tooltip v-if="scope.row.source" :content="scope.row.source" placement="top" effect="light">
              <a 
                v-if="isValidUrl(scope.row.source)" 
                :href="scope.row.source" 
                target="_blank" 
                rel="noopener noreferrer" 
                class="table-source-link"
              >
                {{ formatSource(scope.row.source) }}
              </a>
              <span v-else>{{ formatSource(scope.row.source) }}</span>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="operation-buttons">
              <el-tooltip content="查看详情" placement="top">
                <el-button size="small" type="primary" circle @click="handleDetail(scope.row)">
                  <el-icon><InfoFilled /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip v-if="isAdmin" content="编辑" placement="top">
                <el-button size="small" type="warning" circle @click="handleEdit(scope.row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip v-if="isAdmin" content="推送" placement="top">
                <el-button size="small" type="success" circle @click="handlePush(scope.row)">
                  <el-icon><Position /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip v-if="isAdmin" content="删除" placement="top">
                <el-button size="small" type="danger" circle @click="handleDelete(scope.row)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="入库时间" width="180" sortable>
          <template #default="scope">
            {{ formatDate(scope.row.createdAt, 'YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>

    <!-- 漏洞详情抽屉 -->
    <el-drawer
      v-model="detailDrawerVisible"
      title="漏洞详情"
      size="60%"
      direction="rtl"
      destroy-on-close
    >
      <div v-if="currentVulnerability" class="vulnerability-detail">
        <div class="custom-descriptions">
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">漏洞名称</div>
            <div class="custom-descriptions-content">{{ currentVulnerability.name }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">漏洞编号</div>
            <div class="custom-descriptions-content">{{ currentVulnerability.vulnId }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">危害等级</div>
            <div class="custom-descriptions-content">
              <el-tag :type="getSeverityType(currentVulnerability.severity)" effect="dark">
                {{ currentVulnerability.severity }}
              </el-tag>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">标签</div>
            <div class="custom-descriptions-content">
              <div class="tag-container">
                <el-tag
                  v-for="tag in currentVulnerability.tags"
                  :key="tag"
                  size="small"
                  effect="plain"
                  class="tag-item"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">披露日期</div>
            <div class="custom-descriptions-content">{{ currentVulnerability.disclosureDate }}</div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">信息来源</div>
            <div class="custom-descriptions-content">
              <a 
                v-if="isValidUrl(currentVulnerability.source)" 
                :href="currentVulnerability.source" 
                target="_blank" 
                rel="noopener noreferrer" 
                class="plain-link"
              >
                {{ currentVulnerability.source }}
              </a>
              <span v-else>{{ currentVulnerability.source || '-' }}</span>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">推送原因</div>
            <div class="custom-descriptions-content">
              <div class="multiline-text">{{ currentVulnerability.pushReason }}</div>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">漏洞描述</div>
            <div class="custom-descriptions-content">
              <div class="multiline-text">{{ currentVulnerability.description }}</div>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">参考链接</div>
            <div class="custom-descriptions-content">
              <div v-if="currentVulnerability.references && currentVulnerability.references.length > 0">
                <div v-for="(link, index) in currentVulnerability.references" :key="index" class="reference-link">
                  <a :href="link" target="_blank" rel="noopener noreferrer">{{ link }}</a>
                </div>
              </div>
              <div v-else>无</div>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">修复建议</div>
            <div class="custom-descriptions-content">
              <div class="multiline-text">{{ currentVulnerability.remediation }}</div>
            </div>
          </div>
          <div class="custom-descriptions-row">
            <div class="custom-descriptions-label">入库时间</div>
            <div class="custom-descriptions-content">
              {{ formatDate(currentVulnerability.createdAt, 'YYYY-MM-DD HH:mm:ss') }}
            </div>
          </div>
        </div>
        
        <div class="drawer-footer" v-if="isAdmin">
          <el-button type="primary" @click="handlePush(currentVulnerability)">
            <el-icon><Position /></el-icon> 推送漏洞
          </el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 推送漏洞对话框 -->
    <el-dialog
      v-model="pushDialogVisible"
      title="推送漏洞"
      width="500px"
      destroy-on-close
    >
      <el-form :model="pushForm" label-width="100px">
        <el-form-item label="推送方式">
          <el-radio-group v-model="pushForm.pushType">
            <el-radio label="policy">使用推送策略</el-radio>
            <el-radio label="channel">指定推送通道</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="推送策略" v-if="pushForm.pushType === 'policy'">
          <el-select v-model="pushForm.policyId" placeholder="请选择推送策略" style="width: 100%">
            <el-option
              v-for="policy in pushPolicies"
              :key="policy.id"
              :label="policy.name + (policy.isDefault ? ' (默认)' : '')"
              :value="policy.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="推送通道" v-if="pushForm.pushType === 'channel'">
          <el-select v-model="pushForm.channelId" placeholder="请选择推送通道" style="width: 100%">
            <el-option
              v-for="channel in pushChannels"
              :key="channel.id"
              :label="channel.name"
              :value="channel.id"
              :disabled="!channel.status"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="pushForm.isBatch">
          <div class="batch-info">
            已选择 {{ multipleSelection.length }} 个漏洞进行批量推送
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="pushDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmPush" :loading="pushing">确定推送</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Plus, Delete, Edit, InfoFilled, Search, RefreshLeft, Position, Link, Download, Monitor } from '@element-plus/icons-vue'
import api from './api'
import type { Vulnerability, PushPolicy, PushChannel } from './api'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'

const router = useRouter()

// 状态变量
const loading = ref(false)
const sourcesLoading = ref(false)
const vulnerabilities = ref<Vulnerability[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const detailDrawerVisible = ref(false)
const currentVulnerability = ref<Vulnerability | null>(null)
const isAdmin = ref(false)
const multipleSelection = ref<Vulnerability[]>([])
const availableSources = ref<string[]>([])
const dateRange = ref<[string, string] | null>(null)
const sortBy = ref('')
const sortOrder = ref('')

// 推送相关状态
const pushDialogVisible = ref(false)
const pushPolicies = ref<PushPolicy[]>([])
const pushChannels = ref<PushChannel[]>([])
const pushing = ref(false)
const pushForm = ref({
  pushType: 'policy',
  policyId: null as number | null,
  channelId: null as number | null,
  vulnerabilityId: null as number | null,
  isBatch: false
})

// 过滤表单
const filterForm = ref({
  keyword: '',
  severity: '',
  source: '',
  startDate: '',
  endDate: '',
})

// 获取当前用户信息
const fetchCurrentUser = async () => {
  try {
    const res = await api.getCurrentUser()
    console.log('获取用户信息响应:', res.data)
    if (res.data && res.data.data && res.data.data.user) {
      isAdmin.value = res.data.data.user.role === 'admin'
      console.log('用户角色:', res.data.data.user.role, '是否管理员:', isAdmin.value)
    }
  } catch (error) {
    console.error('获取用户信息失败', error)
  }
}

// 获取所有可用来源
const fetchAvailableSources = async () => {
  sourcesLoading.value = true
  try {
    // 创建一个映射来规范化来源名称
    const sourceMapping: Record<string, string> = {
      'NVD漏洞数据库': 'NVD漏洞数据库',
      '美国国家漏洞数据库(NVD)': 'NVD漏洞数据库',
      '奇安信威胁情报': '奇安信威胁情报中心',
      '奇安信威胁情报中心': '奇安信威胁情报中心',
      'OSCS开源安全情报预警': 'OSCS情报预警'
    }

    // 创建一个集合存储所有来源
    const allSources = new Set<string>()

    // 首先添加默认来源（确保基本来源总是可用）
    const defaultSources = [
      '长亭漏洞库',
      '奇安信威胁情报中心',
      '阿里云漏洞库',
      '微步威胁情报',
      'OSCS情报预警',
      'Seebug漏洞平台',
      '启明星辰漏洞通告',
      'NVD漏洞数据库'
    ]

    defaultSources.forEach(source => allSources.add(source))

    // 尝试从API获取采集器提供商（不依赖用户权限）
    try {
      const res = await api.getCrawlerProviders()
      console.log('采集器提供商API响应:', res.data)

      if (res.data && res.data.data && Array.isArray(res.data.data)) {
        // 添加从 API 获取的采集器提供商
        res.data.data.forEach((provider: any) => {
          if (provider.DisplayName) {
            // 使用映射规范化来源名称
            const normalizedName = sourceMapping[provider.DisplayName] || provider.DisplayName
            allSources.add(normalizedName)
          }
        })
        console.log('从API获取的来源数量:', res.data.data.length)
      }
    } catch (error) {
      console.error('获取采集器提供商失败，使用默认来源:', error)
      // API失败时已经有默认来源，不需要额外处理
    }
    
    // 注释掉重复的API请求，使用硬编码的来源列表
    // 如果需要动态获取来源，可以考虑创建专门的API接口
    // try {
    //   const vulnRes = await api.getVulnerabilities({
    //     page: 1,
    //     pageSize: 100,
    //   })
    //
    //   if (vulnRes.data && vulnRes.data.data && vulnRes.data.data.list) {
    //     vulnRes.data.data.list.forEach((vuln: Vulnerability) => {
    //       if (vuln.source) {
    //         const formattedSource = formatSource(vuln.source)
    //         if (formattedSource && formattedSource !== '-') {
    //           // 使用映射规范化来源名称
    //           const normalizedName = sourceMapping[formattedSource] || formattedSource
    //           allSources.add(normalizedName)
    //         }
    //       }
    //     })
    //   }
    // } catch (error) {
    //   console.error('获取漏洞列表失败', error)
    // }
    
    // 转换为数组并排序
    availableSources.value = Array.from(allSources).sort()

    // 删除特定的重复项
    availableSources.value = availableSources.value.filter(source => source !== 'OSCS开源安全情报预警')

    console.log('最终可用来源列表:', availableSources.value)
  } catch (error) {
    console.error('获取来源列表失败', error)
    
    // 使用硬编码的备用来源
    const fallbackSources = new Set<string>([
      '长亭漏洞库',
      '奇安信威胁情报中心',
      '阿里云漏洞库',
      '微步威胁情报',
      'OSCS情报预警',
      'Seebug漏洞平台',
      '启明星辰漏洞通告',
      'NVD漏洞数据库'
    ])
    
    availableSources.value = Array.from(fallbackSources).sort()
  } finally {
    sourcesLoading.value = false
  }
}

// 获取漏洞列表
const fetchVulnerabilities = async () => {
  loading.value = true
  try {
    // 创建反向映射表，用于查询时将规范化的名称转换回可能的原始格式
    const sourceReverseMapping: Record<string, string[]> = {
      'NVD漏洞数据库': ['NVD漏洞数据库', '美国国家漏洞数据库(NVD)', 'nvd.nist.gov'],
      '奇安信威胁情报中心': ['奇安信威胁情报中心', '奇安信威胁情报', 'ti.qianxin.com'],
      '微步威胁情报': ['微步威胁情报', 'x.threatbook.com', 'threatbook.cn'],
      '长亭漏洞库': ['长亭漏洞库', 'stack.chaitin.com'],
      'Seebug漏洞平台': ['Seebug漏洞平台', 'seebug.org'],
      '阿里云漏洞库': ['阿里云漏洞库', 'avd.aliyun.com'],
      'OSCS情报预警': ['OSCS情报预警', 'oscs1024.com', 'oscs.net'],
      '启明星辰漏洞通告': ['启明星辰漏洞通告', 'venustech.com.cn', 'venustech.com']
    };
    
    // 处理源筛选参数
    let sourceQuery = filterForm.value.source;
    
    // 如果用户选择了规范化的来源名称，则尝试使用原始格式
    if (sourceQuery && sourceReverseMapping[sourceQuery]) {
      // 使用第一个匹配项作为查询条件，通常是原始名称
      sourceQuery = sourceReverseMapping[sourceQuery][0];
    }
    
    const res = await api.getVulnerabilities({
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: filterForm.value.keyword,
      severity: filterForm.value.severity,
      source: sourceQuery,
      startDate: filterForm.value.startDate,
      endDate: filterForm.value.endDate,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value
    })
    
    if (res.data && res.data.data) {
      vulnerabilities.value = res.data.data.list || []
      total.value = res.data.data.pagination?.total || 0
    }
  } catch (error) {
    console.error('获取漏洞列表失败', error)
  } finally {
    loading.value = false
  }
}

// 获取漏洞详情
const fetchVulnerabilityDetail = async (id: number) => {
  try {
    const res = await api.getVulnerabilityDetail(id)
    if (res.data && res.data.data) {
      currentVulnerability.value = res.data.data
      detailDrawerVisible.value = true
    }
  } catch (error) {
    console.error('获取漏洞详情失败', error)
  }
}

// 根据危害等级获取标签类型
const getSeverityType = (severity: string) => {
  switch (severity) {
    case '严重':
      return 'error'  // 使用更深的红色
    case '高危':
      return 'danger'
    case '中危':
      return 'warning'
    case '低危':
      return 'success'
    case '信息':
      return 'info'
    default:
      return 'info'
  }
}

// 格式化日期
const formatDate = (timestamp: number | undefined, format: string = 'YYYY-MM-DD') => {
  if (!timestamp) return '-'
  const date = new Date(timestamp * 1000)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year.toString())
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

// 格式化信息来源
const formatSource = (source: string) => {
  if (!source) return '-';
  
  try {
    // 尝试解析URL
    if (source.startsWith('http://') || source.startsWith('https://')) {
      const url = new URL(source);
      
      // 根据URL判断采集源类型，确保与后端采集器显示完全一致
      if (url.hostname.includes('stack.chaitin.com')) {
        return '长亭漏洞库';
      } else if (url.hostname.includes('nvd.nist.gov')) {
        return 'NVD漏洞数据库';
      } else if (url.hostname.includes('seebug.org')) {
        return 'Seebug漏洞平台';
      } else if (url.hostname.includes('ti.qianxin.com')) {
        return '奇安信威胁情报中心';
      } else if (url.hostname.includes('x.threatbook.com') || url.hostname.includes('threatbook.cn')) {
        return '微步威胁情报';
      } else if (url.hostname.includes('avd.aliyun.com')) {
        return '阿里云漏洞库';
      } else if (url.hostname.includes('oscs1024.com') || url.hostname.includes('oscs.net')) {
        return 'OSCS情报预警';
      } else if (url.hostname.includes('venustech.com.cn') || url.hostname.includes('venustech.com')) {
        return '启明星辰漏洞通告';
      } else if (url.hostname.includes('cve.mitre.org')) {
        // 对于CVE链接，根据URL中的ID查询NVD
        const cveMatch = url.pathname.match(/CVE-\d+-\d+/i);
        if (cveMatch) {
          return 'NVD漏洞数据库';
        }
        return 'CVE';
      } else {
        // 如果不是常见的采集源，返回特定名称
        const domain = url.hostname.replace('www.', '');
        // 检查已知的采集器名称是否包含该域名的一部分
        if (domain.includes('nvd') || domain.includes('nist.gov')) {
          return 'NVD漏洞数据库';
        } else if (domain.includes('aliyun')) {
          return '阿里云漏洞库';
        } else if (domain.includes('qianxin') || domain.includes('threatbook')) {
          return '奇安信威胁情报中心';
        } else if (domain.includes('seebug') || domain.includes('knownsec')) {
          return 'Seebug漏洞平台';
        } else if (domain.includes('chaitin')) {
          return '长亭漏洞库';
        } else if (domain.includes('oscs')) {
          return 'OSCS情报预警';
        } else if (domain.includes('venus') || domain.includes('venustech')) {
          return '启明星辰漏洞通告';
        }
        return domain;
      }
    }
  } catch (e) {
    // 解析URL失败，尝试从文本中提取信息
    if (source.includes('nvd') || source.includes('nist.gov')) {
      return 'NVD漏洞数据库';
    } else if (source.toLowerCase().includes('cve-')) {
      return 'NVD漏洞数据库';
    }
  }
  
  // 如果不是URL或解析失败，尝试匹配已知来源名称
  const sourceLower = source.toLowerCase();
  if (sourceLower.includes('nvd') || sourceLower.includes('国家漏洞数据库') || sourceLower.includes('nist.gov')) {
    return 'NVD漏洞数据库';
  } else if (sourceLower.includes('aliyun')) {
    return '阿里云漏洞库';
  } else if (sourceLower.includes('qianxin') || sourceLower.includes('奇安信')) {
    return '奇安信威胁情报中心';
  } else if (sourceLower.includes('seebug')) {
    return 'Seebug漏洞平台';
  } else if (sourceLower.includes('chaitin')) {
    return '长亭漏洞库';
  } else if (sourceLower.includes('oscs')) {
    return 'OSCS情报预警';
  } else if (sourceLower.includes('venus')) {
    return '启明星辰漏洞通告';
  } else if (sourceLower.includes('threatbook')) {
    return '微步威胁情报';
  }
  
  // 如无法识别，直接返回原始值
  return source;
}

// 处理日期范围变化
const handleDateRangeChange = (val: [string, string] | null) => {
  if (val) {
    filterForm.value.startDate = val[0]
    filterForm.value.endDate = val[1]
    dateRange.value = val
  } else {
    filterForm.value.startDate = ''
    filterForm.value.endDate = ''
    dateRange.value = null
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchVulnerabilities()
}

// 重置过滤条件
const resetFilter = () => {
  filterForm.value = {
    keyword: '',
    severity: '',
    source: '',
    startDate: '',
    endDate: '',
  }
  dateRange.value = null
  handleSearch()
}

// 刷新列表
const refreshList = () => {
  fetchVulnerabilities()
}

// 处理排序变化
const handleSortChange = (column: { prop?: string, order?: string }) => {
  console.log('排序变化:', column)
  if (column.prop) {
    sortBy.value = column.prop
    sortOrder.value = column.order === 'ascending' ? 'asc' : 'desc'
  } else {
    sortBy.value = ''
    sortOrder.value = ''
  }
  fetchVulnerabilities()
}

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchVulnerabilities()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchVulnerabilities()
}

// 处理表格选择变化
const handleSelectionChange = (selection: Vulnerability[]) => {
  multipleSelection.value = selection
}

// 查看详情
const handleDetail = (row: Vulnerability) => {
  fetchVulnerabilityDetail(row.id!)
}

// 编辑漏洞
const handleEdit = (row: Vulnerability) => {
  // 跳转到编辑页面
  router.push(`/vulnerability/edit/${row.id}`)
}

// 创建漏洞
const handleCreate = () => {
  // 跳转到创建页面
  router.push('/vulnerability/create')
}

// 删除漏洞
const handleDelete = (row: Vulnerability) => {
  ElMessageBox.confirm(`确定要删除漏洞 ${row.name} 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await api.deleteVulnerability(row.id!)
      ElMessage.success('删除成功')
      fetchVulnerabilities()
    } catch (error) {
      console.error('删除漏洞失败', error)
    }
  }).catch(() => {})
}

// 批量删除漏洞
const handleBatchDelete = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请先选择要删除的漏洞')
    return
  }

  ElMessageBox.confirm(`确定要删除选中的 ${multipleSelection.value.length} 个漏洞吗?`, '批量删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    loading.value = true
    try {
      const ids = multipleSelection.value.map(item => item.id!).filter(id => id !== undefined)
      await api.batchDeleteVulnerabilities(ids)
      ElMessage.success(`成功删除 ${multipleSelection.value.length} 个漏洞`)
      fetchVulnerabilities()
    } catch (error) {
      console.error('批量删除漏洞失败', error)
      ElMessage.error('批量删除失败，请重试')
    } finally {
      loading.value = false
    }
  }).catch(() => {})
}

// 获取推送策略
const fetchPushPolicies = async () => {
  // 只有管理员才调用此接口
  if (!isAdmin.value) return;
  
  try {
    const res = await api.getPushPolicies({ page: 1, pageSize: 100 })
    console.log('获取推送策略响应:', res.data)
    console.log('推送策略数据结构:', res.data.data)
    console.log('推送策略列表:', res.data.data?.list)

    // 处理策略数据
    pushPolicies.value = (res.data.data?.list || []).map((policy: any) => ({
      id: Number(policy.id || 0),
      name: policy.name || '',
      isDefault: Boolean(policy.isDefault),
    }))
    
    // 如果有默认策略，自动选择
    const defaultPolicy = pushPolicies.value.find(p => p.isDefault)
    if (defaultPolicy && defaultPolicy.id) {
      pushForm.value.policyId = defaultPolicy.id
    }
  } catch (error) {
    console.error('获取推送策略失败', error)
  }
}

// 获取推送通道
const fetchPushChannels = async () => {
  // 只有管理员才调用此接口
  if (!isAdmin.value) return;
  
  try {
    const res = await api.getPushChannels({ page: 1, pageSize: 100 })
    console.log('获取推送通道响应:', res.data)
    
    // 处理通道数据
    pushChannels.value = (res.data.data?.list || []).map((channel: any) => ({
      id: Number(channel.id || 0),
      name: channel.name || '',
      status: Boolean(channel.status)
    }))
    
    // 如果有可用通道，自动选择第一个
    const availableChannel = pushChannels.value.find(c => c.status)
    if (availableChannel && availableChannel.id) {
      pushForm.value.channelId = availableChannel.id
    }
  } catch (error) {
    console.error('获取推送通道失败', error)
  }
}

// 处理推送漏洞
const handlePush = (vulnerability: Vulnerability) => {
  // 确保漏洞ID有效
  if (!vulnerability.id) {
    ElMessage.warning('无效的漏洞ID')
    return
  }

  // 设置当前漏洞（用于确认对话框显示漏洞名称）
  currentVulnerability.value = vulnerability

  // 重置表单
  pushForm.value = {
    pushType: 'policy',
    policyId: null,
    channelId: null,
    vulnerabilityId: Number(vulnerability.id),
    isBatch: false
  }

  // 如果有默认策略，自动选择
  const defaultPolicy = pushPolicies.value.find(p => p.isDefault)
  if (defaultPolicy && defaultPolicy.id) {
    pushForm.value.policyId = defaultPolicy.id
  }

  // 显示推送对话框
  pushDialogVisible.value = true
}

// 处理批量推送
const handleBatchPush = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请先选择要推送的漏洞')
    return
  }
  
  // 检查所有选中的漏洞是否都有有效ID
  const hasInvalidId = multipleSelection.value.some(vuln => !vuln.id)
  if (hasInvalidId) {
    ElMessage.warning('存在无效的漏洞ID')
    return
  }
  
  // 重置表单
  pushForm.value = {
    pushType: 'policy',
    policyId: null,
    channelId: null,
    vulnerabilityId: null,
    isBatch: true
  }
  
  // 如果有默认策略，自动选择
  const defaultPolicy = pushPolicies.value.find(p => p.isDefault)
  if (defaultPolicy && defaultPolicy.id) {
    pushForm.value.policyId = defaultPolicy.id
  }
  
  // 显示推送对话框
  pushDialogVisible.value = true
}

// 确认推送
const confirmPush = async () => {
  if (pushForm.value.pushType === 'policy' && !pushForm.value.policyId) {
    ElMessage.warning('请选择推送策略')
    return
  }
  if (pushForm.value.pushType === 'channel' && !pushForm.value.channelId) {
    ElMessage.warning('请选择推送通道')
    return
  }

  // 添加二次确认
  let confirmMessage = pushForm.value.isBatch 
    ? `确定要推送选中的 ${multipleSelection.value.length} 个漏洞吗?` 
    : `确定要推送漏洞 "${currentVulnerability.value?.name || '未知漏洞'}" 吗?`;
  
  ElMessageBox.confirm(confirmMessage, '推送确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    pushing.value = true
    try {
      if (pushForm.value.isBatch) {
        // 批量推送
        const promises = multipleSelection.value.map(row => {
          const vulnId = Number(row.id)
          if (pushForm.value.pushType === 'policy' && pushForm.value.policyId) {
            return api.pushVulnerability(vulnId, undefined, Number(pushForm.value.policyId))
          } else if (pushForm.value.channelId) {
            return api.pushVulnerability(vulnId, Number(pushForm.value.channelId))
          }
          return null
        }).filter(Boolean)
        
        await Promise.all(promises)
        ElMessage.success(`成功推送 ${promises.length} 个漏洞`)
      } else {
        // 单个推送
        if (!pushForm.value.vulnerabilityId) {
          ElMessage.warning('无效的漏洞ID')
          return
        }
        
        if (pushForm.value.pushType === 'policy' && pushForm.value.policyId) {
          await api.pushVulnerability(pushForm.value.vulnerabilityId, undefined, Number(pushForm.value.policyId))
        } else if (pushForm.value.channelId) {
          await api.pushVulnerability(pushForm.value.vulnerabilityId, Number(pushForm.value.channelId))
        }
        
        ElMessage.success('推送成功')
      }
      
      pushDialogVisible.value = false
    } catch (error: any) {
      console.error('推送失败', error)
      
      // 处理敏感词错误
      if (error.response?.status === 403 && error.response?.data?.data?.sensitive_words) {
        const sensitiveWords = error.response.data.data.sensitive_words
        ElMessageBox.alert(
          `推送内容包含以下敏感词：${sensitiveWords.join(', ')}`,
          '推送被拦截',
          {
            confirmButtonText: '确定',
            type: 'error',
          }
        )
      } else {
        ElMessage.error('推送失败：' + (error.response?.data?.msg || '未知错误'))
      }
    } finally {
      pushing.value = false
    }
  }).catch(() => {
    // 用户取消操作
    ElMessage.info('已取消推送')
  })
}

// 检查是否是有效的URL
const isValidUrl = (url: string | undefined) => {
  if (!url) return false
  try {
    new URL(url)
    return true
  } catch (e) {
    return false
  }
}

// 处理导出Excel
const handleExport = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请先选择要导出的漏洞')
    return
  }

  try {
    // 显示加载中
    loading.value = true
    
    // 获取选中漏洞的ID列表
    const ids = multipleSelection.value.map(item => item.id!)
    
    // 调用API导出Excel
    const response = await api.exportVulnerabilities(ids)
    
    // 创建下载链接
    const blob = new Blob([response.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    
    // 生成文件名 - 直接在前端创建带时间戳的文件名，不依赖后端
    const currentDate = new Date()
    const year = currentDate.getFullYear()
    const month = String(currentDate.getMonth() + 1).padStart(2, '0')
    const day = String(currentDate.getDate()).padStart(2, '0')
    const hours = String(currentDate.getHours()).padStart(2, '0')
    const minutes = String(currentDate.getMinutes()).padStart(2, '0')
    const seconds = String(currentDate.getSeconds()).padStart(2, '0')
    
    const dateStr = `${year}-${month}-${day}`
    const timeStr = `${hours}${minutes}${seconds}`
    const filename = `漏洞数据导出_${dateStr}_${timeStr}.xlsx`
    
    // 创建下载链接并触发下载
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', filename)
    document.body.appendChild(link)
    link.click()
    
    // 清理
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出漏洞失败', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    loading.value = false
  }
}

// 处理IP情报入口
const navigateToIPIntelligence = () => {
  router.push('/ip-intelligence')
}

onMounted(async () => {
  // 先获取用户信息，确定权限
  await fetchCurrentUser()
  
  // 获取漏洞来源和漏洞列表
  fetchAvailableSources()
  fetchVulnerabilities()
  
  // 只有管理员才获取推送策略和通道
  if (isAdmin.value) {
    fetchPushPolicies()
    fetchPushChannels()
  }
})
</script>

<style scoped>
.vulnerability-list {
  padding: 0;
}

/* 自定义严重级别的标签样式 */
:deep(.el-tag--error) {
  background-color: #8b0000;
  border-color: #8b0000;
}

/* 自定义高危级别的标签样式 */
:deep(.el-tag--danger) {
  background-color: #ff4949;
  border-color: #ff4949;
}

.filter-container {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 18px;
  font-weight: bold;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tag-item {
  margin-right: 0;
}

.vulnerability-detail {
  padding: 20px;
}

.multiline-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.reference-link {
  margin-bottom: 5px;
}

.reference-link a {
  color: #409eff;
  text-decoration: none;
}

.reference-link a:hover {
  text-decoration: underline;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.drawer-footer {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  width: 100%;
}

.batch-info {
  color: #409eff;
  font-weight: bold;
}

.external-link-icon {
  margin-left: 5px;
}

.source-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.source-type {
  font-weight: bold;
  color: #409eff;
  padding: 2px 8px;
  background-color: #ecf5ff;
  border-radius: 4px;
  display: inline-block;
}

.source-link-container {
  margin-top: 5px;
}

.source-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.source-name {
  font-weight: bold;
  color: #409eff;
}

.source-link {
  color: #409eff;
  text-decoration: none;
  display: inline-block;
  padding: 4px 10px;
  border: 1px solid #409eff;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.3s;
}

.source-link:hover {
  background-color: #409eff;
  color: #fff;
}

.plain-link {
  color: #409eff;
  text-decoration: none;
  word-break: break-all;
}

.plain-link:hover {
  text-decoration: underline;
}

.table-source-link {
  color: #409eff;
  text-decoration: none;
  word-break: break-all;
}

.table-source-link:hover {
  text-decoration: underline;
}

/* 全局修复表头文字竖排显示问题 */
:deep(.el-table__header th) {
  white-space: nowrap;
  writing-mode: horizontal-tb !important;
}

/* 修复表头文字样式 */
:deep(.el-table__header .cell) {
  white-space: nowrap;
  word-break: keep-all;
}

/* 修复表格样式 */
:deep(.el-table) {
  width: 100% !important;
}

/* 修复表头样式 */
:deep(.el-table .el-table__header-wrapper .el-table__header th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  text-align: center;
  height: auto !important;
  padding: 8px 0;
}

/* 确保表头文字水平显示 */
:deep(.el-table .el-table__header-wrapper .el-table__header th .cell) {
  white-space: normal;
  line-height: 23px;
  word-break: break-word;
  text-align: center;
  writing-mode: horizontal-tb !important;
}

/* 全局修复竖排显示问题 */
:deep([class*="el-"]) {
  writing-mode: horizontal-tb !important;
}

/* 为描述列表标签添加特定样式 */
:deep(.el-descriptions__label) {
  writing-mode: horizontal-tb !important;
  white-space: nowrap !important;
  word-break: keep-all !important;
  width: auto !important;
  display: inline-block !important;
  text-align: right !important;
  padding: 8px 12px !important;
}

.custom-descriptions {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.custom-descriptions-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;
}

.custom-descriptions-row:last-child {
  border-bottom: none;
}

.custom-descriptions-label {
  width: 120px;
  min-width: 120px;
  padding: 12px;
  background-color: #f5f7fa;
  border-right: 1px solid #ebeef5;
  font-weight: bold;
  color: #606266;
  text-align: right;
}

.custom-descriptions-content {
  flex: 1;
  padding: 12px;
  background-color: #fff;
}
</style>