package database

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type   string      `yaml:"type"`   // mysql 或 sqlite
	MySQL  MySQLConfig `yaml:"mysql"`  // MySQL配置
	SQLite SQLiteConfig `yaml:"sqlite"` // SQLite配置
}

// MySQLConfig MySQL数据库配置
type MySQLConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	Database string `yaml:"database"`
	Charset  string `yaml:"charset"`
	Loc      string `yaml:"loc"`
}

// SQLiteConfig SQLite数据库配置
type SQLiteConfig struct {
	File   string `yaml:"file"`   // 数据库文件路径
	Memory bool   `yaml:"memory"` // 是否使用内存数据库
}

// DatabaseManager 数据库管理器
type DatabaseManager struct {
	DB     *gorm.DB
	config *DatabaseConfig
	logger Logger
}

// Logger 日志接口
type Logger interface {
	Debugf(format string, args ...interface{})
	Infof(format string, args ...interface{})
	Warnf(format string, args ...interface{})
	Errorf(format string, args ...interface{})
}

// NewDatabaseManager 创建数据库管理器
func NewDatabaseManager(config *DatabaseConfig, logger Logger) *DatabaseManager {
	return &DatabaseManager{
		config: config,
		logger: logger,
	}
}

// Connect 连接数据库
func (dm *DatabaseManager) Connect() error {
	var err error
	
	switch dm.config.Type {
	case "mysql":
		err = dm.connectMySQL()
	case "sqlite":
		err = dm.connectSQLite()
	default:
		return fmt.Errorf("不支持的数据库类型: %s", dm.config.Type)
	}
	
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}
	
	// 配置连接池
	if err := dm.configureConnectionPool(); err != nil {
		return fmt.Errorf("配置连接池失败: %w", err)
	}
	
	dm.logger.Infof("数据库连接成功，类型: %s", dm.config.Type)
	return nil
}

// connectMySQL 连接MySQL数据库
func (dm *DatabaseManager) connectMySQL() error {
	config := dm.config.MySQL
	
	// 首先连接MySQL服务器（不指定数据库）
	rootDSN := fmt.Sprintf("%s:%s@tcp(%s:%d)/?charset=utf8mb4&parseTime=True&loc=Local&timeout=10s",
		config.User, config.Password, config.Host, config.Port)
	
	dm.logger.Infof("正在连接MySQL服务器: %s:%d", config.Host, config.Port)
	
	// 连接到MySQL服务器
	rootDB, err := gorm.Open(mysql.Open(rootDSN), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		return fmt.Errorf("连接MySQL服务器失败: %w", err)
	}
	
	// 检查并创建数据库
	targetDB := config.Database
	if targetDB == "" {
		targetDB = "vuln_push" // 默认数据库名
	}
	
	var dbExists bool
	err = rootDB.Raw("SELECT 1 FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?", targetDB).Scan(&dbExists).Error
	if err != nil {
		return fmt.Errorf("检查数据库是否存在失败: %w", err)
	}
	
	if !dbExists {
		dm.logger.Infof("数据库 %s 不存在，正在创建...", targetDB)
		err = rootDB.Exec(fmt.Sprintf("CREATE DATABASE `%s` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", targetDB)).Error
		if err != nil {
			return fmt.Errorf("创建数据库失败: %w", err)
		}
		dm.logger.Infof("数据库 %s 创建成功", targetDB)
	}
	
	// 关闭根连接
	if sqlDB, err := rootDB.DB(); err == nil {
		sqlDB.Close()
	}
	
	// 构建目标数据库DSN
	charset := "utf8mb4"
	if config.Charset != "" {
		charset = config.Charset
	}
	
	loc := "Local"
	if config.Loc != "" {
		loc = config.Loc
	}
	
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=%s&timeout=10s&collation=utf8mb4_unicode_ci",
		config.User, config.Password, config.Host, config.Port, targetDB, charset, loc)
	
	// 连接到目标数据库
	dm.DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
		NamingStrategy: schema.NamingStrategy{
			SingularTable: false, // 使用复数表名
		},
	})
	
	return err
}

// connectSQLite 连接SQLite数据库
func (dm *DatabaseManager) connectSQLite() error {
	config := dm.config.SQLite
	
	var dsn string
	if config.Memory {
		dsn = ":memory:"
		dm.logger.Infof("使用SQLite内存数据库")
	} else {
		// 确保数据库文件所在目录存在
		if config.File == "" {
			config.File = "./data.db"
		}
		
		dbDir := filepath.Dir(config.File)
		if dbDir != "." && dbDir != "" {
			if err := os.MkdirAll(dbDir, 0755); err != nil {
				return fmt.Errorf("创建SQLite数据库目录失败: %w", err)
			}
		}
		
		dsn = config.File
		dm.logger.Infof("使用SQLite文件数据库: %s", config.File)
	}
	
	var err error
	dm.DB, err = gorm.Open(sqlite.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
		NamingStrategy: schema.NamingStrategy{
			SingularTable: false, // 使用复数表名
		},
	})
	
	if err != nil {
		return err
	}
	
	// 启用外键约束
	dm.DB.Exec("PRAGMA foreign_keys = ON")
	
	// 启用WAL模式提高性能
	if !config.Memory {
		dm.DB.Exec("PRAGMA journal_mode = WAL")
	}
	
	return nil
}

// configureConnectionPool 配置连接池
func (dm *DatabaseManager) configureConnectionPool() error {
	sqlDB, err := dm.DB.DB()
	if err != nil {
		return err
	}
	
	if dm.config.Type == "mysql" {
		// MySQL连接池配置
		sqlDB.SetMaxIdleConns(10)                  // 最大空闲连接数
		sqlDB.SetMaxOpenConns(100)                 // 最大打开连接数
		sqlDB.SetConnMaxLifetime(time.Hour)        // 连接最大生命周期
		sqlDB.SetConnMaxIdleTime(30 * time.Minute) // 空闲连接最大生命周期
	} else {
		// SQLite连接池配置
		sqlDB.SetMaxIdleConns(1)
		sqlDB.SetMaxOpenConns(1)
		sqlDB.SetConnMaxLifetime(time.Hour)
	}
	
	// 测试连接
	return sqlDB.Ping()
}

// Close 关闭数据库连接
func (dm *DatabaseManager) Close() error {
	if dm.DB == nil {
		return nil
	}
	
	sqlDB, err := dm.DB.DB()
	if err != nil {
		return err
	}
	
	return sqlDB.Close()
}

// GetDB 获取数据库实例
func (dm *DatabaseManager) GetDB() *gorm.DB {
	return dm.DB
}

// Ping 测试数据库连接
func (dm *DatabaseManager) Ping() error {
	if dm.DB == nil {
		return fmt.Errorf("数据库未连接")
	}
	
	sqlDB, err := dm.DB.DB()
	if err != nil {
		return err
	}
	
	return sqlDB.Ping()
}

// Transaction 执行事务
func (dm *DatabaseManager) Transaction(fn func(*gorm.DB) error) error {
	return dm.DB.Transaction(fn)
}

// WithContext 使用上下文
func (dm *DatabaseManager) WithContext(ctx context.Context) *gorm.DB {
	return dm.DB.WithContext(ctx)
}
