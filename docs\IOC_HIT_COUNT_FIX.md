# IOC情报命中次数累计问题修复

## 问题描述

在IOC情报生产过程中，发现命中次数计算存在严重问题：
- **每次生产策略执行时，都会重复处理相同的源数据**
- **没有标记哪些源数据已经被处理过**
- **导致同一批源数据被重复处理，命中次数不断累加**

### 问题根源

1. **缺少处理状态标记**：IOC源数据模型中没有处理状态字段
2. **重复处理逻辑**：生产策略每次都处理时间范围内的所有源数据
3. **累加逻辑错误**：对于已存在的IOC，直接累加命中次数而不检查是否重复处理

## 修复方案

### 1. 数据模型修改

在 `IOCIntelligenceData` 模型中添加处理状态字段：

```go
type IOCIntelligenceData struct {
    // ... 现有字段 ...
    ProcessedStatus  string  `gorm:"size:20;default:'unprocessed';index" json:"processedStatus"` // 处理状态: unprocessed, processed
    ProcessedAt      int64   `gorm:"default:0" json:"processedAt"`                               // 处理时间
    // ... 其他字段 ...
}
```

### 2. 生产策略逻辑修改

#### 2.1 只处理未处理的源数据

```go
// 获取符合条件的IOC情报数据 - 只处理未处理的数据
var sourceData []models.IOCIntelligenceData
query := h.db.Model(&models.IOCIntelligenceData{}).
    Where("created_at BETWEEN ? AND ?", startTime.Unix(), endTime.Unix()).
    Where("processed_status = ?", "unprocessed") // 只处理未处理的数据
```

#### 2.2 追踪处理的源数据

```go
// 按攻击IP分组聚合数据，同时记录源数据ID
type AttackIPGroup struct {
    *models.IOCIntelligenceData
    SourceDataIDs []uint // 记录该组包含的源数据ID
}
```

#### 2.3 处理完成后标记源数据

```go
// 标记源数据为已处理
if len(processedSourceDataIDs) > 0 {
    currentTime := time.Now().Unix()
    updateResult := h.db.Model(&models.IOCIntelligenceData{}).
        Where("id IN ?", processedSourceDataIDs).
        Updates(map[string]interface{}{
            "processed_status": "processed",
            "processed_at":     currentTime,
        })
}
```

### 3. 新增辅助方法

```go
// IsProcessed 检查是否已处理
func (iid *IOCIntelligenceData) IsProcessed() bool {
    return iid.ProcessedStatus == "processed"
}

// MarkAsProcessed 标记为已处理
func (iid *IOCIntelligenceData) MarkAsProcessed() {
    iid.ProcessedStatus = "processed"
    iid.ProcessedAt = time.Now().Unix()
}
```

## 修复效果

### 修复前
- 同一批源数据被重复处理
- IOC命中次数不断累加
- 数据统计不准确

### 修复后
- 每条源数据只处理一次
- IOC命中次数准确反映真实攻击次数
- 避免重复计算，提高数据质量

## 数据库迁移

### 自动迁移（推荐）
重新启动应用程序，GORM会自动添加新字段：
```bash
cd backend
go run main.go
```

### 手动迁移（如果自动迁移失败）

#### 方法1：使用SQL脚本
```bash
mysql -u username -p database_name < add_processed_status_fields.sql
```

#### 方法2：使用迁移工具
```bash
cd backend
go run cmd/migrate_processed_status/main.go -config config.yaml
```

#### 方法3：手动执行SQL
```sql
-- 添加字段
ALTER TABLE ioc_source_data ADD COLUMN processed_status VARCHAR(20) DEFAULT 'unprocessed';
ALTER TABLE ioc_source_data ADD COLUMN processed_at BIGINT DEFAULT 0;
ALTER TABLE ioc_source_data ADD INDEX idx_processed_status (processed_status);

-- 设置默认值
UPDATE ioc_source_data SET processed_status = 'unprocessed', processed_at = 0
WHERE processed_status IS NULL OR processed_status = '';
```

### 兼容性处理
代码已添加兼容性检查，即使字段不存在也能正常运行（但会有重复处理警告）：
- 检查字段是否存在：`hasProcessedStatusColumn()`
- 如果字段不存在，跳过处理状态过滤
- 显示警告信息提醒用户进行数据库迁移

## 验证方法

1. **查看源数据处理状态**：
   ```sql
   SELECT processed_status, COUNT(*) FROM ioc_source_data GROUP BY processed_status;
   ```

2. **检查IOC命中次数变化**：
   - 运行生产策略前后对比IOC命中次数
   - 确保不会重复累加

3. **监控日志输出**：
   - 查看生产策略执行日志
   - 确认只处理未处理的源数据

## 注意事项

1. **现有数据处理**：首次运行修复后的代码时，所有现有源数据都会被标记为未处理状态
2. **性能影响**：添加了处理状态过滤，可能略微提高查询性能
3. **数据一致性**：确保在事务中完成IOC创建和源数据标记操作

## 相关文件

- `backend/internal/models/ioc.go` - 数据模型修改
- `backend/internal/handlers/production_strategy.go` - 生产策略逻辑修改
- `IOC_HIT_COUNT_FIX.md` - 本修复文档
