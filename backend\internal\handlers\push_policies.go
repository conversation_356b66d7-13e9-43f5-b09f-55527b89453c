package handlers

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/internal/models"
)

// GetPushPoliciesRequest 获取推送策略请求
type GetPushPoliciesRequest struct {
	Page      int    `form:"page" binding:"omitempty,min=1"`
	PageSize  int    `form:"pageSize" binding:"omitempty,min=5,max=100"`
	Name      string `form:"name"`
	IsDefault *bool  `form:"is_default"`
}

// UpdatePushPolicyRequest 更新推送策略请求
type UpdatePushPolicyRequest struct {
	Name        *string `json:"name"`
	Description *string `json:"description"`
	ChannelIDs  *string `json:"channelIDs"`
	IsDefault   *bool   `json:"isDefault"`
}

// GetPushPolicies 获取推送策略列表
func (h *PushHandler) GetPushPolicies(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GetPushPoliciesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 构建查询
	query := h.db.Model(&models.PushPolicy{})

	// 应用过滤条件
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.IsDefault != nil {
		query = query.Where("is_default = ?", *req.IsDefault)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取推送策略总数失败: "+err.Error())
		return
	}

	// 应用排序和分页
	var policies []models.PushPolicy
	offset := (req.Page - 1) * req.PageSize
	
	if err := query.Order("created_at desc").Offset(offset).Limit(req.PageSize).Find(&policies).Error; err != nil {
		h.InternalServerError(c, "获取推送策略列表失败: "+err.Error())
		return
	}

	// 为每个策略添加通道信息
	var enrichedPolicies []map[string]interface{}
	for _, policy := range policies {
		// 获取关联的通道信息
		var channelInfos []map[string]interface{}
		if policy.ChannelIDs != "" {
			var channels []models.PushChannel
			channelIDsStr := policy.ChannelIDs
			if err := h.db.Where(fmt.Sprintf("id IN (%s)", channelIDsStr)).Find(&channels).Error; err == nil {
				for _, ch := range channels {
					channelInfos = append(channelInfos, map[string]interface{}{
						"id":   ch.ID,
						"name": ch.Name,
						"type": ch.Type,
					})
				}
			}
		}

		// 创建包含通道信息的策略对象
		enrichedPolicy := map[string]interface{}{
			"id":          policy.ID,
			"name":        policy.Name,
			"description": policy.Description,
			"channelIds":  policy.ChannelIDs,
			"isDefault":   policy.IsDefault,
			"createdAt":   policy.CreatedAt,
			"updatedAt":   policy.UpdatedAt,
			"channelInfo": channelInfos,
		}
		enrichedPolicies = append(enrichedPolicies, enrichedPolicy)
	}

	h.PaginatedSuccess(c, enrichedPolicies, total, req.Page, req.PageSize)
}

// GetPushPolicy 获取单个推送策略
func (h *PushHandler) GetPushPolicy(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var policy models.PushPolicy
	if err := h.db.First(&policy, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "推送策略不存在")
			return
		}
		h.InternalServerError(c, "获取推送策略失败: "+err.Error())
		return
	}

	// 获取关联的通道信息
	var channelInfos []map[string]interface{}
	if policy.ChannelIDs != "" {
		var channels []models.PushChannel
		channelIDsStr := policy.ChannelIDs
		if err := h.db.Where(fmt.Sprintf("id IN (%s)", channelIDsStr)).Find(&channels).Error; err == nil {
			for _, ch := range channels {
				channelInfos = append(channelInfos, map[string]interface{}{
					"id":   ch.ID,
					"name": ch.Name,
					"type": ch.Type,
				})
			}
		}
	}

	// 创建包含通道信息的响应
	response := map[string]interface{}{
		"id":          policy.ID,
		"name":        policy.Name,
		"description": policy.Description,
		"channelIds":  policy.ChannelIDs,
		"isDefault":   policy.IsDefault,
		"createdAt":   policy.CreatedAt,
		"updatedAt":   policy.UpdatedAt,
		"channelInfo": channelInfos,
	}

	h.Success(c, response)
}

// UpdatePushPolicy 更新推送策略
func (h *PushHandler) UpdatePushPolicy(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var req UpdatePushPolicyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 获取现有策略
	var policy models.PushPolicy
	if err := h.db.First(&policy, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "推送策略不存在")
			return
		}
		h.InternalServerError(c, "获取推送策略失败: "+err.Error())
		return
	}

	// 更新字段
	if req.Name != nil {
		policy.Name = *req.Name
	}
	if req.Description != nil {
		policy.Description = *req.Description
	}
	if req.ChannelIDs != nil {
		policy.ChannelIDs = *req.ChannelIDs
	}
	if req.IsDefault != nil {
		// 如果设置为默认策略，需要将其他策略的默认标志设置为false
		if *req.IsDefault && !policy.IsDefault {
			if err := h.db.Model(&models.PushPolicy{}).Where("is_default = ?", true).Update("is_default", false).Error; err != nil {
				h.InternalServerError(c, "重置其他默认策略失败: "+err.Error())
				return
			}
		} else if !*req.IsDefault && policy.IsDefault {
			// 如果取消默认策略，需要确保至少有一个默认策略
			var count int64
			if err := h.db.Model(&models.PushPolicy{}).Where("is_default = ? AND id != ?", true, policy.ID).Count(&count).Error; err != nil {
				h.InternalServerError(c, "检查其他默认策略失败: "+err.Error())
				return
			}
			if count == 0 {
				h.BadRequest(c, "必须保留至少一个默认策略")
				return
			}
		}
		policy.IsDefault = *req.IsDefault
	}

	// 保存更新
	if err := h.db.Save(&policy).Error; err != nil {
		h.InternalServerError(c, "更新推送策略失败: "+err.Error())
		return
	}

	h.SuccessWithMessage(c, "推送策略更新成功", policy)
}
