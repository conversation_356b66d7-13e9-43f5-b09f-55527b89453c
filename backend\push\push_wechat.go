package push

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"time"
)

// 企业微信推送类型常量
const TypeWechatBot = "wechat_bot"

// WechatBotConfig 企业微信机器人配置
type WechatBotConfig struct {
	WebhookURL  string `yaml:"webhook_url" json:"webhook_url"`
	AccessToken string `yaml:"access_token" json:"access_token"`
}

// 企业微信机器人消息结构
type WechatBotMessage struct {
	MsgType  string `json:"msgtype"`
	Markdown struct {
		Content string `json:"content"`
	} `json:"markdown"`
}

// 掩盖URL中的敏感信息
func maskURL(url string) string {
	// 如果URL包含key或token参数，将其值替换为***
	if strings.Contains(url, "key=") {
		re := regexp.MustCompile(`key=([^&]*)`)
		url = re.ReplaceAllString(url, "key=***")
	}
	if strings.Contains(url, "access_token=") {
		re := regexp.MustCompile(`access_token=([^&]*)`)
		url = re.ReplaceAllString(url, "access_token=***")
	}
	return url
}

// 推送到企业微信机器人
func PushToWechatBot(vuln *Vulnerability, channel *PushChannel, record *PushRecord) error {
	// 获取通道配置
	config := channel.GetConfig()
	if config == nil {
		return fmt.Errorf("通道配置为空")
	}
	
	// 检查webhook_url是否存在
	webhookURL, ok := config["webhook_url"].(string)
	if !ok || webhookURL == "" {
		return fmt.Errorf("Webhook URL未配置或格式不正确")
	}
	
	// 构建webhook URL
	if accessToken, ok := config["access_token"].(string); ok && accessToken != "" {
		// 检查URL是否已包含查询参数
		if strings.Contains(webhookURL, "?") {
			webhookURL = fmt.Sprintf("%s&key=%s", webhookURL, accessToken)
		} else {
			webhookURL = fmt.Sprintf("%s?key=%s", webhookURL, accessToken)
		}
	}
	
	// 记录推送URL（不包含敏感信息）
	Infof("正在向企业微信推送消息，URL: %s", maskURL(webhookURL))

	// 构建markdown消息
	severityEmoji := "⚠️ 警告级"
	switch vuln.Severity {
	case "严重":
		severityEmoji = "🚨 严重级"
	case "高危":
		severityEmoji = "🔥 高危级"
	case "中危":
		severityEmoji = "⚠️ 中危级"
	case "低危":
		severityEmoji = "💡 低危级"
	case "信息":
		severityEmoji = "ℹ️ 信息级"
	}

	content := fmt.Sprintf("## %s漏洞告警\n", severityEmoji)
	content += fmt.Sprintf("> **漏洞名称**: %s\n\n", vuln.Name)
	content += fmt.Sprintf("> **漏洞编号**: %s\n\n", vuln.VulnID)
	content += fmt.Sprintf("> **披露日期**: %s\n\n", vuln.DisclosureDate)

	// 安全处理源URL，避免特殊字符问题
	if vuln.Source != "" {
		source := vuln.Source
		// 如果源URL过长，进行截断
		if len([]rune(source)) > 100 {
			runeSource := []rune(source)
			source = string(runeSource[:100]) + "..."
		}
		content += fmt.Sprintf("> **信息来源**: %s\n\n", source)
	}

	if vuln.Description != "" {
		if len(vuln.Description) > 200 {
			// 处理长描述，避免乱码
			desc := []rune(vuln.Description)
			if len(desc) > 200 {
				desc = desc[:200]
			}
			content += fmt.Sprintf("> **漏洞描述**: %s...\n\n", string(desc))
		} else {
			content += fmt.Sprintf("> **漏洞描述**: %s\n\n", vuln.Description)
		}
	}

	if vuln.PushReason != "" {
		// 安全处理推送原因
		reason := vuln.PushReason
		if len([]rune(reason)) > 100 {
			runeReason := []rune(reason)
			reason = string(runeReason[:100]) + "..."
		}
		content += fmt.Sprintf("> **推送原因**: %s\n\n", reason)
	}

	if vuln.Remediation != "" {
		// 安全处理修复建议
		remediation := vuln.Remediation
		if len([]rune(remediation)) > 200 {
			runeRemediation := []rune(remediation)
			remediation = string(runeRemediation[:200]) + "..."
		}
		content += fmt.Sprintf("> **修复建议**: %s\n\n", remediation)
	}

	// 添加参考链接
	references := vuln.GetReferences()
	if len(references) > 0 {
		content += "> **参考链接**:\n"
		// 最多只显示3个参考链接，避免消息过长
		maxLinks := 3
		if len(references) > maxLinks {
			references = references[:maxLinks]
		}

		for _, ref := range references {
			// 处理过长的链接
			linkText := ref
			if len([]rune(linkText)) > 80 {
				runeLink := []rune(linkText)
				linkText = string(runeLink[:80]) + "..."
			}
			content += fmt.Sprintf("> %s\n", linkText)
		}

		// 如果还有更多链接，添加提示
		if len(vuln.GetReferences()) > maxLinks {
			content += "> (更多链接省略...)\n"
		}
	}

	// 添加时间戳
	content += fmt.Sprintf("\n> 推送时间: %s", time.Now().Format("2006-01-02 15:04:05"))

	// 构建请求体
	message := WechatBotMessage{
		MsgType: "markdown",
	}
	message.Markdown.Content = content

	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("构建消息失败: %v", err)
	}

	// 发送请求
	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取完整响应内容
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应内容失败: %v", err)
	}
	
	// 记录完整响应
	Infof("企业微信响应状态码: %d, 响应内容: %s", resp.StatusCode, string(respBody))

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("推送失败，HTTP状态码: %d, 响应内容: %s", resp.StatusCode, string(respBody))
	}

	// 解析响应
	var result struct {
		Errcode int    `json:"errcode"`
		Errmsg  string `json:"errmsg"`
	}

	if err := json.Unmarshal(respBody, &result); err != nil {
		return fmt.Errorf("解析响应失败: %v, 原始响应: %s", err, string(respBody))
	}

	if result.Errcode != 0 {
		return fmt.Errorf("推送失败: 错误码=%d, 错误信息=%s", result.Errcode, result.Errmsg)
	}
	
	Infof("企业微信推送成功: %s", result.Errmsg)
	return nil
}

// 推送IOC情报到企业微信机器人
func PushIOCIntelligenceToWechatBot(ioc *IOCIntelligence, channel *PushChannel, record *IOCIntelligencePushRecord) error {
	// 获取通道配置
	config := channel.GetConfig()
	if config == nil {
		return fmt.Errorf("通道配置为空")
	}

	// 检查webhook_url是否存在
	webhookURL, ok := config["webhook_url"].(string)
	if !ok || webhookURL == "" {
		return fmt.Errorf("Webhook URL未配置或格式不正确")
	}

	// 构建webhook URL
	if accessToken, ok := config["access_token"].(string); ok && accessToken != "" {
		// 检查URL是否已包含查询参数
		if strings.Contains(webhookURL, "?") {
			webhookURL = fmt.Sprintf("%s&key=%s", webhookURL, accessToken)
		} else {
			webhookURL = fmt.Sprintf("%s?key=%s", webhookURL, accessToken)
		}
	}

	// 记录推送URL（不包含敏感信息）
	Infof("正在向企业微信推送IOC情报，URL: %s", maskURL(webhookURL))

	// 构建markdown消息
	riskEmoji := "⚠️ 警告级"
	switch ioc.RiskLevel {
	case "严重":
		riskEmoji = "🚨 严重级"
	case "高危":
		riskEmoji = "🔥 高危级"
	case "中危":
		riskEmoji = "⚠️ 中危级"
	case "低危":
		riskEmoji = "💡 低危级"
	case "信息":
		riskEmoji = "ℹ️ 信息级"
	}

	content := fmt.Sprintf("## %s IOC情报告警\n", riskEmoji)
	content += fmt.Sprintf("> **IOC值**: %s\n\n", ioc.IOC)
	content += fmt.Sprintf("> **IOC类型**: %s\n\n", strings.ToUpper(ioc.IOCType))

	if ioc.Location != "" {
		content += fmt.Sprintf("> **地理位置**: %s\n\n", ioc.Location)
	}

	if ioc.Type != "" {
		content += fmt.Sprintf("> **威胁类型**: %s\n\n", ioc.Type)
	}

	if ioc.HitCount > 0 {
		content += fmt.Sprintf("> **命中次数**: %d\n\n", ioc.HitCount)
	}

	// 移除目标组织字段，已替换为推送状态

	if ioc.Description != "" {
		desc := ioc.Description
		if len([]rune(desc)) > 200 {
			runeDesc := []rune(desc)
			desc = string(runeDesc[:200]) + "..."
		}
		content += fmt.Sprintf("> **威胁描述**: %s\n\n", desc)
	}

	if ioc.Source != "" {
		source := ioc.Source
		if len([]rune(source)) > 100 {
			runeSource := []rune(source)
			source = string(runeSource[:100]) + "..."
		}
		content += fmt.Sprintf("> **情报来源**: %s\n\n", source)
	}

	if len(ioc.Tags) > 0 {
		content += fmt.Sprintf("> **标签**: %s\n\n", strings.Join(ioc.Tags, ", "))
	}

	if ioc.PushReason != "" {
		reason := ioc.PushReason
		if len([]rune(reason)) > 100 {
			runeReason := []rune(reason)
			reason = string(runeReason[:100]) + "..."
		}
		content += fmt.Sprintf("> **推送原因**: %s\n\n", reason)
	}

	// 添加发现日期
	if ioc.DiscoveryDate != "" {
		content += fmt.Sprintf("> **发现日期**: %s\n\n", ioc.DiscoveryDate)
	}

	// 添加时间戳
	content += fmt.Sprintf("\n> 推送时间: %s", time.Now().Format("2006-01-02 15:04:05"))

	// 构建请求体
	message := WechatBotMessage{
		MsgType: "markdown",
	}
	message.Markdown.Content = content

	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("构建消息失败: %v", err)
	}

	// 发送请求
	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取完整响应内容
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应内容失败: %v", err)
	}

	// 记录完整响应
	Infof("企业微信响应状态码: %d, 响应内容: %s", resp.StatusCode, string(respBody))

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("推送失败，HTTP状态码: %d, 响应内容: %s", resp.StatusCode, string(respBody))
	}

	// 解析响应
	var result struct {
		Errcode int    `json:"errcode"`
		Errmsg  string `json:"errmsg"`
	}

	if err := json.Unmarshal(respBody, &result); err != nil {
		return fmt.Errorf("解析响应失败: %v, 原始响应: %s", err, string(respBody))
	}

	if result.Errcode != 0 {
		return fmt.Errorf("推送失败: 错误码=%d, 错误信息=%s", result.Errcode, result.Errmsg)
	}

	Infof("企业微信IOC情报推送成功: %s", result.Errmsg)
	return nil
}

// 批量推送IOC情报到企业微信机器人
func BatchPushIOCIntelligenceToWechatBot(iocIntels []*IOCIntelligence, channel *PushChannel) error {
	// 获取通道配置
	config := channel.GetConfig()
	if config == nil {
		return fmt.Errorf("通道配置为空")
	}

	// 检查webhook_url是否存在
	webhookURL, ok := config["webhook_url"].(string)
	if !ok || webhookURL == "" {
		return fmt.Errorf("Webhook URL未配置或格式不正确")
	}

	// 构建webhook URL
	if accessToken, ok := config["access_token"].(string); ok && accessToken != "" {
		// 检查URL是否已包含查询参数
		if strings.Contains(webhookURL, "?") {
			webhookURL = fmt.Sprintf("%s&key=%s", webhookURL, accessToken)
		} else {
			webhookURL = fmt.Sprintf("%s?key=%s", webhookURL, accessToken)
		}
	}

	// 记录推送URL（不包含敏感信息）
	Infof("正在向企业微信批量推送IOC情报，URL: %s, 数量: %d", maskURL(webhookURL), len(iocIntels))

	// 构建批量推送内容
	content := buildBatchIOCPushContent(iocIntels)

	// 构建请求体
	message := WechatBotMessage{
		MsgType: "markdown",
	}
	message.Markdown.Content = content

	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("构建消息失败: %v", err)
	}

	// 发送请求
	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取完整响应内容
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应内容失败: %v", err)
	}

	// 记录完整响应
	Infof("企业微信响应状态码: %d, 响应内容: %s", resp.StatusCode, string(respBody))

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("推送失败，HTTP状态码: %d, 响应内容: %s", resp.StatusCode, string(respBody))
	}

	// 解析响应
	var result struct {
		Errcode int    `json:"errcode"`
		Errmsg  string `json:"errmsg"`
	}

	if err := json.Unmarshal(respBody, &result); err != nil {
		return fmt.Errorf("解析响应失败: %v, 原始响应: %s", err, string(respBody))
	}

	if result.Errcode != 0 {
		return fmt.Errorf("推送失败: 错误码=%d, 错误信息=%s", result.Errcode, result.Errmsg)
	}

	Infof("企业微信IOC情报批量推送成功: %s", result.Errmsg)
	return nil
}