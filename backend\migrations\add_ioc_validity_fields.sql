-- IOC情报有效期字段迁移脚本
-- 添加有效期相关字段和修改发现时间字段为最后更新字段

-- 为IOC情报表添加有效期相关字段
ALTER TABLE `ioc_intelligence` 
ADD COLUMN `is_valid` BOOLEAN DEFAULT TRUE COMMENT '是否有效',
ADD COLUMN `validity_days` INT DEFAULT 30 COMMENT '有效期天数',
ADD COLUMN `expires_at` BIGINT DEFAULT 0 COMMENT '过期时间戳，0表示永不过期',
ADD COLUMN `last_updated_at` BIGINT DEFAULT 0 COMMENT '最后更新时间（替换原发现时间）';

-- 为新字段添加索引
ALTER TABLE `ioc_intelligence` 
ADD INDEX `idx_is_valid` (`is_valid`),
ADD INDEX `idx_expires_at` (`expires_at`);

-- 初始化现有记录的有效期字段
-- 将现有记录的last_updated_at设置为updated_at的值
UPDATE `ioc_intelligence` 
SET `last_updated_at` = `updated_at` 
WHERE `last_updated_at` = 0;

-- 为现有记录设置默认有效期（30天）
UPDATE `ioc_intelligence` 
SET `expires_at` = UNIX_TIMESTAMP(DATE_ADD(FROM_UNIXTIME(`created_at`), INTERVAL 30 DAY))
WHERE `expires_at` = 0 AND `validity_days` > 0;

-- 为生产策略表添加有效期配置字段
ALTER TABLE `production_strategies` 
ADD COLUMN `default_validity_days` INT DEFAULT 30 COMMENT '默认有效期天数（1-100天）',
ADD COLUMN `enable_validity_control` BOOLEAN DEFAULT TRUE COMMENT '是否启用有效期控制';

-- 更新现有生产策略记录，添加默认有效期配置
UPDATE `production_strategies` 
SET `default_validity_days` = 30, `enable_validity_control` = TRUE 
WHERE `default_validity_days` IS NULL;

-- 创建有效期管理相关的存储过程（可选）
DELIMITER //

-- 清理过期IOC情报的存储过程
CREATE PROCEDURE CleanupExpiredIOC()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE expired_count INT DEFAULT 0;
    
    -- 统计过期的IOC数量
    SELECT COUNT(*) INTO expired_count 
    FROM `ioc_intelligence` 
    WHERE `expires_at` > 0 AND `expires_at` < UNIX_TIMESTAMP(NOW());
    
    -- 标记过期的IOC为无效
    UPDATE `ioc_intelligence` 
    SET `is_valid` = FALSE 
    WHERE `expires_at` > 0 AND `expires_at` < UNIX_TIMESTAMP(NOW()) AND `is_valid` = TRUE;
    
    -- 记录清理结果
    SELECT CONCAT('已标记 ', expired_count, ' 个过期IOC为无效') AS result;
END //

-- 刷新IOC有效期的存储过程
CREATE PROCEDURE RefreshIOCValidity(IN ioc_id INT, IN new_validity_days INT)
BEGIN
    DECLARE current_time BIGINT DEFAULT UNIX_TIMESTAMP(NOW());
    
    -- 更新有效期天数和过期时间
    IF new_validity_days > 0 THEN
        UPDATE `ioc_intelligence` 
        SET `validity_days` = new_validity_days,
            `expires_at` = current_time + (new_validity_days * 24 * 60 * 60),
            `is_valid` = TRUE,
            `last_updated_at` = current_time
        WHERE `id` = ioc_id;
    ELSE
        -- 设置为永不过期
        UPDATE `ioc_intelligence` 
        SET `validity_days` = 0,
            `expires_at` = 0,
            `is_valid` = TRUE,
            `last_updated_at` = current_time
        WHERE `id` = ioc_id;
    END IF;
END //

DELIMITER ;

-- 创建定时清理过期IOC的事件（可选，需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;
-- 
-- CREATE EVENT IF NOT EXISTS cleanup_expired_ioc_event
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO
--   CALL CleanupExpiredIOC();

-- 迁移完成提示
SELECT 'IOC情报有效期字段迁移完成' AS migration_status;
