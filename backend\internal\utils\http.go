package utils

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// HTTPClient HTTP客户端工具
type HTTPClient struct {
	client  *http.Client
	timeout time.Duration
	headers map[string]string
}

// NewHTTPClient 创建HTTP客户端
func NewHTTPClient(timeout time.Duration) *HTTPClient {
	return &HTTPClient{
		client: &http.Client{
			Timeout: timeout,
		},
		timeout: timeout,
		headers: make(map[string]string),
	}
}

// SetHeader 设置请求头
func (hc *HTTPClient) SetHeader(key, value string) *HTTPClient {
	hc.headers[key] = value
	return hc
}

// SetUserAgent 设置User-Agent
func (hc *HTTPClient) SetUserAgent(userAgent string) *HTTPClient {
	return hc.SetHeader("User-Agent", userAgent)
}

// SetContentType 设置Content-Type
func (hc *HTTPClient) SetContentType(contentType string) *HTTPClient {
	return hc.SetHeader("Content-Type", contentType)
}

// Get 发送GET请求
func (hc *HTTPClient) Get(ctx context.Context, url string) (*http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建GET请求失败: %w", err)
	}
	
	// 设置请求头
	for key, value := range hc.headers {
		req.Header.Set(key, value)
	}
	
	return hc.client.Do(req)
}

// Post 发送POST请求
func (hc *HTTPClient) Post(ctx context.Context, url string, data interface{}) (*http.Response, error) {
	var body io.Reader
	
	if data != nil {
		jsonData, err := json.Marshal(data)
		if err != nil {
			return nil, fmt.Errorf("序列化请求数据失败: %w", err)
		}
		body = bytes.NewBuffer(jsonData)
		hc.SetContentType("application/json")
	}
	
	req, err := http.NewRequestWithContext(ctx, "POST", url, body)
	if err != nil {
		return nil, fmt.Errorf("创建POST请求失败: %w", err)
	}
	
	// 设置请求头
	for key, value := range hc.headers {
		req.Header.Set(key, value)
	}
	
	return hc.client.Do(req)
}

// Put 发送PUT请求
func (hc *HTTPClient) Put(ctx context.Context, url string, data interface{}) (*http.Response, error) {
	var body io.Reader
	
	if data != nil {
		jsonData, err := json.Marshal(data)
		if err != nil {
			return nil, fmt.Errorf("序列化请求数据失败: %w", err)
		}
		body = bytes.NewBuffer(jsonData)
		hc.SetContentType("application/json")
	}
	
	req, err := http.NewRequestWithContext(ctx, "PUT", url, body)
	if err != nil {
		return nil, fmt.Errorf("创建PUT请求失败: %w", err)
	}
	
	// 设置请求头
	for key, value := range hc.headers {
		req.Header.Set(key, value)
	}
	
	return hc.client.Do(req)
}

// Delete 发送DELETE请求
func (hc *HTTPClient) Delete(ctx context.Context, url string) (*http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, "DELETE", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建DELETE请求失败: %w", err)
	}
	
	// 设置请求头
	for key, value := range hc.headers {
		req.Header.Set(key, value)
	}
	
	return hc.client.Do(req)
}

// GetJSON 发送GET请求并解析JSON响应
func (hc *HTTPClient) GetJSON(ctx context.Context, url string, result interface{}) error {
	resp, err := hc.Get(ctx, url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP请求失败，状态码: %d", resp.StatusCode)
	}
	
	return json.NewDecoder(resp.Body).Decode(result)
}

// PostJSON 发送POST请求并解析JSON响应
func (hc *HTTPClient) PostJSON(ctx context.Context, url string, data, result interface{}) error {
	resp, err := hc.Post(ctx, url, data)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("HTTP请求失败，状态码: %d", resp.StatusCode)
	}
	
	if result != nil {
		return json.NewDecoder(resp.Body).Decode(result)
	}
	
	return nil
}

// Gin相关工具函数

// GetIDParam 从URL参数获取ID
func GetIDParam(c *gin.Context, paramName string) (uint, error) {
	idStr := c.Param(paramName)
	return ValidateID(idStr)
}

// GetQueryParam 获取查询参数
func GetQueryParam(c *gin.Context, key, defaultValue string) string {
	value := c.Query(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// GetQueryInt 获取整数查询参数
func GetQueryInt(c *gin.Context, key string, defaultValue int) int {
	valueStr := c.Query(key)
	if valueStr == "" {
		return defaultValue
	}
	
	value, err := strconv.Atoi(valueStr)
	if err != nil {
		return defaultValue
	}
	
	return value
}

// GetQueryBool 获取布尔查询参数
func GetQueryBool(c *gin.Context, key string, defaultValue bool) bool {
	valueStr := c.Query(key)
	if valueStr == "" {
		return defaultValue
	}
	
	value, err := strconv.ParseBool(valueStr)
	if err != nil {
		return defaultValue
	}
	
	return value
}

// GetPaginationParams 获取分页参数
func GetPaginationParams(c *gin.Context) (page, pageSize int) {
	page = GetQueryInt(c, "page", 1)
	pageSize = GetQueryInt(c, "pageSize", 20)
	
	// 验证分页参数
	if page < 1 {
		page = 1
	}
	if pageSize < 5 {
		pageSize = 20
	} else if pageSize > 100 {
		pageSize = 100
	}
	
	return page, pageSize
}

// GetSortParams 获取排序参数
func GetSortParams(c *gin.Context, defaultSortBy, defaultSortOrder string) (sortBy, sortOrder string) {
	sortBy = GetQueryParam(c, "sortBy", defaultSortBy)
	sortOrder = GetQueryParam(c, "sortOrder", defaultSortOrder)
	
	// 验证排序方向
	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = defaultSortOrder
	}
	
	return sortBy, sortOrder
}

// GetDateRangeParams 获取日期范围参数
func GetDateRangeParams(c *gin.Context) (startDate, endDate string) {
	startDate = GetQueryParam(c, "startDate", "")
	endDate = GetQueryParam(c, "endDate", "")
	
	// 验证日期格式
	if startDate != "" {
		if _, err := ParseDateString(startDate); err != nil {
			startDate = ""
		}
	}
	
	if endDate != "" {
		if _, err := ParseDateString(endDate); err != nil {
			endDate = ""
		}
	}
	
	return startDate, endDate
}

// BindJSON 绑定JSON数据并验证
func BindJSON(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindJSON(obj); err != nil {
		return fmt.Errorf("JSON数据绑定失败: %w", err)
	}
	
	// 这里可以添加额外的验证逻辑
	return ValidateStruct(obj)
}

// BindQuery 绑定查询参数并验证
func BindQuery(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindQuery(obj); err != nil {
		return fmt.Errorf("查询参数绑定失败: %w", err)
	}
	
	// 这里可以添加额外的验证逻辑
	return ValidateStruct(obj)
}

// GetClientIP 获取客户端IP地址
func GetClientIP(c *gin.Context) string {
	// 尝试从各种头部获取真实IP
	ip := c.GetHeader("X-Forwarded-For")
	if ip != "" {
		// X-Forwarded-For可能包含多个IP，取第一个
		ips := strings.Split(ip, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}
	
	ip = c.GetHeader("X-Real-IP")
	if ip != "" {
		return ip
	}
	
	ip = c.GetHeader("X-Forwarded")
	if ip != "" {
		return ip
	}
	
	ip = c.GetHeader("X-Cluster-Client-IP")
	if ip != "" {
		return ip
	}
	
	// 最后使用RemoteAddr
	return c.ClientIP()
}

// GetUserAgent 获取User-Agent
func GetUserAgent(c *gin.Context) string {
	return c.GetHeader("User-Agent")
}

// IsAjaxRequest 检查是否为AJAX请求
func IsAjaxRequest(c *gin.Context) bool {
	return c.GetHeader("X-Requested-With") == "XMLHttpRequest"
}

// IsJSONRequest 检查是否为JSON请求
func IsJSONRequest(c *gin.Context) bool {
	contentType := c.GetHeader("Content-Type")
	return strings.Contains(contentType, "application/json")
}

// SetNoCacheHeaders 设置不缓存头部
func SetNoCacheHeaders(c *gin.Context) {
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")
}

// SetCacheHeaders 设置缓存头部
func SetCacheHeaders(c *gin.Context, maxAge int) {
	c.Header("Cache-Control", fmt.Sprintf("public, max-age=%d", maxAge))
}

// 响应工具函数

// WriteJSON 写入JSON响应
func WriteJSON(c *gin.Context, statusCode int, data interface{}) {
	c.JSON(statusCode, data)
}

// WriteError 写入错误响应
func WriteError(c *gin.Context, statusCode int, message string) {
	c.JSON(statusCode, gin.H{
		"error":   true,
		"message": message,
	})
}

// WriteSuccess 写入成功响应
func WriteSuccess(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}

// WritePaginatedResponse 写入分页响应
func WritePaginatedResponse(c *gin.Context, data interface{}, total int64, page, pageSize int) {
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
		"pagination": gin.H{
			"total":       total,
			"page":        page,
			"pageSize":    pageSize,
			"totalPages":  totalPages,
			"hasNext":     page < totalPages,
			"hasPrevious": page > 1,
		},
	})
}

// WriteFile 写入文件响应
func WriteFile(c *gin.Context, filename string, data []byte, contentType string) {
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", contentType)
	c.Header("Content-Length", strconv.Itoa(len(data)))
	c.Data(http.StatusOK, contentType, data)
}
