<template>
  <div class="vulnerability-form">
    <el-card shadow="hover" class="form-card">
      <template #header>
        <div class="card-header">
          <h2>{{ isEdit ? '编辑漏洞' : '添加漏洞' }}</h2>
        </div>
      </template>
      
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="right"
        v-loading="loading"
      >
        <el-form-item label="漏洞名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入漏洞名称" />
        </el-form-item>
        
        <el-form-item label="漏洞编号" prop="vulnId">
          <el-input v-model="form.vulnId" placeholder="如: CVE-2023-XXXX" />
        </el-form-item>
        
        <el-form-item label="危害等级" prop="severity">
          <el-select v-model="form.severity" placeholder="请选择危害等级" style="width: 100%">
            <el-option label="严重" value="严重" />
            <el-option label="高危" value="高危" />
            <el-option label="中危" value="中危" />
            <el-option label="低危" value="低危" />
            <el-option label="信息" value="信息" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="标签">
          <el-tag
            v-for="tag in form.tags"
            :key="tag"
            closable
            @close="handleRemoveTag(tag)"
            class="tag-item"
          >
            {{ tag }}
          </el-tag>
          
          <el-input
            v-if="tagInputVisible"
            ref="tagInputRef"
            v-model="tagInputValue"
            class="tag-input"
            size="small"
            @keyup.enter="handleAddTag"
            @blur="handleAddTag"
          />
          
          <el-button v-else class="button-new-tag" size="small" @click="showTagInput">
            + 添加标签
          </el-button>
        </el-form-item>
        
        <el-form-item label="披露日期">
          <el-date-picker
            v-model="form.disclosureDate"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="信息来源">
          <el-input v-model="form.source" placeholder="请输入信息来源" />
        </el-form-item>
        
        <el-form-item label="推送原因">
          <el-input
            v-model="form.pushReason"
            type="textarea"
            :rows="3"
            placeholder="请输入推送原因"
          />
        </el-form-item>
        
        <el-form-item label="漏洞描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="5"
            placeholder="请输入漏洞描述"
          />
        </el-form-item>
        
        <el-form-item label="参考链接">
          <div v-for="(link, index) in form.references" :key="index" class="reference-item">
            <el-input v-model="form.references[index]" placeholder="请输入链接">
              <template #append>
                <el-button @click="removeReference(index)" type="danger" plain>删除</el-button>
              </template>
            </el-input>
          </div>
          
          <el-button type="primary" plain @click="addReference" style="margin-top: 10px">
            添加链接
          </el-button>
        </el-form-item>
        
        <el-form-item label="修复建议">
          <el-input
            v-model="form.remediation"
            type="textarea"
            :rows="4"
            placeholder="请输入修复建议"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            {{ isEdit ? '保存修改' : '提交' }}
          </el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 在表单提交后的推送对话框中添加策略选择 -->
    <el-dialog v-model="pushDialogVisible" title="推送漏洞" width="500px" destroy-on-close>
      <div class="push-dialog-content">
        <p>确定要推送漏洞 <strong>{{ form.name }}</strong> 吗？</p>
        
        <el-form label-width="100px" class="push-form">
          <el-form-item label="推送方式">
            <el-radio-group v-model="pushType">
              <el-radio label="channel">选择通道</el-radio>
              <el-radio label="policy">选择策略</el-radio>
              <el-radio label="default">使用默认策略</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="推送通道" v-if="pushType === 'channel'">
            <el-select v-model="selectedChannelId" style="width: 100%">
              <el-option 
                v-for="channel in channels" 
                :key="channel.id" 
                :label="channel.name" 
                :value="channel.id"
                :disabled="!channel.status"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="推送策略" v-if="pushType === 'policy'">
            <el-select v-model="selectedPolicyId" style="width: 100%">
              <el-option 
                v-for="policy in policies" 
                :key="policy.id" 
                :label="policy.name" 
                :value="policy.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="pushDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmPush" :loading="pushing">确定推送</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import api from './api'
import type { Vulnerability } from './api'

const route = useRoute()
const router = useRouter()
const formRef = ref()
const loading = ref(false)
const submitting = ref(false)

// 标签输入相关
const tagInputRef = ref()
const tagInputVisible = ref(false)
const tagInputValue = ref('')

// 推送相关
const pushDialogVisible = ref(false)
const selectedChannelId = ref<number | undefined>(undefined)
const selectedPolicyId = ref<number | undefined>(undefined)
const pushType = ref('default')
const pushing = ref(false)
const channels = ref<any[]>([])
const policies = ref<any[]>([])

// 是否为编辑模式
const isEdit = computed(() => {
  return route.params.id !== undefined
})

// 表单数据
interface FormData {
  name: string;
  vulnId: string;
  severity: string;
  tags: string[];
  disclosureDate: string;
  source: string;
  pushReason: string;
  description: string;
  references: string[];
  remediation: string;
  [key: string]: any; // 添加索引签名
}

const form = reactive<FormData>({
  name: '',
  vulnId: '',
  severity: '',
  tags: [],
  disclosureDate: '',
  source: '',
  pushReason: '',
  description: '',
  references: [],
  remediation: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入漏洞名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  vulnId: [
    { required: true, message: '请输入漏洞编号', trigger: 'blur' }
  ],
  severity: [
    { required: true, message: '请选择危害等级', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入漏洞描述', trigger: 'blur' }
  ]
}

// 获取漏洞详情
const fetchVulnerabilityDetail = async (id: string) => {
  loading.value = true
  try {
    const res = await api.getVulnerabilityDetail(parseInt(id))
    if (res.data && res.data.data) {
      const vuln = res.data.data
      Object.keys(form).forEach(key => {
        if (key === 'tags' && vuln.tags) {
          form.tags = [...vuln.tags]
        } else if (key === 'references' && vuln.references) {
          form.references = [...vuln.references]
        } else if (vuln[key] !== undefined) {
          form[key] = vuln[key]
        }
      })
    }
  } catch (error) {
    console.error('获取漏洞详情失败', error)
    ElMessage.error('获取漏洞详情失败')
  } finally {
    loading.value = false
  }
}

// 显示标签输入框
const showTagInput = () => {
  tagInputVisible.value = true
  nextTick(() => {
    tagInputRef.value?.focus()
  })
}

// 添加标签
const handleAddTag = () => {
  if (tagInputValue.value) {
    if (!form.tags.includes(tagInputValue.value)) {
      form.tags.push(tagInputValue.value)
    }
  }
  tagInputVisible.value = false
  tagInputValue.value = ''
}

// 移除标签
const handleRemoveTag = (tag: string) => {
  form.tags = form.tags.filter(item => item !== tag)
}

// 添加参考链接
const addReference = () => {
  form.references.push('')
}

// 移除参考链接
const removeReference = (index: number) => {
  form.references.splice(index, 1)
}

// 获取推送通道
const fetchChannels = async () => {
  try {
    const res = await api.getPushChannels()
    channels.value = res.data.data || []
  } catch (error) {
    console.error('获取推送通道失败', error)
  }
}

// 获取推送策略
const fetchPolicies = async () => {
  try {
    const res = await api.getPushPolicies()
    policies.value = res.data.data || []
  } catch (error) {
    console.error('获取推送策略失败', error)
  }
}

// 显示推送对话框
const showPushDialog = () => {
  // 重置推送表单
  pushType.value = 'default'
  selectedChannelId.value = undefined
  selectedPolicyId.value = undefined
  
  // 获取通道和策略
  fetchChannels()
  fetchPolicies()
  
  // 显示对话框
  pushDialogVisible.value = true
}

// 确认推送
const confirmPush = async () => {
  if (pushType.value === 'channel' && !selectedChannelId.value) {
    ElMessage.warning('请选择推送通道')
    return
  }
  
  if (pushType.value === 'policy' && !selectedPolicyId.value) {
    ElMessage.warning('请选择推送策略')
    return
  }
  
  pushing.value = true
  try {
    let res
    
    if (pushType.value === 'channel') {
      res = await api.pushVulnerability(form.id, selectedChannelId.value)
    } else if (pushType.value === 'policy') {
      res = await api.pushVulnerability(form.id, undefined, selectedPolicyId.value)
    } else {
      res = await api.pushVulnerability(form.id)
    }
    
    ElMessage.success('推送成功')
    pushDialogVisible.value = false
  } catch (error: any) {
    console.error('推送失败', error)
    ElMessage.error('推送失败: ' + (error.response?.data?.msg || '未知错误'))
  } finally {
    pushing.value = false
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      submitting.value = true
      try {
        // 过滤空的参考链接
        const filteredReferences = form.references.filter(ref => ref.trim() !== '')
        
        if (isEdit.value) {
          // 编辑模式
          await api.updateVulnerability(parseInt(route.params.id as string), {
            name: form.name,
            vulnId: form.vulnId,
            severity: form.severity,
            tags: form.tags,
            disclosureDate: form.disclosureDate,
            source: form.source,
            pushReason: form.pushReason,
            description: form.description,
            references: filteredReferences,
            remediation: form.remediation
          })
          ElMessage.success('漏洞更新成功')
        } else {
          // 创建模式
          await api.createVulnerability({
            name: form.name,
            vulnId: form.vulnId,
            severity: form.severity,
            tags: form.tags,
            disclosureDate: form.disclosureDate,
            source: form.source,
            pushReason: form.pushReason,
            description: form.description,
            references: filteredReferences,
            remediation: form.remediation
          })
          ElMessage.success('漏洞创建成功')
        }
        
        // 返回漏洞列表页
        router.push('/vulnerabilities')
      } catch (error) {
        console.error(isEdit.value ? '更新漏洞失败' : '创建漏洞失败', error)
      } finally {
        submitting.value = false
      }
    }
  })
}

// 返回上一页
const goBack = () => {
  router.back()
}

onMounted(() => {
  // 如果是编辑模式，获取漏洞详情
  if (isEdit.value && route.params.id) {
    fetchVulnerabilityDetail(route.params.id as string)
  }
  
  // 初始化一个空的参考链接
  if (form.references.length === 0) {
    form.references.push('')
  }
  
  // 获取推送通道和策略
  fetchChannels()
  fetchPolicies()
})
</script>

<style scoped>
.vulnerability-form {
  padding: 0;
}

/* 自定义严重级别的标签样式 */
:deep(.el-tag--error) {
  background-color: #8b0000;
  border-color: #8b0000;
}

/* 自定义高危级别的标签样式 */
:deep(.el-tag--danger) {
  background-color: #ff4949;
  border-color: #ff4949;
}

.form-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tag-item {
  margin-right: 8px;
  margin-bottom: 8px;
}

.tag-input {
  width: 100px;
  margin-right: 8px;
  vertical-align: top;
}

.button-new-tag {
  margin-bottom: 8px;
}

.reference-item {
  margin-bottom: 10px;
}

.push-dialog-content {
  padding: 10px;
}

.push-form {
  margin-top: 20px;
}
</style> 