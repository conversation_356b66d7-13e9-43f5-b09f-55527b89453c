{"name": "vulnerability-push", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --skipLibCheck && vite build", "preview": "vite preview"}, "devDependencies": {"@types/node": "^20.10.5", "@vitejs/plugin-vue": "^4.5.2", "terser": "^5.43.1", "typescript": "^5.3.3", "vite": "^5.0.10"}, "dependencies": {"axios": "^1.6.2", "echarts": "^5.4.3", "element-plus": "^2.4.3", "vue": "^3.3.11", "vue-router": "^4.2.5"}}