package service

import (
	"encoding/json"
	"fmt"

	"gorm.io/gorm"
	"vulnerability_push/internal/models"
)

// ThreatIntelligenceInitializer 威胁情报接口初始化器
type ThreatIntelligenceInitializer struct {
	db *gorm.DB
}

// NewThreatIntelligenceInitializer 创建威胁情报接口初始化器
func NewThreatIntelligenceInitializer(db *gorm.DB) *ThreatIntelligenceInitializer {
	return &ThreatIntelligenceInitializer{
		db: db,
	}
}

// InitializeDefaultInterfaces 初始化默认的威胁情报接口
func (init *ThreatIntelligenceInitializer) InitializeDefaultInterfaces() error {
	// 检查是否已经存在威胁情报接口
	var count int64
	if err := init.db.Model(&models.ThreatIntelligenceInterface{}).Count(&count).Error; err != nil {
		return fmt.Errorf("检查威胁情报接口数量失败: %v", err)
	}

	// 如果已经存在接口，则不进行初始化
	if count > 0 {
		fmt.Printf("威胁情报接口已存在 %d 个，跳过初始化\n", count)
		return nil
	}

	fmt.Println("开始初始化默认威胁情报接口...")

	// 初始化天际友盟接口
	if err := init.initializeTJUNInterface(); err != nil {
		return fmt.Errorf("初始化天际友盟接口失败: %v", err)
	}

	// 初始化微步威胁情报接口
	if err := init.initializeWeibuInterface(); err != nil {
		return fmt.Errorf("初始化微步威胁情报接口失败: %v", err)
	}

	fmt.Println("默认威胁情报接口初始化完成")
	return nil
}

// initializeTJUNInterface 初始化天际友盟接口
func (init *ThreatIntelligenceInitializer) initializeTJUNInterface() error {
	// 创建默认的天际友盟配置（空配置，等待用户填写）
	tjunConfig := models.TJUNConfig{
		Host:      "api.tj-un.com",
		AppKey:    "",
		AppSecret: "",
		Token:     "",
		Timeout:   10,
	}

	configBytes, err := json.Marshal(tjunConfig)
	if err != nil {
		return fmt.Errorf("序列化天际友盟配置失败: %v", err)
	}

	tjunInterface := models.ThreatIntelligenceInterface{
		Name:        "天际友盟威胁情报",
		Type:        "tjun",
		Description: "天际友盟威胁情报接口，提供IOC威胁情报查询服务。需要配置APP KEY、APP密钥和用户Token。",
		Config:      string(configBytes),
		Status:      "disabled", // 默认禁用，等待用户配置后启用
	}

	if err := init.db.Create(&tjunInterface).Error; err != nil {
		return fmt.Errorf("创建天际友盟接口记录失败: %v", err)
	}

	fmt.Printf("✓ 已创建天际友盟威胁情报接口 (ID: %d)\n", tjunInterface.ID)
	return nil
}

// initializeWeibuInterface 初始化微步威胁情报接口
func (init *ThreatIntelligenceInitializer) initializeWeibuInterface() error {
	// 创建默认的微步威胁情报配置（空配置，等待用户填写）
	weibuConfig := models.WeibuConfig{
		Host:    "api.threatbook.cn",
		APIKey:  "",
		Timeout: 10,
	}

	configBytes, err := json.Marshal(weibuConfig)
	if err != nil {
		return fmt.Errorf("序列化微步威胁情报配置失败: %v", err)
	}

	weibuInterface := models.ThreatIntelligenceInterface{
		Name:        "微步威胁情报",
		Type:        "weibu",
		Description: "微步威胁情报接口，提供IP威胁情报查询服务。需要配置API密钥。",
		Config:      string(configBytes),
		Status:      "disabled", // 默认禁用，等待用户配置后启用
	}

	if err := init.db.Create(&weibuInterface).Error; err != nil {
		return fmt.Errorf("创建微步威胁情报接口记录失败: %v", err)
	}

	fmt.Printf("✓ 已创建微步威胁情报接口 (ID: %d)\n", weibuInterface.ID)
	return nil
}

// ForceReinitializeInterfaces 强制重新初始化接口（用于开发和测试）
func (init *ThreatIntelligenceInitializer) ForceReinitializeInterfaces() error {
	fmt.Println("强制重新初始化威胁情报接口...")

	// 删除所有现有接口
	if err := init.db.Where("1 = 1").Delete(&models.ThreatIntelligenceInterface{}).Error; err != nil {
		return fmt.Errorf("删除现有威胁情报接口失败: %v", err)
	}

	// 重新初始化
	return init.InitializeDefaultInterfaces()
}

// GetInterfaceStatus 获取威胁情报接口状态
func (init *ThreatIntelligenceInitializer) GetInterfaceStatus() (map[string]interface{}, error) {
	var interfaces []models.ThreatIntelligenceInterface
	if err := init.db.Find(&interfaces).Error; err != nil {
		return nil, fmt.Errorf("获取威胁情报接口失败: %v", err)
	}

	status := map[string]interface{}{
		"total":      len(interfaces),
		"enabled":    0,
		"disabled":   0,
		"interfaces": make([]map[string]interface{}, 0),
	}

	for _, iface := range interfaces {
		if iface.Status == "enabled" {
			status["enabled"] = status["enabled"].(int) + 1
		} else {
			status["disabled"] = status["disabled"].(int) + 1
		}

		// 检查配置是否完整
		configComplete := false
		if iface.Type == "tjun" {
			if config, err := iface.GetTJUNConfig(); err == nil {
				configComplete = config.AppKey != "" && config.AppSecret != "" && config.Token != ""
			}
		} else if iface.Type == "weibu" {
			if config, err := iface.GetWeibuConfig(); err == nil {
				configComplete = config.APIKey != ""
			}
		}

		interfaceInfo := map[string]interface{}{
			"id":              iface.ID,
			"name":            iface.Name,
			"type":            iface.Type,
			"status":          iface.Status,
			"config_complete": configComplete,
			"created_at":      iface.CreatedAt,
		}

		status["interfaces"] = append(status["interfaces"].([]map[string]interface{}), interfaceInfo)
	}

	return status, nil
}

// ValidateInterfaceConfig 验证威胁情报接口配置
func (init *ThreatIntelligenceInitializer) ValidateInterfaceConfig(interfaceID uint) (bool, string, error) {
	var iface models.ThreatIntelligenceInterface
	if err := init.db.First(&iface, interfaceID).Error; err != nil {
		return false, "", fmt.Errorf("获取威胁情报接口失败: %v", err)
	}

	switch iface.Type {
	case "tjun":
		config, err := iface.GetTJUNConfig()
		if err != nil {
			return false, "配置解析失败", err
		}
		if config.AppKey == "" {
			return false, "APP KEY未配置", nil
		}
		if config.AppSecret == "" {
			return false, "APP密钥未配置", nil
		}
		if config.Token == "" {
			return false, "用户Token未配置", nil
		}
		return true, "配置完整", nil

	case "weibu":
		config, err := iface.GetWeibuConfig()
		if err != nil {
			return false, "配置解析失败", err
		}
		if config.APIKey == "" {
			return false, "API密钥未配置", nil
		}
		return true, "配置完整", nil

	default:
		return false, "不支持的接口类型", nil
	}
}

// UpdateInterfaceConfigStatus 更新接口配置状态（在配置更新后调用）
func (init *ThreatIntelligenceInitializer) UpdateInterfaceConfigStatus(interfaceID uint) error {
	isValid, message, err := init.ValidateInterfaceConfig(interfaceID)
	if err != nil {
		return err
	}

	var iface models.ThreatIntelligenceInterface
	if err := init.db.First(&iface, interfaceID).Error; err != nil {
		return err
	}

	// 如果配置完整且当前是禁用状态，可以考虑自动启用（可选）
	if isValid && iface.Status == "disabled" {
		fmt.Printf("威胁情报接口 %s 配置完整，可以启用\n", iface.Name)
		// 这里不自动启用，让用户手动启用
	} else if !isValid && iface.Status == "enabled" {
		fmt.Printf("威胁情报接口 %s 配置不完整: %s，建议禁用\n", iface.Name, message)
		// 这里不自动禁用，让用户手动处理
	}

	return nil
}
