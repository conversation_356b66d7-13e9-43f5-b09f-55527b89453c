package handlers

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/internal/models"
	"vulnerability_push/push"
)

// GetPushChannelsRequest 获取推送通道请求
type GetPushChannelsRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1"`
	PageSize int    `form:"pageSize" binding:"omitempty,min=5,max=100"`
	Name     string `form:"name"`
	Type     string `form:"type"`
	Status   *bool  `form:"status"`
}

// CreatePushChannelRequest 创建推送通道请求
type CreatePushChannelRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Type        string                 `json:"type" binding:"required"`
	Description string                 `json:"description"`
	Config      map[string]interface{} `json:"config"`
	Status      bool                   `json:"status"`
}

// UpdatePushChannelRequest 更新推送通道请求
type UpdatePushChannelRequest struct {
	Name        *string                 `json:"name"`
	Type        *string                 `json:"type"`
	Description *string                 `json:"description"`
	Config      *map[string]interface{} `json:"config"`
	Status      *bool                   `json:"status"`
}

// GetPushChannels 获取推送通道列表
func (h *PushHandler) GetPushChannels(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GetPushChannelsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 构建查询
	query := h.db.Model(&models.PushChannel{})

	// 应用过滤条件
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取推送通道总数失败: "+err.Error())
		return
	}

	// 应用排序和分页
	var channels []models.PushChannel
	offset := (req.Page - 1) * req.PageSize
	
	if err := query.Order("created_at desc").Offset(offset).Limit(req.PageSize).Find(&channels).Error; err != nil {
		h.InternalServerError(c, "获取推送通道列表失败: "+err.Error())
		return
	}

	h.PaginatedSuccess(c, channels, total, req.Page, req.PageSize)
}

// GetPushChannel 获取单个推送通道
func (h *PushHandler) GetPushChannel(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var channel models.PushChannel
	if err := h.db.First(&channel, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "推送通道不存在")
			return
		}
		h.InternalServerError(c, "获取推送通道失败: "+err.Error())
		return
	}

	// 创建包含配置信息的响应
	response := map[string]interface{}{
		"id":          channel.ID,
		"name":        channel.Name,
		"type":        channel.Type,
		"description": channel.Description,
		"status":      channel.Status,
		"createdAt":   channel.CreatedAt,
		"updatedAt":   channel.UpdatedAt,
		"config":      channel.GetConfig(),
	}

	h.Success(c, response)
}

// CreatePushChannel 创建推送通道
func (h *PushHandler) CreatePushChannel(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req CreatePushChannelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 序列化配置
	configJSON, err := json.Marshal(req.Config)
	if err != nil {
		h.ValidationError(c, fmt.Errorf("配置格式错误: %w", err))
		return
	}

	// 创建推送通道
	channel := models.PushChannel{
		Name:        req.Name,
		Type:        req.Type,
		Description: req.Description,
		Config:      string(configJSON),
		Status:      req.Status,
	}

	if err := h.db.Create(&channel).Error; err != nil {
		h.InternalServerError(c, "创建推送通道失败: "+err.Error())
		return
	}

	h.Success(c, channel)
}

// UpdatePushChannel 更新推送通道
func (h *PushHandler) UpdatePushChannel(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var req UpdatePushChannelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 获取现有通道
	var channel models.PushChannel
	if err := h.db.First(&channel, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "推送通道不存在")
			return
		}
		h.InternalServerError(c, "获取推送通道失败: "+err.Error())
		return
	}

	// 更新字段
	if req.Name != nil {
		channel.Name = *req.Name
	}
	if req.Type != nil {
		channel.Type = *req.Type
	}
	if req.Description != nil {
		channel.Description = *req.Description
	}
	if req.Config != nil {
		configJSON, err := json.Marshal(*req.Config)
		if err != nil {
			h.ValidationError(c, fmt.Errorf("配置格式错误: %w", err))
			return
		}
		channel.Config = string(configJSON)
	}
	if req.Status != nil {
		channel.Status = *req.Status
	}

	// 保存更新
	if err := h.db.Save(&channel).Error; err != nil {
		h.InternalServerError(c, "更新推送通道失败: "+err.Error())
		return
	}

	h.Success(c, gin.H{"message": "推送通道更新成功"})
}

// DeletePushChannel 删除推送通道
func (h *PushHandler) DeletePushChannel(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	// 检查通道是否存在
	var channel models.PushChannel
	if err := h.db.First(&channel, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "推送通道不存在")
			return
		}
		h.InternalServerError(c, "获取推送通道失败: "+err.Error())
		return
	}

	// 删除通道
	if err := h.db.Delete(&channel).Error; err != nil {
		h.InternalServerError(c, "删除推送通道失败: "+err.Error())
		return
	}

	h.Success(c, gin.H{"message": "推送通道删除成功"})
}

// TestPushChannel 测试推送通道
func (h *PushHandler) TestPushChannel(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	// 获取通道信息
	var channel models.PushChannel
	if err := h.db.First(&channel, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "推送通道不存在")
			return
		}
		h.InternalServerError(c, "获取推送通道失败: "+err.Error())
		return
	}

	// 检查通道是否启用
	if !channel.Status {
		h.BadRequest(c, "推送通道已禁用")
		return
	}

	// 创建测试漏洞数据
	testVuln := &push.Vulnerability{
		ID:          0,
		Name:        "推送通道测试",
		Description: fmt.Sprintf("这是一条来自 %s 通道的测试消息，发送时间：%s", channel.Name, time.Now().Format("2006-01-02 15:04:05")),
		Severity:    "info",
		Tags:        "test",
		VulnID:      "TEST-001",
		CreatedAt:   time.Now().Unix(),
	}

	// 转换为push包的通道类型
	pushChannel := &push.PushChannel{
		ID:          channel.ID,
		Name:        channel.Name,
		Type:        channel.Type,
		Description: channel.Description,
		Config:      channel.Config,
		Status:      channel.Status,
		CreatedAt:   channel.CreatedAt,
		UpdatedAt:   channel.UpdatedAt,
	}

	// 创建测试推送记录
	pushRecord := &push.PushRecord{
		ID:              0, // 测试记录，不保存到数据库
		VulnerabilityID: 0,
		ChannelID:       channel.ID,
		Status:          "pending",
		ErrorMessage:    "",
		PushedAt:        time.Now().Unix(),
	}

	// 根据通道类型执行推送测试
	var pushErr error
	switch channel.Type {
	case push.TypeWechatBot:
		pushErr = push.PushToWechatBot(testVuln, pushChannel, pushRecord)
	case push.TypeDingDing:
		pushErr = push.PushToDingDing(testVuln, pushChannel, pushRecord)
	case push.TypeWebhook:
		pushErr = push.PushToWebhook(testVuln, pushChannel, pushRecord)
	case push.TypeLark:
		pushErr = push.PushToLark(testVuln, pushChannel, pushRecord)
	default:
		pushErr = fmt.Errorf("不支持的推送通道类型: %s", channel.Type)
	}

	// 返回测试结果
	if pushErr != nil {
		h.InternalServerError(c, "测试失败: "+pushErr.Error())
		return
	}

	h.SuccessWithMessage(c, "测试成功，消息已推送", nil)
}
