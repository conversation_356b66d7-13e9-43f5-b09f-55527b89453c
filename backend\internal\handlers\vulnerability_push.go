package handlers

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"vulnerability_push/internal/models"
	"vulnerability_push/internal/utils"
	"vulnerability_push/push"
)

// autoTriggerPush 自动触发推送
func (h *VulnerabilityHandler) autoTriggerPush(vuln *models.Vulnerability) {
	utils.Infof("=== 开始自动推送检查 ===")
	utils.Infof("漏洞ID: %d", vuln.ID)
	utils.Infof("漏洞名称: %s", vuln.Name)
	utils.Infof("漏洞标签: %s", vuln.Tags)
	utils.Infof("漏洞描述: %s", vuln.Description)

	if err := h.performAutoTriggerPush(vuln); err != nil {
		// 仅记录错误，不影响主流程
		utils.Errorf("❌ 自动推送漏洞失败: %v", err)
	} else {
		utils.Infof("✅ 自动推送漏洞成功: ID=%d, 名称=%s", vuln.ID, vuln.Name)
	}
	utils.Infof("=== 自动推送检查结束 ===")
}

// performAutoTriggerPush 执行自动推送检查
func (h *VulnerabilityHandler) performAutoTriggerPush(vuln *models.Vulnerability) error {
	utils.Infof("🔍 开始自动推送检查流程...")

	// 获取白名单配置
	var whitelist models.PushWhitelist
	if err := h.db.First(&whitelist).Error; err != nil {
		utils.Errorf("❌ 获取推送白名单失败: %v", err)
		return fmt.Errorf("获取推送白名单失败: %v", err)
	}

	utils.Infof("📋 白名单配置:")
	utils.Infof("  - 关键词: %s", whitelist.Keywords)
	utils.Infof("  - 自动推送: %v", whitelist.AutoPush)
	utils.Infof("  - 策略ID: %d", whitelist.PolicyID)

	// 检查是否启用自动推送
	if !whitelist.AutoPush {
		utils.Warnf("⚠️ 自动推送功能未启用")
		return fmt.Errorf("自动推送功能未启用")
	}

	// 检查是否设置了推送策略
	if whitelist.PolicyID == 0 {
		utils.Warnf("⚠️ 未设置自动推送策略")
		return fmt.Errorf("未设置自动推送策略")
	}

	// 获取推送策略
	var policy models.PushPolicy
	if err := h.db.First(&policy, whitelist.PolicyID).Error; err != nil {
		utils.Errorf("❌ 获取推送策略失败: %v", err)
		return fmt.Errorf("获取推送策略失败: %v", err)
	}

	utils.Infof("📝 推送策略:")
	utils.Infof("  - 策略名称: %s", policy.Name)
	utils.Infof("  - 通道IDs: %s", policy.ChannelIDs)

	// 检查漏洞是否匹配白名单关键词
	keywords := strings.Split(whitelist.Keywords, ",")
	matched := false
	matchedKeyword := ""
	matchedLocation := ""
	
	// 预处理漏洞信息，去除多余空格和换行符
	cleanTitle := h.normalizeText(vuln.Name)
	cleanDesc := h.normalizeText(vuln.Description)
	cleanVulnID := h.normalizeText(vuln.VulnID)
	
	// 处理标签
	var tags []string
	if vuln.Tags != "" {
		tags = h.splitString(vuln.Tags)
		// 清理每个标签
		for i, tag := range tags {
			tags[i] = h.normalizeText(tag)
		}
	}
	
	// 记录到日志文件，便于调试
	utils.Infof("🎯 开始关键词匹配检查:")
	utils.Debugf("  - 漏洞ID: %d", vuln.ID)
	utils.Debugf("  - 漏洞名称: %s", vuln.Name)
	utils.Debugf("  - 漏洞标签: %v", tags)
	utils.Debugf("  - 漏洞描述: %s", vuln.Description)
	utils.Debugf("  - 漏洞编号: %s", vuln.VulnID)
	utils.Debugf("  - 白名单关键词: %v", keywords)

	// 1. 首先检查标题中是否包含关键词
	utils.Infof("🔍 检查标题匹配...")
	for _, keyword := range keywords {
		keyword = strings.TrimSpace(keyword)
		if keyword == "" {
			continue
		}

		cleanKeyword := h.normalizeText(keyword)
		utils.Debugf("  检查关键词 '%s' 是否在标题 '%s' 中", cleanKeyword, cleanTitle)

		// 检查标题中是否包含关键词
		if strings.Contains(strings.ToLower(cleanTitle), strings.ToLower(cleanKeyword)) {
			matched = true
			matchedKeyword = keyword
			matchedLocation = "标题"
			utils.Infof("✅ 漏洞标题匹配到关键词: %s", keyword)
			break
		}
	}
	
	// 2. 如果标题未匹配，检查标签
	if !matched {
		utils.Infof("🔍 检查标签匹配...")
		for _, keyword := range keywords {
			keyword = strings.TrimSpace(keyword)
			if keyword == "" {
				continue
			}
			
			cleanKeyword := h.normalizeText(keyword)
			utils.Debugf("  检查关键词 '%s' 是否在标签 %v 中", cleanKeyword, tags)
			
			// 检查标签中是否包含关键词
			for _, tag := range tags {
				if strings.EqualFold(tag, cleanKeyword) {
					matched = true
					matchedKeyword = keyword
					matchedLocation = "标签"
					utils.Infof("✅ 漏洞标签匹配到关键词: %s", keyword)
					break
				}
			}
			
			if matched {
				break
			}
		}
	}
	
	// 3. 如果标签未匹配，检查描述
	if !matched {
		utils.Infof("🔍 检查描述匹配...")
		for _, keyword := range keywords {
			keyword = strings.TrimSpace(keyword)
			if keyword == "" {
				continue
			}
			
			cleanKeyword := h.normalizeText(keyword)
			
			// 检查描述中是否包含关键词
			if strings.Contains(strings.ToLower(cleanDesc), strings.ToLower(cleanKeyword)) {
				matched = true
				matchedKeyword = keyword
				matchedLocation = "描述"
				utils.Infof("✅ 漏洞描述匹配到关键词: %s", keyword)
				break
			}
		}
	}
	
	// 4. 检查漏洞ID（如CVE编号）
	if !matched {
		utils.Infof("🔍 检查漏洞ID匹配...")
		for _, keyword := range keywords {
			keyword = strings.TrimSpace(keyword)
			if keyword == "" {
				continue
			}
			
			cleanKeyword := h.normalizeText(keyword)
			
			// 检查漏洞ID中是否包含关键词
			if strings.Contains(strings.ToLower(cleanVulnID), strings.ToLower(cleanKeyword)) {
				matched = true
				matchedKeyword = keyword
				matchedLocation = "漏洞ID"
				utils.Infof("✅ 漏洞ID匹配到关键词: %s", keyword)
				break
			}
		}
	}
	
	// 特殊处理：检查是否有CVE编号
	if !matched && strings.Contains(strings.ToLower(whitelist.Keywords), "cve") {
		utils.Infof("🔍 检查CVE编号匹配...")
		// 提取漏洞中的CVE编号
		cvePattern := regexp.MustCompile(`(?i)CVE-\d{4}-\d+`)
		searchText := vuln.Name + " " + vuln.Description + " " + vuln.VulnID + " " + vuln.Tags
		utils.Debugf("  在文本中搜索CVE编号: %s", searchText)
		cveMatches := cvePattern.FindAllString(searchText, -1)
		
		if len(cveMatches) > 0 {
			matched = true
			matchedKeyword = "CVE"
			matchedLocation = "CVE编号"
			utils.Infof("✅ 漏洞包含CVE编号: %v", cveMatches)
		} else {
			utils.Debugf("  未找到CVE编号")
		}
	}
	
	if !matched {
		utils.Warnf("❌ 漏洞不匹配任何白名单关键词")
		utils.Debugf("  - 漏洞ID: %d", vuln.ID)
		utils.Debugf("  - 漏洞名称: %s", vuln.Name)
		utils.Debugf("  - 漏洞标签: %s", vuln.Tags)
		utils.Debugf("  - 白名单关键词: %s", strings.Join(keywords, ", "))
		return fmt.Errorf("漏洞不匹配任何白名单关键词")
	}

	// 记录匹配成功
	utils.Infof("🎉 漏洞匹配白名单关键词成功!")
	utils.Infof("  - 漏洞ID: %d", vuln.ID)
	utils.Infof("  - 漏洞名称: %s", vuln.Name)
	utils.Infof("  - 匹配关键词: %s", matchedKeyword)
	utils.Infof("  - 匹配位置: %s", matchedLocation)

	// 检查漏洞内容是否包含敏感词
	utils.Infof("🔍 检查敏感词...")
	contentToCheck := vuln.Name + " " + vuln.Description + " " + vuln.Remediation + " " + vuln.PushReason
	hasSensitiveWords, foundWords := h.containsSensitiveWords(contentToCheck)
	if hasSensitiveWords {
		utils.Errorf("❌ 漏洞内容包含敏感词: %v", foundWords)
		return fmt.Errorf("漏洞内容包含敏感词: %v", foundWords)
	}

	utils.Infof("✅ 漏洞内容不含敏感词，准备推送")
	
	// 执行推送
	return h.executePush(vuln, &policy)
}

// normalizeText 标准化文本，去除多余空格和换行符
func (h *VulnerabilityHandler) normalizeText(text string) string {
	// 去除前后空格
	text = strings.TrimSpace(text)
	// 将多个连续空格替换为单个空格
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")
	return text
}

// splitString 分割字符串
func (h *VulnerabilityHandler) splitString(str string) []string {
	if str == "" {
		return []string{}
	}
	
	parts := strings.Split(str, ",")
	var result []string
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part != "" {
			result = append(result, part)
		}
	}
	return result
}

// containsSensitiveWords 检查内容是否包含敏感词
func (h *VulnerabilityHandler) containsSensitiveWords(content string) (bool, []string) {
	// 使用push包中的敏感词检查功能
	return push.ContainsSensitiveWords(content)
}

// executePush 执行推送
func (h *VulnerabilityHandler) executePush(vuln *models.Vulnerability, policy *models.PushPolicy) error {
	// 转换为push包类型
	pushVuln := &push.Vulnerability{
		ID:             vuln.ID,
		Name:           vuln.Name,
		VulnID:         vuln.VulnID,
		Severity:       vuln.Severity,
		DisclosureDate: vuln.DisclosureDate,
		Source:         vuln.Source,
		Description:    vuln.Description,
		Remediation:    vuln.Remediation,
		References:     vuln.References,
		Tags:           vuln.Tags,
		PushReason:     vuln.PushReason,
	}

	// 获取策略关联的通道
	channelIDs := strings.Split(policy.ChannelIDs, ",")
	utils.Infof("📡 检查推送通道...")
	utils.Debugf("  - 通道IDs: %v", channelIDs)

	if len(channelIDs) == 0 || (len(channelIDs) == 1 && channelIDs[0] == "") {
		utils.Infof("❌ 推送策略未关联任何通道")
		return fmt.Errorf("推送策略未关联任何通道")
	}

	// 推送到每个通道
	var pushErrors []string
	successCount := 0
	utils.Infof("🚀 开始推送到 %d 个通道...", len(channelIDs))

	for i, channelIDStr := range channelIDs {
		channelIDStr = strings.TrimSpace(channelIDStr)
		if channelIDStr == "" {
			continue
		}

		utils.Infof("📡 处理通道 %d/%d (ID: %s)", i+1, len(channelIDs), channelIDStr)

		// 获取通道信息
		var channel models.PushChannel
		if err := h.db.First(&channel, channelIDStr).Error; err != nil {
			utils.Infof("❌ 通道%s不存在: %v", channelIDStr, err)
			pushErrors = append(pushErrors, fmt.Sprintf("通道%s不存在: %v", channelIDStr, err))
			continue
		}

		utils.Debugf("  - 通道名称: %s", channel.Name)
		utils.Debugf("  - 通道类型: %s", channel.Type)
		utils.Debugf("  - 通道状态: %v", channel.IsEnabled)

		// 检查通道是否启用
		if !channel.IsEnabled {
			utils.Infof("⚠️ 通道%s已禁用", channel.Name)
			pushErrors = append(pushErrors, fmt.Sprintf("通道%s已禁用", channel.Name))
			continue
		}

		// 创建推送记录
		record := &models.PushRecord{
			VulnerabilityID: vuln.ID,
			ChannelID:       channel.ID,
			PolicyID:        policy.ID,
			Status:          "pending",
			CreatedAt:       time.Now().Unix(),
		}

		if err := h.db.Create(record).Error; err != nil {
			pushErrors = append(pushErrors, fmt.Sprintf("创建推送记录失败: %v", err))
			continue
		}

		// 转换通道类型
		pushChannel := &push.PushChannel{
			ID:       channel.ID,
			Name:     channel.Name,
			Type:     channel.Type,
			Config:   channel.Config,
			IsEnabled: channel.IsEnabled,
		}

		// 转换为push包的PushRecord类型
		pushRecord := &push.PushRecord{
			ID:              record.ID,
			VulnerabilityID: record.VulnerabilityID,
			ChannelID:       record.ChannelID,
			PolicyID:        record.PolicyID,
			Status:          record.Status,
			ErrorMessage:    record.ErrorMessage,
			CreatedAt:       record.CreatedAt,
			PushedAt:        record.PushedAt,
		}

		// 执行推送
		utils.Infof("🚀 执行推送到通道: %s (%s)", channel.Name, channel.Type)
		var pushErr error
		switch channel.Type {
		case "dingtalk", "dingding":
			pushErr = push.PushToDingDing(pushVuln, pushChannel, pushRecord)
		case "lark":
			pushErr = push.PushToLark(pushVuln, pushChannel, pushRecord)
		case "webhook":
			pushErr = push.PushToWebhook(pushVuln, pushChannel, pushRecord)
		case "wechat_bot", "wechat", "企业微信":
			// 企业微信机器人，使用专门的企业微信推送函数
			pushErr = push.PushToWechatBot(pushVuln, pushChannel, pushRecord)
		default:
			pushErr = fmt.Errorf("不支持的通道类型: %s", channel.Type)
		}

		// 更新推送记录状态
		if pushErr != nil {
			utils.Infof("❌ 推送到通道%s失败: %v", channel.Name, pushErr)
			record.Status = "failed"
			record.ErrorMessage = pushErr.Error()
			pushErrors = append(pushErrors, fmt.Sprintf("推送到通道%s失败: %v", channel.Name, pushErr))
		} else {
			utils.Infof("✅ 推送到通道%s成功", channel.Name)
			record.Status = "success"
			record.PushedAt = time.Now().Unix()
			successCount++
		}

		// 保存推送记录
		if err := h.db.Save(record).Error; err != nil {
			utils.Infof("⚠️ 保存推送记录失败: %v", err)
		}
	}

	// 推送结果汇总
	utils.Infof("📊 推送结果汇总:")
	utils.Debugf("  - 成功推送: %d 个通道", successCount)
	utils.Debugf("  - 失败推送: %d 个通道", len(pushErrors))

	// 如果所有推送都失败了，返回错误
	if successCount == 0 && len(pushErrors) > 0 {
		utils.Infof("❌ 所有通道推送失败: %s", strings.Join(pushErrors, "; "))
		return fmt.Errorf("所有通道推送失败: %s", strings.Join(pushErrors, "; "))
	}

	// 如果有部分成功，记录警告
	if len(pushErrors) > 0 {
		utils.Infof("⚠️ 部分通道推送失败: %s", strings.Join(pushErrors, "; "))
	}

	utils.Infof("🎉 自动推送完成，成功推送到%d个通道", successCount)
	return nil
}
