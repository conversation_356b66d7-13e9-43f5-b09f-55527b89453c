package handlers

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/internal/models"
	"vulnerability_push/internal/service"
	"vulnerability_push/push"
)

// GetPushRecordsRequest 获取推送记录请求
type GetPushRecordsRequest struct {
	service.PaginationRequest
	Keyword         string `form:"keyword"`
	Status          string `form:"status"`
	ChannelType     string `form:"channel_type"`
	VulnerabilityID uint   `form:"vulnerability_id"`
	StartTime       string `form:"start_time"`
	EndTime         string `form:"end_time"`
}

// GetPushRecords 获取推送记录列表
func (h *PushHandler) GetPushRecords(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GetPushRecordsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 标准化分页参数
	req.NormalizePagination()
	if req.PageSize <= 0 {
		req.PageSize = 20 // 推送记录默认每页20条
	}

	// 构建查询
	query := h.db.Model(&models.PushRecord{}).Preload("Channel").Preload("Vulnerability")

	// 应用过滤条件
	if req.Keyword != "" {
		query = query.Joins("LEFT JOIN vulnerabilities ON push_records.vulnerability_id = vulnerabilities.id").
			Where("vulnerabilities.name LIKE ? OR vulnerabilities.description LIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.ChannelType != "" {
		query = query.Joins("LEFT JOIN push_channels ON push_records.channel_id = push_channels.id").
			Where("push_channels.type = ?", req.ChannelType)
	}
	if req.VulnerabilityID > 0 {
		query = query.Where("vulnerability_id = ?", req.VulnerabilityID)
	}
	if req.StartTime != "" {
		query = query.Where("pushed_at >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		query = query.Where("pushed_at <= ?", req.EndTime)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取推送记录总数失败: "+err.Error())
		return
	}

	// 应用排序和分页
	var records []models.PushRecord
	offset := (req.Page - 1) * req.PageSize

	if err := query.Order("pushed_at desc").Offset(offset).Limit(req.PageSize).Find(&records).Error; err != nil {
		h.InternalServerError(c, "获取推送记录列表失败: "+err.Error())
		return
	}

	// 转换为响应格式
	var responseRecords []map[string]interface{}
	for _, record := range records {
		recordData := map[string]interface{}{
			"id":                record.ID,
			"vulnerabilityId":   record.VulnerabilityID,
			"channelId":         record.ChannelID,
			"status":            record.Status,
			"errorMessage":      record.ErrorMessage,
			"pushedAt":          record.PushedAt,
			"pushedAtFormatted": time.Unix(record.PushedAt, 0).Format("2006-01-02 15:04:05"),
		}

		// 添加漏洞信息（如果预加载成功）
		if record.Vulnerability.ID > 0 {
			recordData["vulnerability"] = map[string]interface{}{
				"id":       record.Vulnerability.ID,
				"name":     record.Vulnerability.Name,
				"vulnId":   record.Vulnerability.VulnID,
				"severity": record.Vulnerability.Severity,
				"source":   record.Vulnerability.Source,
			}
		}

		// 添加推送通道信息（如果预加载成功）
		if record.Channel.ID > 0 {
			recordData["channel"] = map[string]interface{}{
				"id":   record.Channel.ID,
				"name": record.Channel.Name,
				"type": record.Channel.Type,
			}
		}

		responseRecords = append(responseRecords, recordData)
	}

	h.PaginatedSuccess(c, responseRecords, total, req.Page, req.PageSize)
}

// PushVulnerability 推送漏洞
func (h *PushHandler) PushVulnerability(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	// 获取漏洞信息
	var vuln models.Vulnerability
	if err := h.db.First(&vuln, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			h.NotFound(c, "漏洞不存在")
			return
		}
		h.InternalServerError(c, "查询漏洞失败: "+err.Error())
		return
	}

	// 检查漏洞内容是否包含敏感词（优先检查）
	contentToCheck := vuln.Name + " " + vuln.Description + " " + vuln.Remediation + " " + vuln.PushReason
	hasSensitiveWords, foundWords := push.ContainsSensitiveWords(contentToCheck)
	if hasSensitiveWords {
		// 返回403状态码和敏感词详细信息，与前端期望的格式一致
		c.JSON(403, gin.H{
			"code":      403,
			"msg":       fmt.Sprintf("漏洞内容包含敏感词，无法推送: %v", foundWords),
			"timestamp": time.Now().Unix(),
			"data": gin.H{
				"sensitive_words": foundWords,
			},
		})
		return
	}

	// 获取推送参数
	channelIDStr := c.Query("channel_id")
	policyIDStr := c.Query("policy_id")

	// 如果没有指定通道ID或策略ID，则使用默认策略
	if channelIDStr == "" && policyIDStr == "" {
		var defaultPolicy models.PushPolicy
		if err := h.db.Where("is_default = ?", true).First(&defaultPolicy).Error; err != nil {
			h.BadRequest(c, "未指定推送通道或策略，且系统中没有默认策略")
			return
		}
		policyIDStr = fmt.Sprintf("%d", defaultPolicy.ID)
	}

	// 如果指定了策略ID，使用策略推送
	if policyIDStr != "" {
		var policyID uint
		if _, err := fmt.Sscanf(policyIDStr, "%d", &policyID); err != nil {
			h.BadRequest(c, "无效的策略ID")
			return
		}

		var policy models.PushPolicy
		if err := h.db.First(&policy, policyID).Error; err != nil {
			h.NotFound(c, "推送策略不存在")
			return
		}

		// 获取策略关联的通道
		if policy.ChannelIDs == "" {
			h.BadRequest(c, "推送策略未关联任何通道")
			return
		}

		// 转换为推送模块需要的格式
		pushVuln := &push.Vulnerability{
			ID:             vuln.ID,
			Name:           vuln.Name,
			VulnID:         vuln.VulnID,
			Severity:       vuln.Severity,
			DisclosureDate: vuln.DisclosureDate,
			Source:         vuln.Source,
			Description:    vuln.Description,
			Remediation:    vuln.Remediation,
			References:     vuln.References,
			Tags:           vuln.Tags,
			PushReason:     vuln.PushReason,
		}

		channelIDs := strings.Split(policy.ChannelIDs, ",")
		successCount := 0

		// 推送到每个通道
		for _, channelIDStr := range channelIDs {
			channelID, err := strconv.Atoi(strings.TrimSpace(channelIDStr))
			if err != nil {
				continue
			}

			var channel models.PushChannel
			if err := h.db.First(&channel, channelID).Error; err != nil {
				continue
			}

			if !channel.Status {
				continue
			}

			// 转换为推送模块需要的格式
			pushChannel := &push.PushChannel{
				ID:     channel.ID,
				Name:   channel.Name,
				Type:   channel.Type,
				Config: channel.Config,
				Status: channel.Status,
			}

			// 创建推送记录
			record := models.PushRecord{
				VulnerabilityID: vuln.ID,
				ChannelID:       channel.ID,
				Status:          "pending",
				PushedAt:        time.Now().Unix(),
			}

			// 先创建记录
			if err := h.db.Create(&record).Error; err != nil {
				continue
			}

			// 转换为推送模块需要的记录格式
			pushRecord := &push.PushRecord{
				ID:              record.ID,
				VulnerabilityID: record.VulnerabilityID,
				ChannelID:       record.ChannelID,
				Status:          record.Status,
				ErrorMessage:    record.ErrorMessage,
				PushedAt:        record.PushedAt,
			}

			// 执行实际推送
			var pushErr error
			switch channel.Type {
			case push.TypeWechatBot:
				pushErr = push.PushToWechatBot(pushVuln, pushChannel, pushRecord)
			case push.TypeDingDing:
				pushErr = push.PushToDingDing(pushVuln, pushChannel, pushRecord)
			case push.TypeWebhook:
				pushErr = push.PushToWebhook(pushVuln, pushChannel, pushRecord)
			case push.TypeLark:
				pushErr = push.PushToLark(pushVuln, pushChannel, pushRecord)
			default:
				pushErr = fmt.Errorf("不支持的通道类型: %s", channel.Type)
			}

			// 更新推送记录状态
			if pushErr != nil {
				record.Status = "failed"
				record.ErrorMessage = pushErr.Error()
			} else {
				record.Status = "success"
				successCount++
			}
			h.db.Save(&record)
		}

		if successCount > 0 {
			h.Success(c, gin.H{
				"message": fmt.Sprintf("漏洞推送成功，共推送到 %d 个通道", successCount),
			})
		} else {
			h.BadRequest(c, "推送失败，没有可用的通道")
		}
		return
	}

	// 如果指定了通道ID，直接推送到该通道
	if channelIDStr != "" {
		var channelID uint
		if _, err := fmt.Sscanf(channelIDStr, "%d", &channelID); err != nil {
			h.BadRequest(c, "无效的通道ID")
			return
		}

		var channel models.PushChannel
		if err := h.db.First(&channel, channelID).Error; err != nil {
			h.NotFound(c, "推送通道不存在")
			return
		}

		if !channel.Status {
			h.BadRequest(c, "推送通道已禁用")
			return
		}

		// 转换为推送模块需要的格式
		pushVuln := &push.Vulnerability{
			ID:             vuln.ID,
			Name:           vuln.Name,
			VulnID:         vuln.VulnID,
			Severity:       vuln.Severity,
			DisclosureDate: vuln.DisclosureDate,
			Source:         vuln.Source,
			Description:    vuln.Description,
			Remediation:    vuln.Remediation,
			References:     vuln.References,
			Tags:           vuln.Tags,
			PushReason:     vuln.PushReason,
		}

		pushChannel := &push.PushChannel{
			ID:     channel.ID,
			Name:   channel.Name,
			Type:   channel.Type,
			Config: channel.Config,
			Status: channel.Status,
		}

		// 创建推送记录
		record := models.PushRecord{
			VulnerabilityID: vuln.ID,
			ChannelID:       channel.ID,
			Status:          "pending",
			PushedAt:        time.Now().Unix(),
		}

		// 先创建记录
		if err := h.db.Create(&record).Error; err != nil {
			h.InternalServerError(c, "创建推送记录失败: "+err.Error())
			return
		}

		// 转换为推送模块需要的记录格式
		pushRecord := &push.PushRecord{
			ID:              record.ID,
			VulnerabilityID: record.VulnerabilityID,
			ChannelID:       record.ChannelID,
			Status:          record.Status,
			ErrorMessage:    record.ErrorMessage,
			PushedAt:        record.PushedAt,
		}

		// 执行实际推送
		var pushErr error
		switch channel.Type {
		case push.TypeWechatBot:
			pushErr = push.PushToWechatBot(pushVuln, pushChannel, pushRecord)
		case push.TypeDingDing:
			pushErr = push.PushToDingDing(pushVuln, pushChannel, pushRecord)
		case push.TypeWebhook:
			pushErr = push.PushToWebhook(pushVuln, pushChannel, pushRecord)
		case push.TypeLark:
			pushErr = push.PushToLark(pushVuln, pushChannel, pushRecord)
		default:
			pushErr = fmt.Errorf("不支持的通道类型: %s", channel.Type)
		}

		// 更新推送记录状态
		if pushErr != nil {
			record.Status = "failed"
			record.ErrorMessage = pushErr.Error()
		} else {
			record.Status = "success"
		}
		h.db.Save(&record)

		if pushErr != nil {
			h.BadRequest(c, "推送失败: "+pushErr.Error())
			return
		}

		h.Success(c, gin.H{"message": "漏洞推送成功"})
		return
	}

	h.BadRequest(c, "请指定推送通道或策略")
}
