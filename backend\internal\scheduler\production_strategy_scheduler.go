package scheduler

import (
	"fmt"
	"sync"
	"time"

	"gorm.io/gorm"
	"vulnerability_push/internal/handlers"
	"vulnerability_push/internal/models"
	"vulnerability_push/internal/utils"
)

// ProductionStrategyScheduler 生产策略调度器
type ProductionStrategyScheduler struct {
	db      *gorm.DB
	handler *handlers.ProductionStrategyHandler
	timers  map[uint]*time.Timer
	mutex   sync.RWMutex
	running bool
}

// NewProductionStrategyScheduler 创建生产策略调度器
func NewProductionStrategyScheduler(db *gorm.DB) *ProductionStrategyScheduler {
	return &ProductionStrategyScheduler{
		db:      db,
		handler: handlers.NewProductionStrategyHandler(db),
		timers:  make(map[uint]*time.Timer),
		running: false,
	}
}

// Start 启动调度器
func (s *ProductionStrategyScheduler) Start() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.running {
		return
	}

	s.running = true
	utils.Infof("生产策略调度器启动")

	// 加载所有启用的生产策略
	s.loadEnabledStrategies()

	// 启动定期检查任务（每分钟检查一次）
	go s.periodicCheck()
}

// Stop 停止调度器
func (s *ProductionStrategyScheduler) Stop() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.running {
		return
	}

	s.running = false
	utils.Infof("生产策略调度器停止")

	// 停止所有定时器
	for id, timer := range s.timers {
		timer.Stop()
		delete(s.timers, id)
	}
}

// loadEnabledStrategies 加载所有启用的生产策略
func (s *ProductionStrategyScheduler) loadEnabledStrategies() {
	utils.Infof("开始加载启用的生产策略...")

	var strategies []models.ProductionStrategy
	err := s.db.Where("status = ? AND schedule_enabled = ?", "enabled", true).Find(&strategies).Error
	if err != nil {
		utils.Errorf("加载启用的生产策略失败: %v", err)
		return
	}

	utils.Infof("加载到 %d 个启用的生产策略", len(strategies))

	for i, strategy := range strategies {
		utils.Debugf("正在调度第 %d 个策略: %s (ID: %d, 间隔: %d分钟, 启用: %t)",
			i+1, strategy.Name, strategy.ID, strategy.ScheduleInterval, strategy.ScheduleEnabled)
		s.scheduleStrategy(&strategy)
		utils.Debugf("第 %d 个策略调度完成", i+1)
	}

	utils.Infof("所有策略调度完成")
}

// scheduleStrategy 为生产策略安排定时任务
func (s *ProductionStrategyScheduler) scheduleStrategy(strategy *models.ProductionStrategy) {
	utils.Debugf("开始调度策略: %s", strategy.Name)

	// 停止现有的定时器
	if timer, exists := s.timers[strategy.ID]; exists {
		timer.Stop()
		delete(s.timers, strategy.ID)
		utils.Debugf("停止了现有定时器")
	}

	// 计算下次执行时间
	interval := s.calculateInterval(strategy)
	utils.Debugf("计算得到间隔: %v", interval)

	if interval <= 0 {
		utils.Warnf("生产策略 %s 的执行间隔无效，跳过调度", strategy.Name)
		return
	}

	// 创建新的定时器
	timer := time.AfterFunc(interval, func() {
		s.executeStrategy(strategy)
	})

	s.timers[strategy.ID] = timer

	// 更新下次运行时间
	nextRunTime := time.Now().Add(interval).Unix()
	s.db.Model(strategy).Update("next_run_time", nextRunTime)

	fmt.Printf("生产策略 %s 已安排定时任务，下次执行时间: %s\n",
		strategy.Name, time.Now().Add(interval).Format("2006-01-02 15:04:05"))
}

// calculateInterval 计算执行间隔
func (s *ProductionStrategyScheduler) calculateInterval(strategy *models.ProductionStrategy) time.Duration {
	// 使用 ScheduleInterval 字段（1-1440分钟）
	if strategy.ScheduleInterval >= 1 && strategy.ScheduleInterval <= 1440 {
		fmt.Printf("生产策略 %s 使用间隔: %d分钟\n", strategy.Name, strategy.ScheduleInterval)
		return time.Duration(strategy.ScheduleInterval) * time.Minute
	}

	fmt.Printf("生产策略 %s 的间隔配置无效: %d，使用默认60分钟\n", strategy.Name, strategy.ScheduleInterval)
	return 60 * time.Minute
}

// executeStrategy 执行生产策略
func (s *ProductionStrategyScheduler) executeStrategy(strategy *models.ProductionStrategy) {
	fmt.Printf("开始执行定时生产策略: %s (ID: %d)\n", strategy.Name, strategy.ID)

	// 重新从数据库加载策略信息（确保是最新的）
	var currentStrategy models.ProductionStrategy
	err := s.db.First(&currentStrategy, strategy.ID).Error
	if err != nil {
		fmt.Printf("加载生产策略失败: %v\n", err)
		return
	}

	// 检查策略是否仍然启用
	if currentStrategy.Status != "enabled" || !currentStrategy.ScheduleEnabled {
		fmt.Printf("生产策略 %s 已被禁用，停止定时执行\n", currentStrategy.Name)
		s.mutex.Lock()
		if timer, exists := s.timers[currentStrategy.ID]; exists {
			timer.Stop()
			delete(s.timers, currentStrategy.ID)
		}
		s.mutex.Unlock()
		return
	}

	// 更新上次运行时间
	lastRunTime := time.Now().Unix()
	s.db.Model(&currentStrategy).Update("last_run_time", lastRunTime)

	// 异步执行策略
	go func() {
		s.handler.ExecuteProductionStrategyAsync(&currentStrategy)

		// 重新安排下次执行
		if s.running {
			s.scheduleStrategy(&currentStrategy)
		}
	}()
}

// periodicCheck 定期检查任务（每分钟执行一次）
func (s *ProductionStrategyScheduler) periodicCheck() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if !s.running {
				return
			}
			s.checkForUpdates()
		}
	}
}

// checkForUpdates 检查生产策略更新
func (s *ProductionStrategyScheduler) checkForUpdates() {
	var strategies []models.ProductionStrategy
	err := s.db.Where("status = ? AND schedule_enabled = ?", "enabled", true).Find(&strategies).Error
	if err != nil {
		fmt.Printf("检查生产策略更新失败: %v\n", err)
		return
	}

	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 检查新增的策略
	for _, strategy := range strategies {
		if _, exists := s.timers[strategy.ID]; !exists {
			fmt.Printf("发现新启用的生产策略: %s\n", strategy.Name)
			s.scheduleStrategy(&strategy)
		}
	}

	// 检查已删除或禁用的策略
	enabledIDs := make(map[uint]bool)
	for _, strategy := range strategies {
		enabledIDs[strategy.ID] = true
	}

	for id, timer := range s.timers {
		if !enabledIDs[id] {
			fmt.Printf("生产策略 ID %d 已被禁用或删除，停止定时任务\n", id)
			timer.Stop()
			delete(s.timers, id)
		}
	}
}

// UpdateStrategy 更新生产策略调度
func (s *ProductionStrategyScheduler) UpdateStrategy(strategyID uint) {
	var strategy models.ProductionStrategy
	err := s.db.First(&strategy, strategyID).Error
	if err != nil {
		utils.Errorf("加载生产策略失败: %v", err)
		return
	}

	if strategy.Status == "enabled" && strategy.ScheduleEnabled {
		utils.Infof("更新生产策略调度: %s", strategy.Name)
		s.scheduleStrategy(&strategy)
	} else {
		// 停止调度
		s.mutex.Lock()
		if timer, exists := s.timers[strategy.ID]; exists {
			timer.Stop()
			delete(s.timers, strategy.ID)
			utils.Infof("停止生产策略调度: %s", strategy.Name)
		}
		s.mutex.Unlock()
	}
}
