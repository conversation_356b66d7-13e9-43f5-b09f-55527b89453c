package handlers

import (
	"time"

	"github.com/gin-gonic/gin"

	"vulnerability_push/internal/models"
)

// GetVulnerabilityStatsRequest 获取漏洞统计请求
type GetVulnerabilityStatsRequest struct {
	StartDate string `form:"startDate"`
	EndDate   string `form:"endDate"`
}

// GetVulnerabilityStats 获取漏洞统计
func (h *VulnerabilityHandler) GetVulnerabilityStats(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GetVulnerabilityStatsRequest
	if err := h.BindQuery(c, &req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 构建查询
	query := h.db.Model(&models.Vulnerability{})

	// 日期范围过滤
	if req.StartDate != "" {
		query = query.Where("disclosure_date >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("disclosure_date <= ?", req.EndDate)
	}

	// 总数统计
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取漏洞总数失败: "+err.Error())
		return
	}

	// 按严重程度统计
	severityStats := make(map[string]int64)
	severities := []string{"低危", "中危", "高危", "严重"}
	for _, severity := range severities {
		var count int64
		if err := query.Where("severity = ?", severity).Count(&count).Error; err != nil {
			h.InternalServerError(c, "获取严重程度统计失败: "+err.Error())
			return
		}
		severityStats[severity] = count
	}

	// 按来源统计
	var sourceStats []map[string]interface{}
	rows, err := h.db.Raw(`
		SELECT source, COUNT(*) as count 
		FROM vulnerabilities 
		WHERE (? = '' OR disclosure_date >= ?) 
		AND (? = '' OR disclosure_date <= ?)
		GROUP BY source 
		ORDER BY count DESC 
		LIMIT 10
	`, req.StartDate, req.StartDate, req.EndDate, req.EndDate).Rows()
	if err != nil {
		h.InternalServerError(c, "获取来源统计失败: "+err.Error())
		return
	}
	defer rows.Close()

	for rows.Next() {
		var source string
		var count int64
		if err := rows.Scan(&source, &count); err != nil {
			h.InternalServerError(c, "扫描来源统计失败: "+err.Error())
			return
		}
		sourceStats = append(sourceStats, map[string]interface{}{
			"source": source,
			"count":  count,
		})
	}

	// 按月份统计（最近12个月）
	var monthlyStats []map[string]interface{}
	monthlyRows, err := h.db.Raw(`
		SELECT DATE_FORMAT(disclosure_date, '%Y-%m') as month, COUNT(*) as count 
		FROM vulnerabilities 
		WHERE disclosure_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
		AND (? = '' OR disclosure_date >= ?) 
		AND (? = '' OR disclosure_date <= ?)
		GROUP BY month 
		ORDER BY month DESC
	`, req.StartDate, req.StartDate, req.EndDate, req.EndDate).Rows()
	if err != nil {
		h.InternalServerError(c, "获取月度统计失败: "+err.Error())
		return
	}
	defer monthlyRows.Close()

	for monthlyRows.Next() {
		var month string
		var count int64
		if err := monthlyRows.Scan(&month, &count); err != nil {
			h.InternalServerError(c, "扫描月度统计失败: "+err.Error())
			return
		}
		monthlyStats = append(monthlyStats, map[string]interface{}{
			"month": month,
			"count": count,
		})
	}

	// 最近7天统计
	var recentStats []map[string]interface{}
	for i := 6; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i).Format("2006-01-02")
		var count int64
		
		countQuery := h.db.Model(&models.Vulnerability{}).Where("DATE(disclosure_date) = ?", date)
		if req.StartDate != "" {
			countQuery = countQuery.Where("disclosure_date >= ?", req.StartDate)
		}
		if req.EndDate != "" {
			countQuery = countQuery.Where("disclosure_date <= ?", req.EndDate)
		}
		
		if err := countQuery.Count(&count).Error; err != nil {
			h.InternalServerError(c, "获取最近统计失败: "+err.Error())
			return
		}
		
		recentStats = append(recentStats, map[string]interface{}{
			"date":  date,
			"count": count,
		})
	}

	stats := map[string]interface{}{
		"total":         total,
		"severityStats": severityStats,
		"sourceStats":   sourceStats,
		"monthlyStats":  monthlyStats,
		"recentStats":   recentStats,
	}

	h.Success(c, stats)
}

// GetVulnerabilityTrends 获取漏洞趋势
func (h *VulnerabilityHandler) GetVulnerabilityTrends(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 获取最近30天的趋势数据
	var trends []map[string]interface{}
	for i := 29; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i).Format("2006-01-02")
		
		// 当天新增漏洞数
		var newCount int64
		if err := h.db.Model(&models.Vulnerability{}).
			Where("DATE(FROM_UNIXTIME(created_at)) = ?", date).
			Count(&newCount).Error; err != nil {
			h.InternalServerError(c, "获取新增趋势失败: "+err.Error())
			return
		}
		
		// 当天披露漏洞数
		var disclosedCount int64
		if err := h.db.Model(&models.Vulnerability{}).
			Where("disclosure_date = ?", date).
			Count(&disclosedCount).Error; err != nil {
			h.InternalServerError(c, "获取披露趋势失败: "+err.Error())
			return
		}
		
		trends = append(trends, map[string]interface{}{
			"date":           date,
			"newCount":       newCount,
			"disclosedCount": disclosedCount,
		})
	}

	h.Success(c, map[string]interface{}{
		"trends": trends,
	})
}

// GetVulnerabilitySummary 获取漏洞概览
func (h *VulnerabilityHandler) GetVulnerabilitySummary(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 总漏洞数
	var total int64
	if err := h.db.Model(&models.Vulnerability{}).Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取总数失败: "+err.Error())
		return
	}

	// 今日新增
	today := time.Now().Format("2006-01-02")
	var todayCount int64
	if err := h.db.Model(&models.Vulnerability{}).
		Where("DATE(FROM_UNIXTIME(created_at)) = ?", today).
		Count(&todayCount).Error; err != nil {
		h.InternalServerError(c, "获取今日新增失败: "+err.Error())
		return
	}

	// 本周新增
	weekStart := time.Now().AddDate(0, 0, -int(time.Now().Weekday())).Format("2006-01-02")
	var weekCount int64
	if err := h.db.Model(&models.Vulnerability{}).
		Where("DATE(FROM_UNIXTIME(created_at)) >= ?", weekStart).
		Count(&weekCount).Error; err != nil {
		h.InternalServerError(c, "获取本周新增失败: "+err.Error())
		return
	}

	// 本月新增
	monthStart := time.Now().AddDate(0, 0, -time.Now().Day()+1).Format("2006-01-02")
	var monthCount int64
	if err := h.db.Model(&models.Vulnerability{}).
		Where("DATE(FROM_UNIXTIME(created_at)) >= ?", monthStart).
		Count(&monthCount).Error; err != nil {
		h.InternalServerError(c, "获取本月新增失败: "+err.Error())
		return
	}

	// 严重漏洞数（高危+严重）
	var criticalCount int64
	if err := h.db.Model(&models.Vulnerability{}).
		Where("severity IN ?", []string{"高危", "严重"}).
		Count(&criticalCount).Error; err != nil {
		h.InternalServerError(c, "获取严重漏洞数失败: "+err.Error())
		return
	}

	summary := map[string]interface{}{
		"total":         total,
		"todayCount":    todayCount,
		"weekCount":     weekCount,
		"monthCount":    monthCount,
		"criticalCount": criticalCount,
	}

	h.Success(c, summary)
}

// GetDashboardStats 获取首页仪表板统计数据
func (h *VulnerabilityHandler) GetDashboardStats(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 总漏洞数
	var totalCount int64
	if err := h.db.Model(&models.Vulnerability{}).Count(&totalCount).Error; err != nil {
		h.InternalServerError(c, "获取总数失败: "+err.Error())
		return
	}

	// 计算增长率（相比上周）
	weekAgo := time.Now().AddDate(0, 0, -7).Format("2006-01-02")
	var weekAgoCount int64
	if err := h.db.Model(&models.Vulnerability{}).
		Where("DATE(FROM_UNIXTIME(created_at)) < ?", weekAgo).
		Count(&weekAgoCount).Error; err != nil {
		h.InternalServerError(c, "获取上周数据失败: "+err.Error())
		return
	}

	var growthRate float64
	if weekAgoCount > 0 {
		growthRate = float64(totalCount-weekAgoCount) / float64(weekAgoCount) * 100
	}

	// 严重漏洞数（高危+严重）
	var criticalCount int64
	if err := h.db.Model(&models.Vulnerability{}).
		Where("severity IN ?", []string{"高危", "严重"}).
		Count(&criticalCount).Error; err != nil {
		h.InternalServerError(c, "获取严重漏洞数失败: "+err.Error())
		return
	}

	// 高危漏洞数
	var highRiskCount int64
	if err := h.db.Model(&models.Vulnerability{}).
		Where("severity = ?", "高危").
		Count(&highRiskCount).Error; err != nil {
		h.InternalServerError(c, "获取高危漏洞数失败: "+err.Error())
		return
	}

	// 高危漏洞增长率
	var highRiskWeekAgoCount int64
	if err := h.db.Model(&models.Vulnerability{}).
		Where("severity = ? AND DATE(FROM_UNIXTIME(created_at)) < ?", "高危", weekAgo).
		Count(&highRiskWeekAgoCount).Error; err != nil {
		h.InternalServerError(c, "获取高危漏洞上周数据失败: "+err.Error())
		return
	}

	var highRiskGrowthRate float64
	if highRiskWeekAgoCount > 0 {
		highRiskGrowthRate = float64(highRiskCount-highRiskWeekAgoCount) / float64(highRiskWeekAgoCount) * 100
	}

	// 按严重程度统计
	severityDistribution := make(map[string]int64)
	severities := []string{"低危", "中危", "高危", "严重"}
	for _, severity := range severities {
		var count int64
		if err := h.db.Model(&models.Vulnerability{}).
			Where("severity = ?", severity).
			Count(&count).Error; err != nil {
			h.InternalServerError(c, "获取严重程度统计失败: "+err.Error())
			return
		}
		severityDistribution[severity] = count
	}

	// 获取收集器统计
	var collectorStats []map[string]interface{}
	crawlerRows, err := h.db.Raw(`
		SELECT c.name, c.status, c.last_run_at, c.next_run_at, c.type,
		       COALESCE(COUNT(v.id), 0) as vuln_count
		FROM crawlers c
		LEFT JOIN vulnerabilities v ON (
			(c.type = 'chaitin' AND v.source LIKE '%chaitin%') OR
			(c.type = 'qianxin' AND v.source LIKE '%qianxin%') OR
			(c.type = 'aliyun' AND v.source LIKE '%aliyun%') OR
			(c.type = 'threatbook' AND v.source LIKE '%threatbook%') OR
			(c.type = 'seebug' AND v.source LIKE '%seebug%') OR
			(c.type = 'venustech' AND v.source LIKE '%venustech%') OR
			(c.type = 'oscs' AND v.source LIKE '%oscs%') OR
			(c.type = 'nvd' AND (v.source LIKE '%nvd%' OR v.source LIKE '%nist%'))
		)
		GROUP BY c.id, c.name, c.status, c.last_run_at, c.next_run_at, c.type
		ORDER BY c.last_run_at DESC
		LIMIT 5
	`).Rows()
	if err != nil {
		h.InternalServerError(c, "获取收集器统计失败: "+err.Error())
		return
	}
	defer crawlerRows.Close()

	activeCollectorCount := 0
	for crawlerRows.Next() {
		var name, crawlerType string
		var status bool
		var lastRunAt, nextRunAt int64
		var vulnCount int64
		if err := crawlerRows.Scan(&name, &status, &lastRunAt, &nextRunAt, &crawlerType, &vulnCount); err != nil {
			h.InternalServerError(c, "解析收集器统计失败: "+err.Error())
			return
		}

		if status {
			activeCollectorCount++
		}

		collectorStats = append(collectorStats, map[string]interface{}{
			"name":        name,
			"status":      status,
			"count":       vulnCount,
			"lastRunTime": lastRunAt,
			"nextRunTime": nextRunAt,
		})
	}

	// 今日新入库漏洞
	today := time.Now().Format("2006-01-02")
	var todayVulnerabilities []map[string]interface{}
	todayRows, err := h.db.Raw(`
		SELECT id, name, vuln_id, severity
		FROM vulnerabilities
		WHERE DATE(FROM_UNIXTIME(created_at)) = ?
		ORDER BY created_at DESC
		LIMIT 10
	`, today).Rows()
	if err != nil {
		h.InternalServerError(c, "获取今日漏洞失败: "+err.Error())
		return
	}
	defer todayRows.Close()

	for todayRows.Next() {
		var id int64
		var name, vulnId, severity string
		if err := todayRows.Scan(&id, &name, &vulnId, &severity); err != nil {
			h.InternalServerError(c, "解析今日漏洞失败: "+err.Error())
			return
		}

		todayVulnerabilities = append(todayVulnerabilities, map[string]interface{}{
			"id":       id,
			"name":     name,
			"vulnId":   vulnId,
			"severity": severity,
		})
	}

	// 构建响应数据
	stats := map[string]interface{}{
		"total": map[string]interface{}{
			"count":      totalCount,
			"growthRate": growthRate,
		},
		"urgent": map[string]interface{}{
			"count":      criticalCount,
			"needAction": criticalCount > 0,
		},
		"highRisk": map[string]interface{}{
			"count":      highRiskCount,
			"growthRate": highRiskGrowthRate,
		},
		"activeCollectors": map[string]interface{}{
			"count":  activeCollectorCount,
			"status": "运行正常",
		},
		"severityDistribution": severityDistribution,
		"collectorStats":       collectorStats,
		"todayVulnerabilities": todayVulnerabilities,
	}

	h.Success(c, stats)
}

// GetDatabaseInfo 获取数据库信息（调试用）
func (h *VulnerabilityHandler) GetDatabaseInfo(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 检查漏洞表
	var vulnCount int64
	if err := h.db.Model(&models.Vulnerability{}).Count(&vulnCount).Error; err != nil {
		h.InternalServerError(c, "获取漏洞数量失败: "+err.Error())
		return
	}

	// 检查采集器表
	var crawlerCount int64
	if err := h.db.Model(&models.Crawler{}).Count(&crawlerCount).Error; err != nil {
		h.InternalServerError(c, "获取采集器数量失败: "+err.Error())
		return
	}

	// 获取最近的几条漏洞记录
	var recentVulns []models.Vulnerability
	if err := h.db.Model(&models.Vulnerability{}).
		Order("created_at DESC").
		Limit(5).
		Find(&recentVulns).Error; err != nil {
		h.InternalServerError(c, "获取最近漏洞失败: "+err.Error())
		return
	}

	// 获取采集器信息
	var crawlers []models.Crawler
	if err := h.db.Model(&models.Crawler{}).
		Order("created_at DESC").
		Find(&crawlers).Error; err != nil {
		h.InternalServerError(c, "获取采集器失败: "+err.Error())
		return
	}

	info := map[string]interface{}{
		"vulnerabilityCount": vulnCount,
		"crawlerCount":       crawlerCount,
		"recentVulns":        recentVulns,
		"crawlers":           crawlers,
	}

	h.Success(c, info)
}
