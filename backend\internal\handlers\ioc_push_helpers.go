package handlers

import (
	"fmt"
	"strings"
	"time"

	"vulnerability_push/internal/models"
	"vulnerability_push/internal/utils"
	"vulnerability_push/push"
)

// 辅助方法：转换为push包中的IOC情报类型
func (h *IOCHandler) convertToIOCIntelligencePush(ioc *models.IOCIntelligence) *push.IOCIntelligence {
	var tags []string
	if ioc.Tags != "" {
		tags = strings.Split(ioc.Tags, ",")
		for i, tag := range tags {
			tags[i] = strings.TrimSpace(tag)
		}
	}

	return &push.IOCIntelligence{
		ID:            ioc.ID,
		IOC:           ioc.IOC,
		IOCType:       ioc.IOCType,
		Location:      ioc.Location,
		Type:          ioc.Type,
		RiskLevel:     ioc.RiskLevel,
		HitCount:      ioc.HitCount,
		Description:   ioc.Description,
		Source:        ioc.Source,
		DiscoveryDate: time.Unix(ioc.CreatedAt, 0).Format("2006-01-02"),
		Tags:          tags,
		PushReason:    ioc.PushReason,
	}
}

// 辅助方法：转换为push包中的推送通道类型
func (h *IOCHandler) convertToPushChannel(channel *models.PushChannel) *push.PushChannel {
	return &push.PushChannel{
		ID:          channel.ID,
		Name:        channel.Name,
		Type:        channel.Type,
		Description: channel.Description,
		Config:      channel.Config,
		Status:      channel.Status,
		CreatedAt:   channel.CreatedAt,
		UpdatedAt:   channel.UpdatedAt,
	}
}

// 辅助方法：转换为push包中的推送记录类型
func (h *IOCHandler) convertToIOCIntelligencePushRecord(record *models.IOCIntelligencePushRecord) *push.IOCIntelligencePushRecord {
	return &push.IOCIntelligencePushRecord{
		ID:                record.ID,
		IOCIntelligenceID: record.IOCIntelligenceID,
		ChannelID:         record.ChannelID,
		Status:            record.Status,
		ErrorMessage:      record.ErrorMessage,
		PushedAt:          record.PushedAt,
	}
}

// 推送到策略
func (h *IOCHandler) pushIOCIntelligenceUsingPolicy(iocIntel *models.IOCIntelligence, policy *models.PushPolicy) error {
	if policy.ChannelIDs == "" {
		return fmt.Errorf("策略 %s 没有配置推送通道", policy.Name)
	}

	// 解析通道ID列表
	channelIDs := strings.Split(policy.ChannelIDs, ",")

	// 转换为push包中的类型
	pushIOCIntel := h.convertToIOCIntelligencePush(iocIntel)

	// 推送到每个通道
	var errors []string
	for _, channelIDStr := range channelIDs {
		channelIDStr = strings.TrimSpace(channelIDStr)
		if channelIDStr == "" {
			continue
		}

		var channelID uint
		fmt.Sscanf(channelIDStr, "%d", &channelID)

		var channel models.PushChannel
		if err := h.db.First(&channel, channelID).Error; err != nil {
			errors = append(errors, fmt.Sprintf("通道ID %s 不存在", channelIDStr))
			continue
		}

		if !channel.Status {
			errors = append(errors, fmt.Sprintf("通道 %s 已禁用", channel.Name))
			continue
		}

		// 创建推送记录
		record := models.IOCIntelligencePushRecord{
			IOCIntelligenceID: iocIntel.ID,
			ChannelID:         channel.ID,
			ChannelName:       channel.Name,
			ChannelType:       channel.Type,
			Status:            "success",
			PushedAt:          time.Now().Unix(),
		}

		// 转换为push包中的类型
		pushChannel := h.convertToPushChannel(&channel)
		pushRecord := h.convertToIOCIntelligencePushRecord(&record)

		// 根据通道类型推送
		var pushErr error
		switch channel.Type {
		case "wechat_bot":
			pushErr = h.pushIOCIntelligenceToWechatBot(pushIOCIntel, pushChannel, pushRecord)
		case "dingding":
			pushErr = h.pushIOCIntelligenceToDingDing(pushIOCIntel, pushChannel, pushRecord)
		case "webhook":
			pushErr = h.pushIOCIntelligenceToWebhook(pushIOCIntel, pushChannel, pushRecord)
		case "lark":
			pushErr = h.pushIOCIntelligenceToLark(pushIOCIntel, pushChannel, pushRecord)
		default:
			pushErr = fmt.Errorf("不支持的通道类型: %s", channel.Type)
		}

		// 更新推送记录状态
		if pushErr != nil {
			record.Status = "failed"
			record.ErrorMessage = pushErr.Error()
			errors = append(errors, fmt.Sprintf("推送到通道 %s 失败: %v", channel.Name, pushErr))
		}

		// 保存推送记录
		if err := h.db.Create(&record).Error; err != nil {
			fmt.Printf("保存推送记录失败: %v\n", err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分推送失败: %s", strings.Join(errors, "; "))
	}

	return nil
}

// 默认推送（推送到所有启用的通道）
func (h *IOCHandler) pushIOCIntelligenceToDefault(iocIntel *models.IOCIntelligence) error {
	// 获取所有启用的推送通道
	var channels []models.PushChannel
	if err := h.db.Where("status = ?", true).Find(&channels).Error; err != nil {
		return fmt.Errorf("获取推送通道失败: %v", err)
	}

	if len(channels) == 0 {
		return fmt.Errorf("没有可用的推送通道")
	}

	// 转换为push包中的类型
	pushIOCIntel := h.convertToIOCIntelligencePush(iocIntel)

	// 推送到每个通道
	var errors []string
	for _, channel := range channels {
		// 创建推送记录
		record := models.IOCIntelligencePushRecord{
			IOCIntelligenceID: iocIntel.ID,
			ChannelID:         channel.ID,
			ChannelName:       channel.Name,
			ChannelType:       channel.Type,
			Status:            "success",
			PushedAt:          time.Now().Unix(),
		}

		// 转换为push包中的类型
		pushChannel := h.convertToPushChannel(&channel)
		pushRecord := h.convertToIOCIntelligencePushRecord(&record)

		// 根据通道类型推送
		var pushErr error
		switch channel.Type {
		case "wechat_bot":
			pushErr = h.pushIOCIntelligenceToWechatBot(pushIOCIntel, pushChannel, pushRecord)
		case "dingding":
			pushErr = h.pushIOCIntelligenceToDingDing(pushIOCIntel, pushChannel, pushRecord)
		case "webhook":
			pushErr = h.pushIOCIntelligenceToWebhook(pushIOCIntel, pushChannel, pushRecord)
		case "lark":
			pushErr = h.pushIOCIntelligenceToLark(pushIOCIntel, pushChannel, pushRecord)
		default:
			pushErr = fmt.Errorf("不支持的通道类型: %s", channel.Type)
		}

		// 更新推送记录状态
		if pushErr != nil {
			record.Status = "failed"
			record.ErrorMessage = pushErr.Error()
			errors = append(errors, fmt.Sprintf("推送到通道 %s 失败: %v", channel.Name, pushErr))
		}

		// 保存推送记录
		if err := h.db.Create(&record).Error; err != nil {
			fmt.Printf("保存推送记录失败: %v\n", err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分推送失败: %s", strings.Join(errors, "; "))
	}

	return nil
}

// 推送到微信机器人
func (h *IOCHandler) pushIOCIntelligenceToWechatBot(iocIntel *push.IOCIntelligence, channel *push.PushChannel, record *push.IOCIntelligencePushRecord) error {
	fmt.Printf("推送IOC情报到微信机器人: %s\n", iocIntel.IOC)
	return push.PushIOCIntelligenceToWechatBot(iocIntel, channel, record)
}

// 推送到钉钉
func (h *IOCHandler) pushIOCIntelligenceToDingDing(iocIntel *push.IOCIntelligence, channel *push.PushChannel, record *push.IOCIntelligencePushRecord) error {
	utils.Infof("推送IOC情报到钉钉: %s", iocIntel.IOC)
	return push.PushIOCIntelligenceToDingDing(iocIntel, channel, record)
}

// 推送到Webhook
func (h *IOCHandler) pushIOCIntelligenceToWebhook(iocIntel *push.IOCIntelligence, channel *push.PushChannel, record *push.IOCIntelligencePushRecord) error {
	utils.Infof("推送IOC情报到Webhook: %s", iocIntel.IOC)
	return push.PushIOCIntelligenceToWebhook(iocIntel, channel, record)
}

// 推送到飞书
func (h *IOCHandler) pushIOCIntelligenceToLark(iocIntel *push.IOCIntelligence, channel *push.PushChannel, record *push.IOCIntelligencePushRecord) error {
	fmt.Printf("推送IOC情报到飞书: %s\n", iocIntel.IOC)
	return push.PushIOCIntelligenceToLark(iocIntel, channel, record)
}
