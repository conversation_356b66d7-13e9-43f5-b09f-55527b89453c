package service

import (
	"sync"
	"time"

	"gorm.io/gorm"
	"vulnerability_push/internal/utils"
)

var (
	globalWhitelistCache *IOCWhitelistCache
	globalWhitelistOnce  sync.Once
)

// InitGlobalWhitelistCache 初始化全局白名单缓存
func InitGlobalWhitelistCache(db *gorm.DB) {
	globalWhitelistOnce.Do(func() {
		globalWhitelistCache = NewIOCWhitelistCache(db)

		// 启动定期刷新协程（可选）
		go func() {
			ticker := time.NewTicker(5 * time.Minute) // 每5分钟检查一次
			defer ticker.Stop()

			for range ticker.C {
				// 如果缓存超过10分钟没有更新，自动刷新
				if globalWhitelistCache.IsExpired(10 * time.Minute) {
					if err := globalWhitelistCache.RefreshCache(); err != nil {
						utils.Errorf("自动刷新IOC白名单缓存失败: %v", err)
					}
				}
			}
		}()
	})
}

// GetGlobalWhitelistCache 获取全局白名单缓存实例
func GetGlobalWhitelistCache() *IOCWhitelistCache {
	return globalWhitelistCache
}

// IsIOCWhitelistedGlobal 全局IOC白名单检查函数
func IsIOCWhitelistedGlobal(ioc string) bool {
	if globalWhitelistCache == nil {
		return false
	}
	return globalWhitelistCache.IsIOCWhitelisted(ioc)
}

// IsIPWhitelistedGlobal 全局IP白名单检查函数
func IsIPWhitelistedGlobal(ip string) bool {
	if globalWhitelistCache == nil {
		return false
	}
	return globalWhitelistCache.IsIPWhitelisted(ip)
}

// IsDomainWhitelistedGlobal 全局域名白名单检查函数
func IsDomainWhitelistedGlobal(domain string) bool {
	if globalWhitelistCache == nil {
		return false
	}
	return globalWhitelistCache.IsDomainWhitelisted(domain)
}

// RefreshGlobalWhitelistCache 刷新全局白名单缓存
func RefreshGlobalWhitelistCache() error {
	if globalWhitelistCache == nil {
		return nil
	}
	return globalWhitelistCache.RefreshCache()
}

// AddToGlobalWhitelistCache 添加IOC到全局白名单缓存
func AddToGlobalWhitelistCache(ioc, iocType string) {
	if globalWhitelistCache == nil {
		return
	}
	
	switch iocType {
	case "ip":
		globalWhitelistCache.AddIP(ioc)
	case "domain":
		globalWhitelistCache.AddDomain(ioc)
	}
}

// RemoveFromGlobalWhitelistCache 从全局白名单缓存中移除IOC
func RemoveFromGlobalWhitelistCache(ioc, iocType string) {
	if globalWhitelistCache == nil {
		return
	}
	
	switch iocType {
	case "ip":
		globalWhitelistCache.RemoveIP(ioc)
	case "domain":
		globalWhitelistCache.RemoveDomain(ioc)
	}
}

// GetGlobalWhitelistStats 获取全局白名单缓存统计信息
func GetGlobalWhitelistStats() map[string]interface{} {
	if globalWhitelistCache == nil {
		return map[string]interface{}{
			"ip_count":     0,
			"domain_count": 0,
			"last_load":    nil,
		}
	}
	return globalWhitelistCache.GetCacheStats()
}
