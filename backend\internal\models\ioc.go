package models

import (
	"fmt"
	"strings"
	"time"
)

// ========================================
// IOC情报功能模型定义
// 功能模块划分：
// 1. 数据采集模块 (Data Collection) - 从各种数据接口采集原始数据，处理和清洗数据，存储为源数据
// 2. 情报生产模块 (Intelligence Production) - 从源数据生成IOC情报，应用生产策略和过滤规则
// 3. 情报推送模块 (Intelligence Push) - 管理IOC情报的推送，支持单个和批量推送
// ========================================

// IOCIntelligence IOC情报模型 - 情报生产模块的核心数据模型
// 存储经过生产策略处理后的最终IOC情报数据
type IOCIntelligence struct {
	ID          uint   `gorm:"primaryKey" json:"id"`
	IOC         string `gorm:"size:255;not null;index" json:"ioc"`
	IOCType     string `gorm:"size:20;not null;index" json:"iocType"` // ip, domain, hash, url
	Location    string `gorm:"size:100" json:"location"`
	Type        string `gorm:"size:100;not null" json:"type"`
	RiskLevel   string `gorm:"size:20;not null;index" json:"riskLevel"` // 严重, 高危, 中危, 低危
	HitCount    int    `gorm:"default:0" json:"hitCount"`
	Description string `gorm:"type:text" json:"description"`
	Tags        string `gorm:"type:text" json:"tags"`
	PushStatus  string `gorm:"size:20;default:'not_pushed'" json:"pushStatus"` // not_pushed, pushed, failed

	// 有效期相关字段
	IsValid        bool  `gorm:"default:true;index" json:"isValid"`        // 是否有效
	ValidityDays   int   `gorm:"default:30" json:"validityDays"`           // 有效期天数
	ExpiresAt      int64 `gorm:"default:0;index" json:"expiresAt"`         // 过期时间戳，0表示永不过期
	LastUpdatedAt  int64 `gorm:"autoUpdateTime" json:"lastUpdatedAt"`      // 最后更新时间（替换原发现时间）

	// 天际友盟情报相关字段
	TJUNData         string `gorm:"type:longtext" json:"tjunData"`         // 天际友盟原始数据(JSON格式)
	TJUNQueryStatus  string `gorm:"size:20;default:'not_queried'" json:"tjunQueryStatus"` // not_queried, success, failed
	TJUNQueryTime    int64  `gorm:"default:0" json:"tjunQueryTime"`        // 天际友盟查询时间
	TJUNErrorMessage string `gorm:"type:text" json:"tjunErrorMessage"`     // 天际友盟查询错误信息

	// 微步威胁情报相关字段
	WeibuData         string `gorm:"type:longtext" json:"weibuData"`         // 微步威胁情报原始数据(JSON格式)
	WeibuQueryStatus  string `gorm:"size:20;default:'not_queried'" json:"weibuQueryStatus"` // not_queried, success, failed
	WeibuQueryTime    int64  `gorm:"default:0" json:"weibuQueryTime"`        // 微步威胁情报查询时间
	WeibuErrorMessage string `gorm:"type:text" json:"weibuErrorMessage"`     // 微步威胁情报查询错误信息
	PushedAt    int64  `gorm:"default:0" json:"pushedAt"`                      // 推送时间，0表示未推送
	Source      string `gorm:"size:100" json:"source"`
	PushReason  string `gorm:"type:text" json:"pushReason"`
	CreatedAt   int64  `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt   int64  `gorm:"autoUpdateTime" json:"updatedAt"`
}

// TableName 指定表名
func (IOCIntelligence) TableName() string {
	return "ioc_intelligence"
}

// IOCIntelligencePushRecord IOC情报推送记录模型 - 情报推送模块的核心数据模型
// 记录IOC情报推送的详细信息和状态
type IOCIntelligencePushRecord struct {
	ID                uint   `gorm:"primaryKey" json:"id"`
	IOCIntelligenceID uint   `gorm:"not null;index" json:"iocIntelligenceId"`
	ChannelID         uint   `gorm:"not null;index" json:"channelId"`
	ChannelName       string `gorm:"size:100" json:"channelName"`
	ChannelType       string `gorm:"size:50" json:"channelType"`
	Status            string `gorm:"size:20;not null" json:"status"` // success, failed
	ErrorMessage      string `gorm:"type:text" json:"errorMessage"`
	PushedAt          int64  `gorm:"autoCreateTime" json:"pushedAt"`

	// 关联关系
	IOCIntelligence *IOCIntelligence `gorm:"foreignKey:IOCIntelligenceID" json:"iocIntelligence,omitempty"`
	Channel         *PushChannel     `gorm:"foreignKey:ChannelID" json:"channel,omitempty"`
}

// TableName 指定表名
func (IOCIntelligencePushRecord) TableName() string {
	return "ioc_intelligence_push_records"
}

// GetTags 获取标签数组
func (ioc *IOCIntelligence) GetTags() []string {
	if ioc.Tags == "" {
		return []string{}
	}
	
	tags := strings.Split(ioc.Tags, ",")
	result := make([]string, 0, len(tags))
	for _, tag := range tags {
		if trimmed := strings.TrimSpace(tag); trimmed != "" {
			result = append(result, trimmed)
		}
	}
	return result
}

// SetTags 设置标签数组
func (ioc *IOCIntelligence) SetTags(tags []string) {
	ioc.Tags = strings.Join(tags, ",")
}

// GetRiskLevelScore 获取风险等级分数（用于排序）
func (ioc *IOCIntelligence) GetRiskLevelScore() int {
	switch ioc.RiskLevel {
	case "严重":
		return 5
	case "高危":
		return 4
	case "中危":
		return 3
	case "低危":
		return 2
	default:
		return 1
	}
}

// IsHighRisk 是否为高风险
func (ioc *IOCIntelligence) IsHighRisk() bool {
	return ioc.RiskLevel == "严重" || ioc.RiskLevel == "高危"
}

// GetCreatedTime 获取创建时间
func (ioc *IOCIntelligence) GetCreatedTime() time.Time {
	return time.Unix(ioc.CreatedAt, 0)
}

// GetUpdatedTime 获取更新时间
func (ioc *IOCIntelligence) GetUpdatedTime() time.Time {
	return time.Unix(ioc.UpdatedAt, 0)
}

// GetLastUpdatedTime 获取最后更新时间
func (ioc *IOCIntelligence) GetLastUpdatedTime() time.Time {
	return time.Unix(ioc.LastUpdatedAt, 0)
}

// GetExpiresTime 获取过期时间
func (ioc *IOCIntelligence) GetExpiresTime() time.Time {
	if ioc.ExpiresAt == 0 {
		return time.Time{} // 返回零值时间表示永不过期
	}
	return time.Unix(ioc.ExpiresAt, 0)
}

// IsExpired 检查是否已过期
func (ioc *IOCIntelligence) IsExpired() bool {
	if ioc.ExpiresAt == 0 {
		return false // 永不过期
	}
	return time.Now().Unix() > ioc.ExpiresAt
}

// SetValidityPeriod 设置有效期
func (ioc *IOCIntelligence) SetValidityPeriod(days int) {
	if days <= 0 {
		// 永不过期
		ioc.ValidityDays = 0
		ioc.ExpiresAt = 0
	} else {
		ioc.ValidityDays = days
		ioc.ExpiresAt = time.Now().AddDate(0, 0, days).Unix()
	}
}

// RefreshValidity 刷新有效期（基于当前时间重新计算过期时间）
func (ioc *IOCIntelligence) RefreshValidity() {
	if ioc.ValidityDays > 0 {
		ioc.ExpiresAt = time.Now().AddDate(0, 0, ioc.ValidityDays).Unix()
	}
}

// MarkAsInvalid 标记为无效
func (ioc *IOCIntelligence) MarkAsInvalid() {
	ioc.IsValid = false
}

// MarkAsValid 标记为有效
func (ioc *IOCIntelligence) MarkAsValid() {
	ioc.IsValid = true
}

// GetValidityStatusText 获取有效性状态文本
func (ioc *IOCIntelligence) GetValidityStatusText() string {
	if !ioc.IsValid {
		return "无效"
	}
	if ioc.IsExpired() {
		return "已过期"
	}
	return "有效"
}

// GetValidityInfo 获取有效期信息
func (ioc *IOCIntelligence) GetValidityInfo() map[string]interface{} {
	info := map[string]interface{}{
		"is_valid":     ioc.IsValid,
		"validity_days": ioc.ValidityDays,
		"status":       ioc.GetValidityStatusText(),
	}

	if ioc.ExpiresAt > 0 {
		info["expires_at"] = ioc.GetExpiresTime().Format("2006-01-02 15:04:05")
		info["expires_timestamp"] = ioc.ExpiresAt

		// 计算剩余天数
		remainingSeconds := ioc.ExpiresAt - time.Now().Unix()
		if remainingSeconds > 0 {
			remainingDays := int(remainingSeconds / 86400) // 86400秒 = 1天
			info["remaining_days"] = remainingDays
		} else {
			info["remaining_days"] = 0
		}
	} else {
		info["expires_at"] = "永不过期"
		info["expires_timestamp"] = 0
		info["remaining_days"] = -1 // -1表示永不过期
	}

	return info
}

// IncrementHitCount 增加命中次数
func (ioc *IOCIntelligence) IncrementHitCount() {
	ioc.HitCount++
}

// GetPushedTime 获取推送时间
func (ioc *IOCIntelligence) GetPushedTime() time.Time {
	if ioc.PushedAt == 0 {
		return time.Time{} // 返回零值时间表示未推送
	}
	return time.Unix(ioc.PushedAt, 0)
}

// IsPushed 是否已推送
func (ioc *IOCIntelligence) IsPushed() bool {
	return ioc.PushStatus == "pushed"
}

// MarkAsPushed 标记为已推送
func (ioc *IOCIntelligence) MarkAsPushed() {
	ioc.PushStatus = "pushed"
	ioc.PushedAt = time.Now().Unix()
}

// MarkAsPushFailed 标记推送失败
func (ioc *IOCIntelligence) MarkAsPushFailed() {
	ioc.PushStatus = "failed"
	ioc.PushedAt = 0
}

// GetPushStatusText 获取推送状态文本
func (ioc *IOCIntelligence) GetPushStatusText() string {
	switch ioc.PushStatus {
	case "pushed":
		return "已推送"
	case "failed":
		return "推送失败"
	default:
		return "未推送"
	}
}

// IOCWhitelist IOC白名单模型
type IOCWhitelist struct {
	ID        uint   `gorm:"primaryKey" json:"id"`
	IOC       string `gorm:"size:255;not null;uniqueIndex" json:"ioc"`
	IOCType   string `gorm:"size:20;not null;index" json:"iocType"` // ip, domain, hash, url
	Remark    string `gorm:"size:500" json:"remark"`
	CreatedBy string `gorm:"size:100" json:"createdBy"`
	CreatedAt int64  `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt int64  `gorm:"autoUpdateTime" json:"updatedAt"`
}

// TableName 指定表名
func (IOCWhitelist) TableName() string {
	return "ioc_whitelists"
}

// GetCreatedTime 获取创建时间
func (ioc *IOCWhitelist) GetCreatedTime() time.Time {
	return time.Unix(ioc.CreatedAt, 0)
}

// GetUpdatedTime 获取更新时间
func (ioc *IOCWhitelist) GetUpdatedTime() time.Time {
	return time.Unix(ioc.UpdatedAt, 0)
}

// DataInterface 数据接口模型 - 数据采集模块的配置模型
// 定义各种数据源的采集配置和调度信息
type DataInterface struct {
	ID          uint   `gorm:"primaryKey" json:"id"`
	Name        string `gorm:"size:100;not null" json:"name"`
	Type        string `gorm:"size:50;not null;index" json:"type"`
	Description string `gorm:"size:500" json:"description"`
	Config      string `gorm:"type:text" json:"config"` // JSON格式配置
	Status      string `gorm:"size:20;not null;index" json:"status"` // enabled, disabled
	Interval    int    `gorm:"not null;default:3600" json:"interval"` // 执行间隔（秒）
	LastRunAt   int64  `gorm:"default:0" json:"lastRunAt"`
	CreatedAt   int64  `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt   int64  `gorm:"autoUpdateTime" json:"updatedAt"`

	// 采集配置
	CollectionEnabled  bool   `gorm:"default:false" json:"collection_enabled"`               // 是否启用定时采集
	CollectionFreq     string `gorm:"size:50;default:manual" json:"collection_freq"`        // 采集频率：manual, hourly, daily, weekly, custom
	CollectionInterval int    `gorm:"default:60" json:"collection_interval"`                 // 自定义采集间隔（分钟）
	TimeRangeType      string `gorm:"size:20;default:relative" json:"time_range_type"`       // 时间范围类型：relative, absolute
	TimeRangeValue     int    `gorm:"default:3600" json:"time_range_value"`                  // 相对时间范围值（秒）
	StartTime          string `gorm:"size:50" json:"start_time"`                             // 绝对开始时间
	EndTime            string `gorm:"size:50" json:"end_time"`                               // 绝对结束时间

	// 统计信息
	LastRunTime       *time.Time `gorm:"type:datetime" json:"last_run_time"`                   // 最后执行时间
	LastRunStatus     string    `gorm:"size:20" json:"last_run_status"`                        // 最后执行状态：success, failed, running
	LastRunMessage    string    `gorm:"type:text" json:"last_run_message"`                     // 最后执行消息
	TotalRuns         int       `gorm:"default:0" json:"total_runs"`                           // 总执行次数
	SuccessRuns       int       `gorm:"default:0" json:"success_runs"`                         // 成功执行次数
	FailedRuns        int       `gorm:"default:0" json:"failed_runs"`                          // 失败执行次数
	LastDataCount     int       `gorm:"default:0" json:"last_data_count"`                      // 最后一次采集的数据量
	TotalDataCount    int       `gorm:"default:0" json:"total_data_count"`                     // 总采集数据量

	// 关联关系
	Logs []DataInterfaceLog `gorm:"foreignKey:InterfaceID;constraint:OnDelete:CASCADE" json:"logs,omitempty"`
}

// TableName 指定表名
func (DataInterface) TableName() string {
	return "data_interfaces"
}

// IsEnabled 检查是否启用
func (di *DataInterface) IsEnabled() bool {
	return di.Status == "enabled"
}

// IsDisabled 检查是否禁用
func (di *DataInterface) IsDisabled() bool {
	return di.Status == "disabled"
}

// GetLastRunTime 获取最后运行时间
func (di *DataInterface) GetLastRunTime() time.Time {
	if di.LastRunAt == 0 {
		return time.Time{}
	}
	return time.Unix(di.LastRunAt, 0)
}

// UpdateLastRunTime 更新最后运行时间
func (di *DataInterface) UpdateLastRunTime() {
	di.LastRunAt = time.Now().Unix()
}

// GetCreatedTime 获取创建时间
func (di *DataInterface) GetCreatedTime() time.Time {
	return time.Unix(di.CreatedAt, 0)
}

// GetUpdatedTime 获取更新时间
func (di *DataInterface) GetUpdatedTime() time.Time {
	return time.Unix(di.UpdatedAt, 0)
}

// DataInterfaceLog 数据接口日志模型
type DataInterfaceLog struct {
	ID          uint   `gorm:"primaryKey" json:"id"`
	InterfaceID uint   `gorm:"not null;index" json:"interfaceId"`
	Status      string `gorm:"size:20;not null;index" json:"status"` // running, success, failed
	Message     string `gorm:"type:text" json:"message"`
	StartedAt   int64  `gorm:"default:0" json:"startedAt"`    // 开始时间
	EndedAt     int64  `gorm:"default:0" json:"endedAt"`      // 结束时间
	Duration    int    `gorm:"default:0" json:"duration"`     // 执行时长（秒）
	DataCount   int    `gorm:"default:0" json:"dataCount"`    // 处理的数据量
	ProcessedAt int64  `gorm:"not null" json:"processedAt"`
	CreatedAt   int64  `gorm:"autoCreateTime" json:"createdAt"`

	// 关联关系
	Interface DataInterface `gorm:"foreignKey:InterfaceID" json:"interface,omitempty"`
}

// TableName 指定表名
func (DataInterfaceLog) TableName() string {
	return "data_interface_logs"
}

// IsSuccess 检查是否成功
func (dil *DataInterfaceLog) IsSuccess() bool {
	return dil.Status == "success"
}

// IsRunning 检查是否正在运行
func (dil *DataInterfaceLog) IsRunning() bool {
	return dil.Status == "running"
}

// IsFailed 检查是否失败
func (dil *DataInterfaceLog) IsFailed() bool {
	return dil.Status == "failed"
}

// MarkAsStarted 标记为开始执行
func (dil *DataInterfaceLog) MarkAsStarted() {
	dil.Status = "running"
	dil.StartedAt = time.Now().Unix()
	dil.ProcessedAt = dil.StartedAt
}

// MarkAsCompleted 标记为完成（成功或失败）
func (dil *DataInterfaceLog) MarkAsCompleted(status, message string, dataCount int) {
	dil.Status = status
	dil.Message = message
	dil.DataCount = dataCount
	dil.EndedAt = time.Now().Unix()
	dil.ProcessedAt = dil.EndedAt

	// 计算执行时长
	if dil.StartedAt > 0 {
		dil.Duration = int(dil.EndedAt - dil.StartedAt)
	}
}

// GetStartedTime 获取开始时间
func (dil *DataInterfaceLog) GetStartedTime() time.Time {
	if dil.StartedAt == 0 {
		return time.Time{}
	}
	return time.Unix(dil.StartedAt, 0)
}

// GetEndedTime 获取结束时间
func (dil *DataInterfaceLog) GetEndedTime() time.Time {
	if dil.EndedAt == 0 {
		return time.Time{}
	}
	return time.Unix(dil.EndedAt, 0)
}

// GetProcessedTime 获取处理时间
func (dil *DataInterfaceLog) GetProcessedTime() time.Time {
	return time.Unix(dil.ProcessedAt, 0)
}

// GetCreatedTime 获取创建时间
func (dil *DataInterfaceLog) GetCreatedTime() time.Time {
	return time.Unix(dil.CreatedAt, 0)
}

// ProductionStrategy 生产策略模型 - 情报生产模块的配置模型
// 定义从源数据生成IOC情报的策略和规则
type ProductionStrategy struct {
	ID                     uint   `gorm:"primaryKey" json:"id"`
	Name                   string `gorm:"size:100;not null" json:"name"`
	Description            string `gorm:"size:500" json:"description"`
	Status                 string `gorm:"size:20;not null;index" json:"status"` // enabled, disabled
	AttackCountThreshold   int    `gorm:"default:3" json:"attackCountThreshold"`
	ThreatScoreThreshold   int    `gorm:"default:2" json:"threatScoreThreshold"`
	EnableThreatScoring    bool   `gorm:"default:true" json:"enableThreatScoring"`
	RiskLevelFilter        string `gorm:"type:text" json:"riskLevelFilter"`

	// 有效期配置
	DefaultValidityDays    int    `gorm:"default:30" json:"defaultValidityDays"`       // 默认有效期天数（1-100天）
	EnableValidityControl  bool   `gorm:"default:true" json:"enableValidityControl"`   // 是否启用有效期控制

	// 定时任务配置（替换原数据质量配置）
	ScheduleEnabled        bool   `gorm:"default:false" json:"scheduleEnabled"`        // 是否启用定时任务
	ScheduleInterval       int    `gorm:"default:60" json:"scheduleInterval"`          // 定时任务间隔（分钟）
	LastRunTime            *int64 `gorm:"default:null" json:"lastRunTime"`             // 上次运行时间
	NextRunTime            *int64 `gorm:"default:null" json:"nextRunTime"`             // 下次运行时间

	TimeRangeHours         int    `gorm:"default:24" json:"timeRangeHours"`
	AllowedCountries       string `gorm:"type:text" json:"allowedCountries"`
	BlockedCountries       string `gorm:"type:text" json:"blockedCountries"`
	AllowedIPRanges        string `gorm:"type:text" json:"allowedIPRanges"`
	BlockedIPRanges        string `gorm:"type:text" json:"blockedIPRanges"`
	CreatedBy              string `gorm:"size:100" json:"createdBy"`
	CreatedAt              int64  `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt              int64  `gorm:"autoUpdateTime" json:"updatedAt"`
}

// TableName 指定表名
func (ProductionStrategy) TableName() string {
	return "production_strategies"
}

// IsEnabled 检查是否启用
func (ps *ProductionStrategy) IsEnabled() bool {
	return ps.Status == "enabled"
}

// GetRiskLevelFilters 获取风险等级过滤器
func (ps *ProductionStrategy) GetRiskLevelFilters() []string {
	if ps.RiskLevelFilter == "" {
		return []string{}
	}
	return strings.Split(ps.RiskLevelFilter, ",")
}

// GetCreatedTime 获取创建时间
func (ps *ProductionStrategy) GetCreatedTime() time.Time {
	return time.Unix(ps.CreatedAt, 0)
}

// GetUpdatedTime 获取更新时间
func (ps *ProductionStrategy) GetUpdatedTime() time.Time {
	return time.Unix(ps.UpdatedAt, 0)
}

// IsValidityControlEnabled 检查是否启用有效期控制
func (ps *ProductionStrategy) IsValidityControlEnabled() bool {
	return ps.EnableValidityControl
}

// GetValidityDays 获取有效期天数
func (ps *ProductionStrategy) GetValidityDays() int {
	if ps.DefaultValidityDays <= 0 || ps.DefaultValidityDays > 100 {
		return 30 // 默认30天
	}
	return ps.DefaultValidityDays
}

// ValidateValidityDays 验证有效期天数
func (ps *ProductionStrategy) ValidateValidityDays() error {
	if ps.DefaultValidityDays < 1 || ps.DefaultValidityDays > 100 {
		return fmt.Errorf("有效期天数必须在1-100天之间")
	}
	return nil
}

// ApplyValidityToIOC 将有效期设置应用到IOC情报
func (ps *ProductionStrategy) ApplyValidityToIOC(ioc *IOCIntelligence) {
	if ps.IsValidityControlEnabled() {
		ioc.SetValidityPeriod(ps.GetValidityDays())
	} else {
		// 如果未启用有效期控制，设置为永不过期
		ioc.SetValidityPeriod(0)
	}
}

// ProcessedUUID 已处理UUID记录表（用于去重）
type ProcessedUUID struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	UUID        string    `gorm:"uniqueIndex;size:100;not null" json:"uuid"`        // UUID
	SourceType  string    `gorm:"size:50;not null" json:"source_type"`              // 数据源类型：cccc_black_tech, es_alarm等
	ProcessedAt time.Time `gorm:"not null" json:"processed_at"`                     // 处理时间
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`                 // 创建时间
}

// IOCIntelligenceData IOC情报源数据模型 - 数据采集模块的核心数据模型
// 存储从各种数据接口采集并初步处理后的原始攻击数据，作为生产IOC情报的数据源
type IOCIntelligenceData struct {
	ID               uint    `gorm:"primaryKey" json:"id"`
	UUID             string  `gorm:"size:100;unique;not null;index" json:"uuid"`     // 数据源唯一标识（如CCCC黑科技的UUID）
	AttackIP         string  `gorm:"size:45;not null;index" json:"attackIp"`         // 攻击IP
	VictimIP         string  `gorm:"size:45;not null;index" json:"victimIp"`         // 受害IP
	VictimIPs        string  `gorm:"type:text" json:"victimIps"`                     // 受害IP列表（JSON格式）
	SourceLabel      string  `gorm:"size:50;index" json:"sourceLabel"`               // 来源标签
	Category         string  `gorm:"size:100;index" json:"category"`                 // 攻击类别
	AttackCount      int     `gorm:"not null;default:0;index" json:"attackCount"`    // 攻击次数
	FirstAttackTime  string  `gorm:"size:20" json:"firstAttackTime"`                 // 首次攻击时间
	LastAttackTime   string  `gorm:"size:20" json:"lastAttackTime"`                  // 最后攻击时间
	AttackTimes      string  `gorm:"type:text" json:"attackTimes"`                   // 攻击时间列表（JSON格式）
	ThreatScore      float64 `gorm:"type:decimal(4,2);default:0" json:"threatScore"` // 威胁评分
	ProcessedStatus  string  `gorm:"size:20;default:'unprocessed';index" json:"processedStatus"` // 处理状态: unprocessed, processed
	ProcessedAt      int64   `gorm:"default:0" json:"processedAt"`                   // 处理时间
	CreatedAt        int64   `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt        int64   `gorm:"autoUpdateTime" json:"updatedAt"`
}

// TableName 指定表名 - IOC情报源数据表
func (IOCIntelligenceData) TableName() string {
	return "ioc_source_data"
}

// GetCreatedTime 获取创建时间
func (iid *IOCIntelligenceData) GetCreatedTime() time.Time {
	return time.Unix(iid.CreatedAt, 0)
}

// GetUpdatedTime 获取更新时间
func (iid *IOCIntelligenceData) GetUpdatedTime() time.Time {
	return time.Unix(iid.UpdatedAt, 0)
}

// IsProcessed 检查是否已处理
func (iid *IOCIntelligenceData) IsProcessed() bool {
	return iid.ProcessedStatus == "processed"
}

// MarkAsProcessed 标记为已处理
func (iid *IOCIntelligenceData) MarkAsProcessed() {
	iid.ProcessedStatus = "processed"
	iid.ProcessedAt = time.Now().Unix()
}

// CalculateThreatScore 计算威胁评分
func (iid *IOCIntelligenceData) CalculateThreatScore(severities map[string]int) {
	iid.ThreatScore = CalculateSimpleThreatScore(severities, iid.AttackCount)
}

// CalculateSimpleThreatScore 计算简化威胁评分（基于危害等级和攻击次数）
func CalculateSimpleThreatScore(severities map[string]int, attackCount int) float64 {
	// 基础分数：根据最高严重程度计算
	var baseScore float64 = 0
	for severity := range severities {
		var score float64
		switch severity {
		case "严重":
			score = 9.0
		case "高危":
			score = 8.0
		case "中危":
			score = 5.0
		case "低危":
			score = 2.0
		case "信息":
			score = 1.0
		default:
			score = 3.0
		}
		if score > baseScore {
			baseScore = score
		}
	}

	// 频次系数
	var frequencyMultiplier float64 = 1.0
	if attackCount >= 100 {
		frequencyMultiplier = 1.8
	} else if attackCount >= 50 {
		frequencyMultiplier = 1.6
	} else if attackCount >= 20 {
		frequencyMultiplier = 1.4
	} else if attackCount >= 10 {
		frequencyMultiplier = 1.2
	} else if attackCount >= 5 {
		frequencyMultiplier = 1.1
	}

	// 计算最终评分
	finalScore := baseScore * frequencyMultiplier

	// 限制在0-10范围内
	if finalScore > 10.0 {
		finalScore = 10.0
	}

	return finalScore
}

// MapThreatScoreToRiskLevel 将威胁评分映射为风险等级
func MapThreatScoreToRiskLevel(score float64) string {
	if score >= 8.0 {
		return "严重"
	} else if score >= 6.0 {
		return "高危"
	} else if score >= 4.0 {
		return "中危"
	} else if score >= 2.0 {
		return "低危"
	} else {
		return "信息"
	}
}

// IOC类型常量
const (
	IOCTypeIP     = "ip"
	IOCTypeDomain = "domain"
	IOCTypeHash   = "hash"
	IOCTypeURL    = "url"
)

// GetAllIOCTypes 获取所有IOC类型
func GetAllIOCTypes() []string {
	return []string{
		IOCTypeIP,
		IOCTypeDomain,
		IOCTypeHash,
		IOCTypeURL,
	}
}

// IsValidIOCType 检查IOC类型是否有效
func IsValidIOCType(iocType string) bool {
	validTypes := GetAllIOCTypes()
	for _, t := range validTypes {
		if t == iocType {
			return true
		}
	}
	return false
}

// 风险等级常量
const (
	RiskLevelCritical = "严重"
	RiskLevelHigh     = "高危"
	RiskLevelMedium   = "中危"
	RiskLevelLow      = "低危"
	RiskLevelInfo     = "信息"
)

// GetAllRiskLevels 获取所有风险等级
func GetAllRiskLevels() []string {
	return []string{
		RiskLevelCritical,
		RiskLevelHigh,
		RiskLevelMedium,
		RiskLevelLow,
		RiskLevelInfo,
	}
}

// IsValidRiskLevel 检查风险等级是否有效
func IsValidRiskLevel(riskLevel string) bool {
	validLevels := GetAllRiskLevels()
	for _, l := range validLevels {
		if l == riskLevel {
			return true
		}
	}
	return false
}

// 数据接口状态常量
const (
	DataInterfaceStatusEnabled  = "enabled"
	DataInterfaceStatusDisabled = "disabled"
)

// GetAllDataInterfaceStatuses 获取所有数据接口状态
func GetAllDataInterfaceStatuses() []string {
	return []string{
		DataInterfaceStatusEnabled,
		DataInterfaceStatusDisabled,
	}
}

// IsValidDataInterfaceStatus 检查数据接口状态是否有效
func IsValidDataInterfaceStatus(status string) bool {
	validStatuses := GetAllDataInterfaceStatuses()
	for _, s := range validStatuses {
		if s == status {
			return true
		}
	}
	return false
}

// ProductionStrategyLog 生产策略执行日志模型
type ProductionStrategyLog struct {
	ID          uint   `gorm:"primaryKey" json:"id"`
	StrategyID  uint   `gorm:"not null;index" json:"strategyId"`
	Status      string `gorm:"size:20;not null" json:"status"` // running, success, failed
	Message     string `gorm:"type:text" json:"message"`
	ProcessedAt int64  `gorm:"not null" json:"processedAt"`
}

// TableName 指定表名
func (ProductionStrategyLog) TableName() string {
	return "production_strategy_logs"
}
