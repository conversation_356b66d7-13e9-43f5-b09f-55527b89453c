// Package IOC_Feed 提供微步威胁情报API的接口实现和情报查询服务
package IOC_Feed

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"vulnerability_push/internal/config"
	"vulnerability_push/internal/utils"
)

// WeibuClient 微步威胁情报请求客户端
type WeibuClient struct {
	host    string // API域名，如 api.threatbook.cn
	apiKey  string // API密钥
	timeout time.Duration
	client  *http.Client
}

// NewWeibuClient 创建新的微步威胁情报客户端
func NewWeibuClient(host, apiKey string) *WeibuClient {
	return &WeibuClient{
		host:   host,
		apiKey: apiKey,
		timeout: 10 * time.Second, // 默认10秒超时
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// SetTimeout 设置请求超时时间
func (c *WeibuClient) SetTimeout(timeout time.Duration) *WeibuClient {
	c.timeout = timeout
	c.client.Timeout = timeout
	return c
}

// WeibuIPReputationResponse 微步IP信誉查询响应结构
type WeibuIPReputationResponse struct {
	ResponseCode int                    `json:"response_code"` // 响应状态码：0成功，其他为错误
	VerboseMsg   string                 `json:"verbose_msg"`   // 响应消息
	Data         map[string]interface{} `json:"data"`          // 响应数据，IP为key的映射
}

// WeibuIPReputationRecord IP信誉记录结构
type WeibuIPReputationRecord struct {
	IsMalicious      string                 `json:"is_malicious"`      // 是否恶意
	ConfidenceLevel  string                 `json:"confidence_level"`  // 可信度
	Severity         string                 `json:"severity"`          // 严重程度
	Judgments        []string               `json:"judgments"`         // 威胁类型
	TagsClasses      []WeibuTagClass        `json:"tags_classes"`      // 标签类别
	Basic            WeibuBasicInfo         `json:"basic"`             // 基础信息
	UpdateTime       string                 `json:"update_time"`       // 更新时间
}

// WeibuTagClass 标签类别结构
type WeibuTagClass struct {
	TagsType string      `json:"tags_type"` // 标签类别
	Tags     interface{} `json:"tags"`      // 标签信息（可能是数组或字符串）
}

// WeibuBasicInfo 基础信息结构
type WeibuBasicInfo struct {
	Location WeibuLocation `json:"location"` // 位置信息
	Carrier  string        `json:"carrier"`  // 运营商
}

// WeibuLocation 位置信息结构
type WeibuLocation struct {
	Country  string `json:"country"`  // 国家
	Province string `json:"province"` // 省份
	City     string `json:"city"`     // 城市
}

// QueryIPReputationBatch 批量查询IP信誉（参考Python代码实现）
func (c *WeibuClient) QueryIPReputationBatch(ctx context.Context, ipList []string, lang string) (*WeibuIPReputationResponse, error) {
	// 参数验证
	if c.apiKey == "" {
		return nil, fmt.Errorf("请填写API密钥")
	}
	if len(ipList) == 0 {
		return nil, fmt.Errorf("IP列表不能为空")
	}
	if len(ipList) > 100 {
		return nil, fmt.Errorf("批量查询最多支持100个IP")
	}

	// 构建请求URL
	requestURL := fmt.Sprintf("https://%s/v3/scene/ip_reputation", c.host)

	// 构建查询参数（参考Python代码的params）
	params := url.Values{}
	params.Set("apikey", c.apiKey)
	params.Set("resource", strings.Join(ipList, ",")) // 批量查询多个IP，用逗号分隔
	if lang != "" {
		params.Set("lang", lang) // 可选，返回中文结果
	}

	// 创建GET请求（参考Python代码使用GET方法）
	fullURL := fmt.Sprintf("%s?%s", requestURL, params.Encode())
	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 调试输出请求信息
	utils.Infof("微步请求URL: %s\n", fullURL)
	utils.Infof("微步请求方法: GET\n")
	utils.Infof("微步查询IP数量: %d\n", len(ipList))
	utils.Infof("微步查询IP列表: %v\n", ipList)

	// 执行请求，支持重试（参考天际友盟的重试逻辑）
	var resp *http.Response
	for i := 0; i < 3; i++ {
		resp, err = c.client.Do(req)
		if err != nil {
			if i == 2 { // 最后一次重试
				return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
			}
			continue
		}

		// 检查状态码
		if resp.StatusCode == 200 {
			break
		} else if resp.StatusCode >= 500 {
			resp.Body.Close()
			if i == 2 { // 最后一次重试
				return nil, fmt.Errorf("服务器错误，状态码: %d", resp.StatusCode)
			}
			continue
		} else {
			// 客户端错误，不重试
			break
		}
	}

	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应数据
	var response WeibuIPReputationResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %v, 响应内容: %s", err, string(body))
	}

	// 检查业务状态码（微步API成功状态码为0）
	if response.ResponseCode != 0 {
		return nil, fmt.Errorf("API返回业务错误: code=%d, message=%s", response.ResponseCode, response.VerboseMsg)
	}

	return &response, nil
}

// =============================================================================
// 微步威胁情报查询服务
// =============================================================================

// WeibuIntelligenceService 微步威胁情报查询服务
type WeibuIntelligenceService struct {
	client *WeibuClient
	config *config.WeibuConfig
}

// WeibuIntelligenceResult 微步威胁情报查询结果
type WeibuIntelligenceResult struct {
	Success      bool        `json:"success"`
	Data         interface{} `json:"data"`
	ErrorMessage string      `json:"errorMessage,omitempty"`
	QueryTime    int64       `json:"queryTime"`
}

// WeibuBatchResult 微步批量查询结果
type WeibuBatchResult struct {
	Success      bool                             `json:"success"`
	Results      map[string]*WeibuIntelligenceResult `json:"results"`     // IP -> 查询结果的映射
	ErrorMessage string                           `json:"errorMessage,omitempty"`
	QueryTime    int64                            `json:"queryTime"`
}

// NewWeibuIntelligenceService 创建微步威胁情报查询服务
func NewWeibuIntelligenceService(cfg *config.WeibuConfig) *WeibuIntelligenceService {
	if cfg == nil {
		// 使用默认配置
		cfg = &config.WeibuConfig{
			Enabled:      true,
			Host:         "api.threatbook.cn",
			ApiKey:       "your_api_key_here",
			Timeout:      10,
			MaxBatchSize: 100,
			RetryCount:   3,
			Lang:         "zh",
		}
	}

	client := NewWeibuClient(cfg.Host, cfg.ApiKey)
	client.SetTimeout(time.Duration(cfg.Timeout) * time.Second)

	return &WeibuIntelligenceService{
		client: client,
		config: cfg,
	}
}

// QueryIPReputationBatch 批量查询IP信誉（参考Python代码的批量处理逻辑）
func (s *WeibuIntelligenceService) QueryIPReputationBatch(ipList []string) *WeibuBatchResult {
	result := &WeibuBatchResult{
		QueryTime: time.Now().Unix(),
		Results:   make(map[string]*WeibuIntelligenceResult),
	}

	if len(ipList) == 0 {
		result.Success = false
		result.ErrorMessage = "IP列表不能为空"
		return result
	}

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(s.config.Timeout*2)*time.Second)
	defer cancel()

	// 按批次处理（参考Python代码的batch_size处理）
	batchSize := s.config.MaxBatchSize
	successCount := 0

	for i := 0; i < len(ipList); i += batchSize {
		end := i + batchSize
		if end > len(ipList) {
			end = len(ipList)
		}
		
		batch := ipList[i:end]
		utils.Infof("📡 微步批量查询第 %d 批，共 %d 个IP\n", (i/batchSize)+1, len(batch))

		// 调用微步API
		response, err := s.client.QueryIPReputationBatch(ctx, batch, s.config.Lang)
		if err != nil {
			utils.Infof("❌ 微步批量查询失败: %v\n", err)
			// 为该批次的所有IP设置错误结果
			for _, ip := range batch {
				result.Results[ip] = &WeibuIntelligenceResult{
					Success:      false,
					ErrorMessage: fmt.Sprintf("批量查询失败: %v", err),
					QueryTime:    result.QueryTime,
				}
			}
			continue
		}

		// 处理响应结果（参考Python代码的results.update(data.get("data", {}))）
		if response.Data != nil {
			for ip, record := range response.Data {
				result.Results[ip] = &WeibuIntelligenceResult{
					Success:   true,
					Data:      record,
					QueryTime: result.QueryTime,
				}
				successCount++
				utils.Infof("✅ %s: 微步查询成功\n", ip)
			}
		}

		// 为批次中未返回结果的IP设置未找到状态
		for _, ip := range batch {
			if _, exists := result.Results[ip]; !exists {
				result.Results[ip] = &WeibuIntelligenceResult{
					Success:      false,
					ErrorMessage: "未找到该IP的信誉信息",
					QueryTime:    result.QueryTime,
				}
				utils.Infof("⚠️ %s: 未找到信誉信息\n", ip)
			}
		}

		// 避免频率限制（参考Python代码的time.sleep(1)）
		if i+batchSize < len(ipList) {
			utils.Infof("⏳ 等待1秒避免频率限制...\n")
			time.Sleep(1 * time.Second)
		}
	}

	// 设置总体结果状态
	result.Success = successCount > 0
	if !result.Success && len(result.Results) > 0 {
		// 如果所有查询都失败，设置总体错误信息
		for _, res := range result.Results {
			if res.ErrorMessage != "" {
				result.ErrorMessage = res.ErrorMessage
				break
			}
		}
	}

	utils.Infof("📊 微步批量查询完成: 成功 %d/%d\n", successCount, len(ipList))
	return result
}

// QueryIPReputation 查询单个IP信誉
func (s *WeibuIntelligenceService) QueryIPReputation(ip string) *WeibuIntelligenceResult {
	// 调用批量查询，只传入一个IP
	batchResult := s.QueryIPReputationBatch([]string{ip})
	if result, exists := batchResult.Results[ip]; exists {
		return result
	}

	// 如果批量查询失败，返回失败结果
	return &WeibuIntelligenceResult{
		Success:      false,
		ErrorMessage: batchResult.ErrorMessage,
		QueryTime:    batchResult.QueryTime,
	}
}

// SerializeData 序列化数据为JSON字符串
func (r *WeibuIntelligenceResult) SerializeData() (string, error) {
	if r.Data == nil {
		return "", nil
	}

	dataBytes, err := json.Marshal(r.Data)
	if err != nil {
		return "", fmt.Errorf("序列化微步数据失败: %v", err)
	}

	return string(dataBytes), nil
}

// GetQueryStatus 获取查询状态
func (r *WeibuIntelligenceResult) GetQueryStatus() string {
	if r.Success {
		return "success"
	}
	return "failed"
}

// ExportToCSV 导出查询结果为CSV格式（参考Python代码的CSV导出功能）
func (s *WeibuIntelligenceService) ExportToCSV(results map[string]*WeibuIntelligenceResult, outputFile string) error {
	// 这里可以实现CSV导出功能，类似Python代码中的CSV写入
	// 由于Go的CSV处理相对复杂，这里先提供接口，后续可以扩展
	return fmt.Errorf("CSV导出功能待实现")
}

// =============================================================================
// 全局服务实例管理
// =============================================================================

// 全局微步威胁情报服务实例
var globalWeibuService *WeibuIntelligenceService

// InitGlobalWeibuService 初始化全局微步威胁情报服务
func InitGlobalWeibuService() error {
	// 从配置文件加载配置
	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		return fmt.Errorf("加载配置文件失败: %v", err)
	}

	if !cfg.Weibu.Enabled {
		return fmt.Errorf("微步威胁情报服务已禁用")
	}

	globalWeibuService = NewWeibuIntelligenceService(&cfg.Weibu)
	return nil
}

// GetGlobalWeibuService 获取全局微步威胁情报服务
func GetGlobalWeibuService() *WeibuIntelligenceService {
	if globalWeibuService == nil {
		// 如果没有初始化，自动初始化
		InitGlobalWeibuService()
	}
	return globalWeibuService
}

// QueryWeibuIPReputationGlobal 使用全局服务查询IP信誉
func QueryWeibuIPReputationGlobal(ip string) *WeibuIntelligenceResult {
	service := GetGlobalWeibuService()
	if service == nil {
		return &WeibuIntelligenceResult{
			Success:      false,
			ErrorMessage: "微步威胁情报服务不可用",
			QueryTime:    time.Now().Unix(),
		}
	}
	return service.QueryIPReputation(ip)
}

// QueryWeibuIPReputationBatchGlobal 使用全局服务批量查询IP信誉
func QueryWeibuIPReputationBatchGlobal(ipList []string) *WeibuBatchResult {
	service := GetGlobalWeibuService()
	if service == nil {
		return &WeibuBatchResult{
			Success:      false,
			ErrorMessage: "微步威胁情报服务不可用",
			QueryTime:    time.Now().Unix(),
			Results:      make(map[string]*WeibuIntelligenceResult),
		}
	}
	return service.QueryIPReputationBatch(ipList)
}
