package crawlers

import (
	"context"
	"sync"
)

// 采集器管理器
type CrawlerManager struct {
	crawlers []Grabber
	mutex    sync.Mutex
}

// 创建采集器管理器
func NewCrawlerManager() *CrawlerManager {
	return &CrawlerManager{
		crawlers: make([]<PERSON>rab<PERSON>, 0),
	}
}

// 注册采集器
func (m *CrawlerManager) RegisterCrawler(crawler Grabber) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.crawlers = append(m.crawlers, crawler)
}

// 获取所有采集器
func (m *CrawlerManager) GetAllCrawlers() []Grabber {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	result := make([]Grabber, len(m.crawlers))
	copy(result, m.crawlers)
	return result
}

// 获取所有采集源信息
func (m *CrawlerManager) GetAllProviders() []*Provider {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	providers := make([]*Provider, 0, len(m.crawlers))
	for _, crawler := range m.crawlers {
		providers = append(providers, crawler.ProviderInfo())
	}
	return providers
}

// 执行采集任务
func (m *CrawlerManager) RunCrawlTask(ctx context.Context, pageLimit int, startDate string, endDate string) ([]*VulnInfo, error) {
	m.mutex.Lock()
	crawlers := make([]Grabber, len(m.crawlers))
	copy(crawlers, m.crawlers)
	m.mutex.Unlock()

	var allVulns []*VulnInfo
	var wg sync.WaitGroup
	var mu sync.Mutex

	for _, crawler := range crawlers {
		wg.Add(1)
		go func(c Grabber) {
			defer wg.Done()

			vulns, err := c.GetUpdate(ctx, pageLimit, startDate, endDate)
			if err != nil {
				// 记录错误日志，但不中断其他采集器
				return
			}

			var valuableVulns []*VulnInfo
			for _, vuln := range vulns {
				if c.IsValuable(vuln) {
					valuableVulns = append(valuableVulns, vuln)
				}
			}

			if len(valuableVulns) > 0 {
				mu.Lock()
				allVulns = append(allVulns, valuableVulns...)
				mu.Unlock()
			}
		}(crawler)
	}

	wg.Wait()
	return allVulns, nil
}
