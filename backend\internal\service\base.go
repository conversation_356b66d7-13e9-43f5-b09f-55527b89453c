package service

import (
	"gorm.io/gorm"
)

// BaseService 基础服务结构
type BaseService struct {
	DB *gorm.DB
}

// NewBaseService 创建基础服务
func NewBaseService(db *gorm.DB) *BaseService {
	return &BaseService{
		DB: db,
	}
}

// ServiceManager 服务管理器
type ServiceManager struct {
	DB                    *gorm.DB
	UserService          UserServiceInterface
	VulnerabilityService VulnerabilityServiceInterface
	CrawlerService       CrawlerServiceInterface
	PushService          PushServiceInterface
	IOCService           IOCServiceInterface
	DataInterfaceService DataInterfaceServiceInterface
}

// NewServiceManager 创建服务管理器
func NewServiceManager(db *gorm.DB) *ServiceManager {
	return &ServiceManager{
		DB:                    db,
		UserService:          NewUserService(db),
		// TODO: 实现其他服务
		// VulnerabilityService: NewVulnerabilityService(db),
		// CrawlerService:       NewCrawlerService(db),
		// PushService:          NewPushService(db),
		// IOCService:           NewIOCService(db),
		// DataInterfaceService: NewDataInterfaceService(db),
	}
}
