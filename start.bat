@echo off
echo ==== 威胁情报管理平台启动脚本 ====

REM 检查是否安装了Go
where go >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未安装Go语言环境。请安装Go 1.16或更高版本。
    pause
    exit /b 1
)

REM 检查Go版本
for /f "tokens=3" %%i in ('go version') do set GO_VERSION=%%i
echo 检测到Go版本: %GO_VERSION%

REM 确保当前目录是项目根目录
if not exist backend (
    echo 错误: 请在项目根目录运行此脚本。
    pause
    exit /b 1
)

if not exist frontend (
    echo 错误: 请在项目根目录运行此脚本。
    pause
    exit /b 1
)

REM 检查并安装后端依赖
echo 正在检查并安装后端依赖...
cd backend
go mod tidy
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 安装后端依赖失败。
    cd ..
    pause
    exit /b 1
)

REM 构建后端
echo 正在构建后端...
go build -o vulnerability_push_server.exe .
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 构建后端失败。
    cd ..
    pause
    exit /b 1
)

REM 检查前端依赖安装
echo 正在检查前端环境...
cd ..\frontend
if not exist node_modules (
    echo 正在安装前端依赖(这可能需要一些时间)...
    where npm >nul 2>nul
    if %ERRORLEVEL% EQU 0 (
        npm install
    ) else (
        where yarn >nul 2>nul
        if %ERRORLEVEL% EQU 0 (
            yarn install
        ) else (
            echo 警告: 未找到npm或yarn，无法安装前端依赖。请手动安装前端依赖。
        )
    )
)

REM 启动后端服务
echo 正在启动后端服务...
cd ..\backend
start /B vulnerability_push_server.exe

REM 输出系统信息
echo 前端部署说明:
echo 1. 开发模式: 在frontend目录中运行 'npm run serve' 或 'yarn serve'
echo 2. 生产模式: 在frontend目录中运行 'npm run build' 或 'yarn build'，然后将dist目录部署到Web服务器

echo.
echo 系统初始化完成!
echo - 后端API地址: http://localhost:8080
echo - 默认管理员用户名: admin (首次启动时会在控制台显示初始密码)
echo - 请查看后端服务窗口获取更多信息

cd ..
echo.
echo 按任意键关闭此窗口(后端服务将继续运行)...
pause 