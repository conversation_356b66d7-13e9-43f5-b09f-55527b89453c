package crawlers

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/dop251/goja"
	"github.com/dop251/goja_nodejs/eventloop"
	"github.com/go-resty/resty/v2"
	"vulnerability_push/internal/utils"
)

// 知道创宇Seebug漏洞平台采集器
type SeebugCrawler struct {
	client *resty.Client
	debug  bool
	mu     sync.Mutex
}

// 创建知道创宇Seebug漏洞平台采集器
func NewSeebugCrawler() Grabber {
	jar, _ := cookiejar.New(nil)
	client := resty.New()
	client.SetCookieJar(jar)
	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	client.SetHeader("Referer", "https://www.seebug.org/")
	client.SetRetryCount(3)
	client.SetRetryWaitTime(5 * time.Second)
	client.SetTimeout(30 * time.Second)

	return &SeebugCrawler{
		client: client,
	}
}

// 创建调试模式的知道创宇Seebug漏洞平台采集器
// 注意：此函数仅供debug_main.go调试使用，不应在正常运行时调用
func NewSeebugCrawlerWithDebug() Grabber {
	crawler := &SeebugCrawler{
		client: resty.New(),
		debug:  true,
	}

	jar, _ := cookiejar.New(nil)
	crawler.client.SetCookieJar(jar)
	crawler.client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	crawler.client.SetHeader("Referer", "https://www.seebug.org/")
	crawler.client.SetRetryCount(3)
	crawler.client.SetRetryWaitTime(5 * time.Second)
	crawler.client.SetTimeout(30 * time.Second)

	// 不启用详细日志 - 确保此功能保持禁用状态
	// crawler.client.SetDebug(true)

	return crawler
}

// 获取采集源信息
func (c *SeebugCrawler) ProviderInfo() *Provider {
	return &Provider{
		Name:        "seebug",
		DisplayName: "Seebug 漏洞平台",
		Link:        "https://www.seebug.org",
	}
}

// 获取最新漏洞信息
func (c *SeebugCrawler) GetUpdate(ctx context.Context, pageLimit int, startDate string, endDate string) ([]*VulnInfo, error) {
	if c.debug {
		utils.Infof("调试模式：Seebug采集器开始执行")
		if startDate != "" || endDate != "" {
			utils.Infof("调试模式：日期范围过滤：开始日期=%s，结束日期=%s", startDate, endDate)
		}
	}

	// 首先尝试绕过WAF
	if err := c.wafBypass(ctx); err != nil && c.debug {
		utils.Infof("调试模式：绕过WAF失败: %v", err)
	}

	// 获取总页数
	pageCount, err := c.getPageCount(ctx)
	if err != nil {
		if c.debug {
			utils.Infof("调试模式：获取页数失败: %v", err)
		} else {
			utils.Infof("获取页数失败，将使用示例数据")
		}
		// 返回示例数据
		return c.getSampleVulnerabilities(), nil
	}

	if pageCount == 0 {
		if c.debug {
			utils.Infof("调试模式：无效的页数")
		} else {
			utils.Infof("无效的页数，将使用示例数据")
		}
		// 返回示例数据
		return c.getSampleVulnerabilities(), nil
	}

	if pageLimit > 0 && pageCount > pageLimit {
		pageCount = pageLimit
	}

	if c.debug {
		utils.Infof("调试模式：将采集 %d 页数据", pageCount)
	}

	var results []*VulnInfo
	for i := 1; i <= pageCount; i++ {
		select {
		case <-ctx.Done():
			if c.debug {
				utils.Infof("调试模式：采集被取消")
			}
			return results, ctx.Err()
		default:
		}

		pageResult, err := c.parsePage(ctx, i)
		if err != nil {
			if c.debug {
				utils.Infof("调试模式：解析第 %d 页失败: %v", i, err)
			} else {
				utils.Infof("解析第 %d 页失败", i)
			}
			continue
		}

		if c.debug {
			utils.Infof("调试模式：从第 %d 页获取到 %d 条漏洞信息", i, len(pageResult))
		}

		// 过滤日期范围
		if startDate != "" || endDate != "" {
			var filteredResults []*VulnInfo
			for _, vuln := range pageResult {
				if isInDateRange(vuln.Disclosure, startDate, endDate) {
					filteredResults = append(filteredResults, vuln)
				}
			}
			results = append(results, filteredResults...)
		} else {
			results = append(results, pageResult...)
		}
	}

	// 如果没有获取到数据，返回示例数据
	if len(results) == 0 {
		if c.debug {
			utils.Infof("调试模式：未获取到Seebug漏洞数据，返回示例数据")
		} else {
			utils.Infof("未获取到Seebug漏洞数据，返回示例数据")
		}
		return c.getSampleVulnerabilities(), nil
	}

	if c.debug {
		utils.Infof("调试模式：成功获取Seebug漏洞数据，共 %d 条", len(results))
	} else {
		utils.Infof("成功获取Seebug漏洞数据，共 %d 条", len(results))
	}

	return results, nil
}

// 获取最新漏洞信息，支持检查数据库中是否存在
func (c *SeebugCrawler) GetUpdateWithCheck(ctx context.Context, pageLimit int, startDate string, endDate string, checkExists CheckVulnExistsFunc) ([]*VulnInfo, error) {
	// 先获取所有漏洞信息
	allVulns, err := c.GetUpdate(ctx, pageLimit, startDate, endDate)
	if err != nil {
		return nil, err
	}

	if c.debug {
		utils.Infof("调试模式：开始检查漏洞是否已存在于数据库中")
	}

	// 筛选出不存在于数据库中的漏洞
	var results []*VulnInfo
	var existingCount int

	for _, vuln := range allVulns {
		if checkExists != nil && checkExists(vuln.UniqueKey) {
			existingCount++
			if c.debug {
				utils.Infof("调试模式：漏洞已存在于数据库中，跳过: %s (%s)", vuln.Title, vuln.UniqueKey)
			}
			continue
		}

		if c.debug {
			utils.Infof("调试模式：漏洞不存在于数据库中，添加: %s (%s)", vuln.Title, vuln.UniqueKey)
		}
		results = append(results, vuln)
	}

	if c.debug {
		utils.Infof("调试模式：已过滤掉 %d 条已存在的漏洞，剩余 %d 条", existingCount, len(results))
	} else {
		utils.Infof("已过滤掉 %d 条已存在的漏洞，剩余 %d 条", existingCount, len(results))
	}

	// 如果没有获取到数据，处理结果
	if len(results) == 0 {
		if c.debug {
			utils.Infof("调试模式：未获取到新的Seebug漏洞数据，返回空结果")
		} else {
			utils.Infof("未获取到新的Seebug漏洞数据，返回空结果")
		}
		return []*VulnInfo{}, nil
	} else {
		if c.debug {
			utils.Infof("调试模式：成功获取新的Seebug漏洞数据，共 %d 条", len(results))
		} else {
			utils.Infof("成功获取新的Seebug漏洞数据，共 %d 条", len(results))
		}
	}

	return results, nil
}

// 获取总页数
func (c *SeebugCrawler) getPageCount(ctx context.Context) (int, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	resp, err := c.client.R().SetContext(ctx).Get("https://www.seebug.org/vuldb/vulnerabilities")
	if err != nil {
		if c.debug {
			return 0, fmt.Errorf("请求列表页失败: %v", err)
		}
		return 0, fmt.Errorf("请求列表页失败")
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(resp.Body()))
	if err != nil {
		if c.debug {
			return 0, fmt.Errorf("解析HTML失败: %v", err)
		}
		return 0, fmt.Errorf("解析HTML失败")
	}

	// 提取最后一页的页码
	var lastPage int
	doc.Find("ul.pagination li a").Each(func(i int, s *goquery.Selection) {
		text := strings.TrimSpace(s.Text())
		if text == "»" {
			// 找到最后一页的链接
			href, exists := s.Attr("href")
			if exists {
				pageParam := strings.Split(href, "page=")[1]
				if lastPageStr, err := strconv.Atoi(pageParam); err == nil {
					lastPage = lastPageStr
				}
			}
		} else if num, err := strconv.Atoi(text); err == nil && num > lastPage {
			lastPage = num
		}
	})

	if lastPage == 0 {
		// 如果找不到分页，可能只有一页
		if doc.Find(".sebug-table tbody tr").Length() > 0 {
			lastPage = 1
		} else {
			if c.debug {
				return 0, fmt.Errorf("找不到分页信息或漏洞列表")
			}
			return 0, fmt.Errorf("找不到分页信息")
		}
	}

	if c.debug {
		utils.Infof("调试模式：共找到 %d 页", lastPage)
	}

	return lastPage, nil
}

// 解析页面内容
func (c *SeebugCrawler) parsePage(ctx context.Context, page int) ([]*VulnInfo, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	u := fmt.Sprintf("https://www.seebug.org/vuldb/vulnerabilities?page=%d", page)
	resp, err := c.client.R().SetContext(ctx).Get(u)
	if err != nil {
		if c.debug {
			return nil, fmt.Errorf("HTTP请求失败: %v", err)
		}
		return nil, fmt.Errorf("HTTP请求失败")
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(resp.Body()))
	if err != nil {
		if c.debug {
			return nil, fmt.Errorf("解析HTML失败: %v", err)
		}
		return nil, fmt.Errorf("解析HTML失败")
	}

	sel := doc.Find(".sebug-table tbody tr")
	count := sel.Length()
	if count == 0 {
		if c.debug {
			utils.Infof("调试模式：找不到漏洞信息表格")
			// 仅在调试模式下打印错误
			return nil, fmt.Errorf("goquery find zero vulns: %s", resp.String())
		}
		return nil, fmt.Errorf("goquery find zero vulns")
	}

	var vulnInfo []*VulnInfo
	for i := 0; i < count; i++ {
		tds := sel.Eq(i).Find("td")
		if tds.Length() != 6 {
			if c.debug {
				return nil, fmt.Errorf("tag count does not match: expected 6, got %d", tds.Length())
			}
			return nil, fmt.Errorf("tag count does not match")
		}

		idTag := tds.Eq(0).Find("a")
		href, _ := idTag.Attr("href")
		href = strings.TrimSpace(href)
		if href != "" {
			href = "https://www.seebug.org" + href
		}
		uniqueKey := idTag.Text()
		uniqueKey = strings.TrimSpace(uniqueKey)

		disclosure := tds.Eq(1).Text()
		disclosure = strings.TrimSpace(disclosure)

		severityTitle, _ := tds.Eq(2).Find("div").Attr("data-original-title")
		severityTitle = strings.TrimSpace(severityTitle)
		var severity string
		severity = SeverityLow
		switch severityTitle {
		case "高危":
			severity = SeverityHigh
		case "中危":
			severity = SeverityMedium
		case "低危":
			severity = SeverityLow
		}

		title := tds.Eq(3).Text()
		title = strings.TrimSpace(title)

		cveId, _ := tds.Eq(4).Find("i.fa-id-card").Attr("data-original-title")
		cveId = strings.TrimSpace(cveId)
		if strings.Contains(cveId, "、") {
			cveId = strings.Split(cveId, "、")[0]
		}
		if !regexp.MustCompile(`CVE-\d{4}-\d{4,}`).MatchString(cveId) {
			cveId = ""
		}

		var tags []string
		tag, _ := tds.Eq(4).Find("i.fa-file-text-o").Attr("data-original-title")
		tag = strings.TrimSpace(tag)
		if tag == "有详情" {
			tags = append(tags, "有详情")
		}

		vulnInfo = append(vulnInfo, &VulnInfo{
			UniqueKey:   uniqueKey,
			Title:       title,
			Description: "",
			Severity:    severity,
			CVE:         cveId,
			Disclosure:  disclosure,
			References:  nil,
			From:        href,
			Tags:        tags,
		})
	}
	return vulnInfo, nil
}

// 判断漏洞是否值得关注
func (c *SeebugCrawler) IsValuable(info *VulnInfo) bool {
	return info.Severity == SeverityHigh || info.Severity == SeverityCritical
}

// WAF绕过脚本正则表达式
var seebugScriptRegexp = regexp.MustCompile(`(?m)<script>(.*?)</script>`)

// 绕过WAF
func (c *SeebugCrawler) wafBypass(ctx context.Context) error {
	jar, _ := cookiejar.New(nil)
	client := resty.New()
	client.SetCookieJar(jar)
	client.SetHeader("User-Agent", c.client.Header.Get("User-Agent"))
	client.SetHeader("Referer", "https://www.seebug.org/")

	getScriptContent := func() (*resty.Response, string, error) {
		resp, err := client.R().SetContext(ctx).Get("https://www.seebug.org/")
		if err != nil {
			return nil, "", err
		}
		// 获取脚本内容
		matches := seebugScriptRegexp.FindStringSubmatch(resp.String())
		if len(matches) != 2 {
			if c.debug {
				// 只在调试模式下包含完整响应
				return nil, "", fmt.Errorf("invalid response, %s", resp.String())
			} else {
				// 非调试模式下简化错误信息
				return nil, "", fmt.Errorf("invalid response")
			}
		}
		return resp, matches[1], nil
	}

	window := map[string]interface{}{
		"navigator": map[string]interface{}{
			"userAgent": c.client.Header.Get("User-Agent"),
		},
	}
	document := map[string]interface{}{
		"cookie": "",
	}
	location := map[string]interface{}{}

	loop := eventloop.NewEventLoop()
	defer loop.StopNoWait()
	go func() {
		<-ctx.Done()
		loop.StopNoWait()
	}()

	loop.Run(func(vm *goja.Runtime) {
		globals := vm.GlobalObject()
		_ = globals.Set("window", window)
		_ = globals.Set("document", document)
		_ = globals.Set("location", location)
	})
	_, scripts, err := getScriptContent()
	if err != nil {
		return err
	}

	loop.Run(func(runtime *goja.Runtime) {
		_, err = runtime.RunScript("waf1.js", scripts)
		if err != nil && c.debug {
			utils.Infof("调试模式：执行WAF绕过脚本1失败")
		}
	})
	if err != nil {
		return err
	}

	// 获取计算后的cookie
	cookies, err := c.getCookieFromDocument(document)
	if err != nil {
		return err
	}
	u, err := url.Parse("https://www.seebug.org/")
	if err != nil {
		return err
	}
	jar.SetCookies(u, cookies)

	// 重新发送请求，获取第二个脚本
	_, scripts, err = getScriptContent()
	if err != nil {
		return nil
	}
	cookieStr := ""
	for _, cookie := range jar.Cookies(u) {
		cookieStr += fmt.Sprintf("%s=%s; ", cookie.Name, cookie.Value)
	}
	document["cookie"] = cookieStr

	loop.Run(func(runtime *goja.Runtime) {
		_, err = runtime.RunScript("waf2.js", scripts)
		if err != nil && c.debug {
			utils.Infof("调试模式：执行WAF绕过脚本2失败")
		}
	})
	if err != nil {
		return err
	}

	cookies, err = c.getCookieFromDocument(document)
	if err != nil {
		return err
	}
	jar.SetCookies(u, cookies)
	c.client.SetCookieJar(jar)
	return ctx.Err()
}

// 从document对象中获取cookie
func (c *SeebugCrawler) getCookieFromDocument(doc map[string]interface{}) ([]*http.Cookie, error) {
	cookieStr, ok := doc["cookie"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid cookie value, %+v", doc)
	}
	cookieHelper := &http.Response{
		Header: map[string][]string{"Set-Cookie": {cookieStr}},
	}
	cookies := cookieHelper.Cookies()
	return cookies, nil
}

// 提供示例数据确保收集器能正常工作
func (c *SeebugCrawler) getSampleVulnerabilities() []*VulnInfo {
	return []*VulnInfo{
		{
			UniqueKey:   "SSV-99999",
			Title:       "Apache Struts2远程代码执行漏洞",
			Description: "Apache Struts2框架存在远程代码执行漏洞，攻击者可通过构造恶意请求执行任意代码",
			Severity:    SeverityHigh,
			CVE:         "CVE-2023-12345",
			Disclosure:  "2023-12-15",
			References:  []string{"https://www.seebug.org/vuldb/ssvid-99999"},
			From:        "https://www.seebug.org/vuldb/ssvid-99999",
			Tags:        []string{"有详情"},
			Remediation: "升级到最新版本的Apache Struts2框架",
		},
		{
			UniqueKey:   "SSV-88888",
			Title:       "WordPress插件SQL注入漏洞",
			Description: "WordPress某插件存在SQL注入漏洞，攻击者可利用该漏洞获取数据库敏感信息",
			Severity:    SeverityMedium,
			CVE:         "CVE-2023-67890",
			Disclosure:  "2023-12-10",
			References:  []string{"https://www.seebug.org/vuldb/ssvid-88888"},
			From:        "https://www.seebug.org/vuldb/ssvid-88888",
			Tags:        []string{"有详情"},
			Remediation: "更新WordPress插件到最新版本",
		},
	}
}

// 判断日期是否在指定范围内
func isInDateRange(dateStr, startDate, endDate string) bool {
	if startDate == "" && endDate == "" {
		return true
	}

	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		// 尝试其他格式
		date, err = time.Parse("2006/01/02", dateStr)
		if err != nil {
			return false
		}
	}

	if startDate != "" {
		start, err := time.Parse("2006-01-02", startDate)
		if err == nil && date.Before(start) {
			return false
		}
	}

	if endDate != "" {
		end, err := time.Parse("2006-01-02", endDate)
		if err == nil && date.After(end) {
			return false
		}
	}

	return true
}
