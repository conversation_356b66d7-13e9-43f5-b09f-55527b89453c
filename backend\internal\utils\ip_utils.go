package utils

import (
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	"github.com/oschwald/geoip2-golang"
)

// IPLocationInfo IP地理位置信息结构体
type IPLocationInfo struct {
	IP                string  `json:"ip"`                 // IP地址
	CountryCode       string  `json:"country_code"`       // 国家代码
	CountryNameCN     string  `json:"country_name_cn"`    // 国家名称（中文）
	CountryNameEN     string  `json:"country_name_en"`    // 国家名称（英文）
	SubdivisionCode   string  `json:"subdivision_code"`   // 省份/州代码
	SubdivisionNameCN string  `json:"subdivision_name_cn"` // 省份/州名称（中文）
	SubdivisionNameEN string  `json:"subdivision_name_en"` // 省份/州名称（英文）
	CityNameCN        string  `json:"city_name_cn"`       // 城市名称（中文）
	CityNameEN        string  `json:"city_name_en"`       // 城市名称（英文）
	Longitude         float64 `json:"longitude"`          // 经度
	Latitude          float64 `json:"latitude"`           // 纬度
	IsPrivate         bool    `json:"is_private"`         // 是否为私有IP
	Error             string  `json:"error,omitempty"`    // 错误信息
}

// BaiduIPResponse 百度IP查询API响应结构体
type BaiduIPResponse struct {
	Status string `json:"status"`
	Data   []struct {
		Location string `json:"location"`
		OrigIP   string `json:"origip"`
	} `json:"data"`
}

// MeituanIPResponse 美团IP查询API响应结构体
type MeituanIPResponse struct {
	Data struct {
		IP   string `json:"ip"`
		Rgeo struct {
			Country  string `json:"country"`
			Province string `json:"province"`
			City     string `json:"city"`
			District string `json:"district"`
		} `json:"rgeo"`
	} `json:"data"`
}

// IPSearcher IP查询器
type IPSearcher struct {
	db *geoip2.Reader
}

// NewIPSearcher 创建新的IP查询器
func NewIPSearcher(dbPath string) (*IPSearcher, error) {
	if dbPath == "" {
		// 使用默认路径
		dbPath = filepath.Join("data", "GeoLite2-City.mmdb")
	}

	db, err := geoip2.Open(dbPath)
	if err != nil {
		return nil, fmt.Errorf("打开GeoIP数据库失败: %v", err)
	}

	return &IPSearcher{db: db}, nil
}

// Close 关闭IP查询器
func (s *IPSearcher) Close() error {
	if s.db != nil {
		return s.db.Close()
	}
	return nil
}

// IsPrivateIP 判断是否为私有IP地址
func IsPrivateIP(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	// 检查IPv4私有地址范围
	if parsedIP.To4() != nil {
		// 10.0.0.0/8
		if parsedIP[12] == 10 {
			return true
		}
		// **********/12
		if parsedIP[12] == 172 && parsedIP[13] >= 16 && parsedIP[13] <= 31 {
			return true
		}
		// ***********/16
		if parsedIP[12] == 192 && parsedIP[13] == 168 {
			return true
		}
		// *********/8 (loopback)
		if parsedIP[12] == 127 {
			return true
		}
	}

	// 检查IPv6私有地址
	if parsedIP.To4() == nil {
		// ::1 (loopback)
		if parsedIP.IsLoopback() {
			return true
		}
		// fc00::/7 (unique local)
		if parsedIP[0] == 0xfc || parsedIP[0] == 0xfd {
			return true
		}
		// fe80::/10 (link local)
		if parsedIP[0] == 0xfe && (parsedIP[1]&0xc0) == 0x80 {
			return true
		}
	}

	return false
}

// SearchIP 查询IP地址归属信息
func (s *IPSearcher) SearchIP(ip string) *IPLocationInfo {
	result := &IPLocationInfo{
		IP:        ip,
		IsPrivate: IsPrivateIP(ip),
	}

	// 验证IP地址格式
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		result.Error = "无效的IP地址格式"
		return result
	}

	// 如果是私有IP，返回基本信息
	if result.IsPrivate {
		result.CountryNameCN = "内网地址"
		result.CountryNameEN = "Private Network"
		return result
	}

	// 查询GeoIP数据库
	record, err := s.db.City(parsedIP)
	if err != nil {
		result.Error = fmt.Sprintf("查询GeoIP数据库失败: %v", err)
		return result
	}

	// 填充国家信息
	result.CountryCode = record.Country.IsoCode
	if name, ok := record.Country.Names["zh-CN"]; ok {
		result.CountryNameCN = name
	}
	if name, ok := record.Country.Names["en"]; ok {
		result.CountryNameEN = name
	}

	// 填充省份/州信息
	if len(record.Subdivisions) > 0 {
		subdivision := record.Subdivisions[0]
		result.SubdivisionCode = subdivision.IsoCode
		if name, ok := subdivision.Names["zh-CN"]; ok {
			result.SubdivisionNameCN = name
		}
		if name, ok := subdivision.Names["en"]; ok {
			result.SubdivisionNameEN = name
		}
	}

	// 填充城市信息
	if name, ok := record.City.Names["zh-CN"]; ok {
		result.CityNameCN = name
	}
	if name, ok := record.City.Names["en"]; ok {
		result.CityNameEN = name
	}

	// 填充经纬度信息
	result.Longitude = float64(record.Location.Longitude)
	result.Latitude = float64(record.Location.Latitude)

	return result
}

// queryBaiduIP 查询百度IP归属API
func (s *IPSearcher) queryBaiduIP(ip string) string {
	url := fmt.Sprintf("https://opendata.baidu.com/api.php?co=&resource_id=6006&oe=utf8&query=%s", ip)

	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	resp, err := client.Get(url)
	if err != nil {
		return ""
	}
	defer resp.Body.Close()

	var baiduResp BaiduIPResponse
	if err := json.NewDecoder(resp.Body).Decode(&baiduResp); err != nil {
		return ""
	}

	if baiduResp.Status != "0" || len(baiduResp.Data) == 0 {
		return ""
	}

	location := baiduResp.Data[0].Location
	// 清理位置信息，移除多余的空格和特殊字符
	location = strings.TrimSpace(location)
	location = strings.ReplaceAll(location, "  ", " ")

	return location
}

// queryMeituanIP 查询美团IP归属API
func (s *IPSearcher) queryMeituanIP(ip string) string {
	url := fmt.Sprintf("https://apimobile.meituan.com/locate/v2/ip/loc?rgeo=true&ip=%s", ip)

	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	resp, err := client.Get(url)
	if err != nil {
		return ""
	}
	defer resp.Body.Close()

	var meituanResp MeituanIPResponse
	if err := json.NewDecoder(resp.Body).Decode(&meituanResp); err != nil {
		return ""
	}

	// 构建位置字符串
	var parts []string
	if meituanResp.Data.Rgeo.Country != "" {
		parts = append(parts, meituanResp.Data.Rgeo.Country)
	}
	if meituanResp.Data.Rgeo.Province != "" {
		parts = append(parts, meituanResp.Data.Rgeo.Province)
	}
	if meituanResp.Data.Rgeo.City != "" {
		parts = append(parts, meituanResp.Data.Rgeo.City)
	}
	if meituanResp.Data.Rgeo.District != "" {
		parts = append(parts, meituanResp.Data.Rgeo.District)
	}

	if len(parts) > 0 {
		location := strings.Join(parts, " ")
		// 清理位置信息
		location = strings.TrimSpace(location)
		location = strings.ReplaceAll(location, "  ", " ")
		return location
	}

	return ""
}

// GetSimpleLocation 获取简化的IP归属信息
func (s *IPSearcher) GetSimpleLocation(ip string) string {
	info := s.SearchIP(ip)

	if info.Error != "" {
		// GeoIP查询失败，尝试第三方API
		// 优先使用百度API
		if baiduResult := s.queryBaiduIP(ip); baiduResult != "" {
			return baiduResult
		}
		// 百度API失败，尝试美团API
		if meituanResult := s.queryMeituanIP(ip); meituanResult != "" {
			return meituanResult
		}
		return "未知"
	}

	if info.IsPrivate {
		return "内网地址"
	}

	// 如果是中国或未知国家，尝试第三方API获取更详细信息
	if info.CountryCode == "CN" || info.CountryCode == "" {
		// 优先使用百度API
		if baiduResult := s.queryBaiduIP(ip); baiduResult != "" {
			return baiduResult
		}

		// 百度API失败，尝试美团API
		if meituanResult := s.queryMeituanIP(ip); meituanResult != "" {
			return meituanResult
		}

		// 第三方API失败，使用GeoIP结果
		if info.CountryCode == "CN" {
			var parts []string

			// 添加省份信息
			if info.SubdivisionNameCN != "" {
				parts = append(parts, info.SubdivisionNameCN)
			}

			// 添加城市信息
			if info.CityNameCN != "" {
				parts = append(parts, info.CityNameCN)
			}

			// 如果有省市信息，返回组合结果
			if len(parts) > 0 {
				result := ""
				for i, part := range parts {
					if i > 0 {
						result += " "
					}
					result += part
				}
				return result
			}

			return "中国"
		}
	}

	// 其他国家，直接返回国家名称
	if info.CountryNameCN != "" {
		return info.CountryNameCN
	}

	if info.CountryNameEN != "" {
		return info.CountryNameEN
	}

	return "未知"
}

// PrintSimpleLocation 打印简化的IP归属信息
func (s *IPSearcher) PrintSimpleLocation(ip string) {
	location := s.GetSimpleLocation(ip)
	Infof("%s: %s\n", ip, location)
}

// 全局IP查询器实例
var globalIPSearcher *IPSearcher

// InitGlobalIPSearcher 初始化全局IP查询器
func InitGlobalIPSearcher() error {
	searcher, err := NewIPSearcher("")
	if err != nil {
		return fmt.Errorf("初始化IP查询器失败: %v", err)
	}
	globalIPSearcher = searcher
	return nil
}

// GetGlobalIPSearcher 获取全局IP查询器实例
func GetGlobalIPSearcher() *IPSearcher {
	return globalIPSearcher
}

// CloseGlobalIPSearcher 关闭全局IP查询器
func CloseGlobalIPSearcher() error {
	if globalIPSearcher != nil {
		return globalIPSearcher.Close()
	}
	return nil
}

// GetIPLocation 获取IP地理位置信息（便捷函数）
func GetIPLocation(ip string) string {
	if globalIPSearcher == nil {
		// 如果全局查询器未初始化，尝试初始化
		if err := InitGlobalIPSearcher(); err != nil {
			Infof("初始化IP查询器失败: %v", err)
			return "未知"
		}
	}

	return globalIPSearcher.GetSimpleLocation(ip)
}
