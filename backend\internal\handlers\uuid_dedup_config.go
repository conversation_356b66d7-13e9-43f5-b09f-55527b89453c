package handlers

import (
	"fmt"
	"runtime"
)

// UUIDDedupConfigManager UUID去重配置管理器
type UUIDDedupConfigManager struct {
	configs map[string]*UUIDDedupConfig
}

// NewUUIDDedupConfigManager 创建配置管理器
func NewUUIDDedupConfigManager() *UUIDDedupConfigManager {
	manager := &UUIDDedupConfigManager{
		configs: make(map[string]*UUIDDedupConfig),
	}
	
	// 初始化预设配置
	manager.initPresetConfigs()
	return manager
}

// initPresetConfigs 初始化预设配置
func (m *UUIDDedupConfigManager) initPresetConfigs() {
	// 获取系统CPU核心数
	cpuCount := runtime.NumCPU()
	
	// 低性能配置（适用于资源受限环境）
	m.configs["low"] = &UUIDDedupConfig{
		BatchSize:     500,
		WorkerCount:   2,
		SaveBatchSize: 500,
	}
	
	// 中等性能配置（默认配置）
	m.configs["medium"] = &UUIDDedupConfig{
		BatchSize:     1000,
		WorkerCount:   4,
		SaveBatchSize: 1000,
	}
	
	// 高性能配置（适用于高性能服务器）
	m.configs["high"] = &UUIDDedupConfig{
		BatchSize:     2000,
		WorkerCount:   cpuCount,
		SaveBatchSize: 2000,
	}
	
	// 超高性能配置（适用于大数据量处理）
	m.configs["ultra"] = &UUIDDedupConfig{
		BatchSize:     5000,
		WorkerCount:   cpuCount * 2,
		SaveBatchSize: 5000,
	}
	
	// 自动配置（根据系统资源自动调整）
	m.configs["auto"] = m.generateAutoConfig()
}

// generateAutoConfig 生成自动配置
func (m *UUIDDedupConfigManager) generateAutoConfig() *UUIDDedupConfig {
	cpuCount := runtime.NumCPU()
	
	// 根据CPU核心数调整配置
	var batchSize, workerCount, saveBatchSize int
	
	if cpuCount <= 2 {
		// 低配置系统
		batchSize = 500
		workerCount = 2
		saveBatchSize = 500
	} else if cpuCount <= 4 {
		// 中等配置系统
		batchSize = 1000
		workerCount = 4
		saveBatchSize = 1000
	} else if cpuCount <= 8 {
		// 高配置系统
		batchSize = 2000
		workerCount = cpuCount
		saveBatchSize = 2000
	} else {
		// 超高配置系统
		batchSize = 3000
		workerCount = cpuCount
		saveBatchSize = 3000
	}
	
	return &UUIDDedupConfig{
		BatchSize:     batchSize,
		WorkerCount:   workerCount,
		SaveBatchSize: saveBatchSize,
	}
}

// GetConfig 获取指定名称的配置
func (m *UUIDDedupConfigManager) GetConfig(name string) *UUIDDedupConfig {
	if config, exists := m.configs[name]; exists {
		return config
	}
	
	// 如果配置不存在，返回默认配置
	return m.configs["medium"]
}

// SetCustomConfig 设置自定义配置
func (m *UUIDDedupConfigManager) SetCustomConfig(name string, config *UUIDDedupConfig) {
	if config != nil {
		m.configs[name] = config
	}
}

// ListConfigs 列出所有可用配置
func (m *UUIDDedupConfigManager) ListConfigs() map[string]*UUIDDedupConfig {
	result := make(map[string]*UUIDDedupConfig)
	for name, config := range m.configs {
		result[name] = config
	}
	return result
}

// GetRecommendedConfig 根据数据量推荐配置
func (m *UUIDDedupConfigManager) GetRecommendedConfig(dataSize int) *UUIDDedupConfig {
	if dataSize < 1000 {
		return m.GetConfig("low")
	} else if dataSize < 10000 {
		return m.GetConfig("medium")
	} else if dataSize < 100000 {
		return m.GetConfig("high")
	} else {
		return m.GetConfig("ultra")
	}
}

// PrintConfigInfo 打印配置信息
func (m *UUIDDedupConfigManager) PrintConfigInfo(name string) {
	config := m.GetConfig(name)
	fmt.Printf("UUID去重配置 [%s]:\n", name)
	fmt.Printf("  批量查询大小: %d\n", config.BatchSize)
	fmt.Printf("  工作协程数量: %d\n", config.WorkerCount)
	fmt.Printf("  保存批次大小: %d\n", config.SaveBatchSize)
	fmt.Printf("  系统CPU核心数: %d\n", runtime.NumCPU())
}

// ValidateConfig 验证配置参数
func (m *UUIDDedupConfigManager) ValidateConfig(config *UUIDDedupConfig) error {
	if config.BatchSize <= 0 {
		return fmt.Errorf("批量查询大小必须大于0")
	}
	
	if config.WorkerCount <= 0 {
		return fmt.Errorf("工作协程数量必须大于0")
	}
	
	if config.SaveBatchSize <= 0 {
		return fmt.Errorf("保存批次大小必须大于0")
	}
	
	// 检查是否超过合理范围
	if config.BatchSize > 10000 {
		return fmt.Errorf("批量查询大小过大，建议不超过10000")
	}
	
	if config.WorkerCount > runtime.NumCPU()*4 {
		return fmt.Errorf("工作协程数量过多，建议不超过CPU核心数的4倍")
	}
	
	if config.SaveBatchSize > 10000 {
		return fmt.Errorf("保存批次大小过大，建议不超过10000")
	}
	
	return nil
}

// OptimizeConfigForDataSize 根据数据大小优化配置
func (m *UUIDDedupConfigManager) OptimizeConfigForDataSize(baseConfig *UUIDDedupConfig, dataSize int) *UUIDDedupConfig {
	optimized := &UUIDDedupConfig{
		BatchSize:     baseConfig.BatchSize,
		WorkerCount:   baseConfig.WorkerCount,
		SaveBatchSize: baseConfig.SaveBatchSize,
	}
	
	// 根据数据大小调整批次大小
	if dataSize > 50000 {
		optimized.BatchSize = minInt(optimized.BatchSize*2, 5000)
		optimized.SaveBatchSize = minInt(optimized.SaveBatchSize*2, 5000)
	} else if dataSize < 1000 {
		optimized.BatchSize = maxInt(optimized.BatchSize/2, 100)
		optimized.SaveBatchSize = maxInt(optimized.SaveBatchSize/2, 100)
		optimized.WorkerCount = maxInt(optimized.WorkerCount/2, 1)
	}
	
	return optimized
}

// minInt 返回两个整数中的较小值
func minInt(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// maxInt 返回两个整数中的较大值
func maxInt(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// 全局配置管理器实例
var GlobalUUIDDedupConfigManager = NewUUIDDedupConfigManager()
